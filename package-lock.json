{"name": "vendor-platform", "version": "0.1.41", "lockfileVersion": 3, "requires": true, "packages": {"": {"name": "vendor-platform", "version": "0.1.41", "dependencies": {"@chakra-ui/next-js": "2.2.0", "@chakra-ui/react": "2.7.0", "@emotion/react": "11.11.1", "@emotion/styled": "11.11.0", "@faker-js/faker": "^8.4.1", "@hookform/resolvers": "^3.9.1", "@radix-ui/react-accordion": "^1.2.1", "@radix-ui/react-checkbox": "^1.3.1", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-tooltip": "^1.1.4", "@react-pdf/renderer": "3.1.12", "@reduxjs/toolkit": "1.9.5", "@tanstack/react-query": "^5.74.4", "@tanstack/react-table": "^8.17.3", "@types/node": "20.2.5", "@types/react": "^18.3.3", "@types/react-dom": "18.2.4", "@types/yup": "^0.32.0", "animate.css": "4.1.1", "autoprefixer": "10.4.14", "axios": "1.4.0", "chart.js": "4.4.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "^0.2.1", "cookie": "^0.6.0", "cookies-next": "^4.2.1", "cypress": "12.14.0", "date-fns": "^2.30.0", "date-fns-tz": "2.0.0", "dayjs": "1.11.9", "eslint": "8.41.0", "eslint-config-next": "14.2.5", "form-data": "^4.0.0", "formik": "^2.4.6", "framer-motion": "10.12.16", "jspdf": "^3.0.1", "jszip": "^3.10.1", "lodash": "^4.17.21", "lucide-react": "^0.378.0", "luxon": "^3.5.0", "moment": "2.29.4", "next": "^14.2.8", "next-auth": "^4.24.5", "next-international": "^1.2.4", "next-themes": "0.3.0", "nodemailer": "^6.9.15", "numero-a-letras": "1.0.6", "nuqs": "^2.2.3", "postcss": "8.4.24", "prom-client": "^15.1.2", "react": "18.2.0", "react-big-calendar": "^1.17.1", "react-calendar": "^5.0.0", "react-chartjs-2": "5.2.0", "react-date-picker": "^11.0.0", "react-day-picker": "^8.10.1", "react-dom": "18.2.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.53.2", "react-icons": "^4.12.0", "react-redux": "8.1.1", "react-select": "5.7.3", "react-signature-canvas": "^1.0.6", "react-virtuoso": "^4.10.1", "react-window": "^1.8.10", "react-zoom-pan-pinch": "3.1.0", "recharts": "^2.13.0-alpha.4", "sonner": "^1.4.41", "sweetalert2": "11.10.0", "sweetalert2-react-content": "^5.1.0", "tailwind-merge": "^2.3.0", "tailwindcss": "3.3.2", "tailwindcss-animate": "^1.0.7", "typescript": "^5.4.5", "uuid": "^10.0.0", "xlsx": "^0.18.5", "yup": "^0.32.11", "zod": "^3.23.8", "zustand": "4.4.6"}, "devDependencies": {"@next/eslint-plugin-next": "13.4.5", "@types/luxon": "^3.4.2", "@types/nodemailer": "^6.4.15", "@types/react-big-calendar": "^1.16.1", "@types/react-signature-canvas": "^1.0.5", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "5.59.9", "@typescript-eslint/parser": "5.59.9", "encoding": "^0.1.13", "eslint-config-airbnb": "19.0.4", "eslint-config-airbnb-base": "15.0.0", "eslint-config-airbnb-typescript": "17.0.0", "eslint-config-prettier": "8.8.0", "eslint-plugin-cypress": "2.13.3", "eslint-plugin-prettier": "4.2.1", "husky": "8.0.3", "lint-staged": "13.2.2", "prettier": "2.8.8"}, "engines": {"node": ">=18.0.0"}}, "node_modules/@alloc/quick-lru": {"version": "5.2.0", "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/@babel/code-frame": {"version": "7.24.7", "license": "MIT", "dependencies": {"@babel/highlight": "^7.24.7", "picocolors": "^1.0.0"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/generator": {"version": "7.25.5", "license": "MIT", "dependencies": {"@babel/types": "^7.25.4", "@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.25", "jsesc": "^2.5.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-module-imports": {"version": "7.24.7", "license": "MIT", "dependencies": {"@babel/traverse": "^7.24.7", "@babel/types": "^7.24.7"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-string-parser": {"version": "7.24.8", "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-validator-identifier": {"version": "7.24.7", "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/highlight": {"version": "7.24.7", "license": "MIT", "dependencies": {"@babel/helper-validator-identifier": "^7.24.7", "chalk": "^2.4.2", "js-tokens": "^4.0.0", "picocolors": "^1.0.0"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/parser": {"version": "7.25.4", "license": "MIT", "dependencies": {"@babel/types": "^7.25.4"}, "bin": {"parser": "bin/babel-parser.js"}, "engines": {"node": ">=6.0.0"}}, "node_modules/@babel/runtime": {"version": "7.27.6", "resolved": "https://registry.npmjs.org/@babel/runtime/-/runtime-7.27.6.tgz", "integrity": "sha512-vbavdySgbTTrmFE+EsiqUTzlOr5bzlnJtUv9PynGCAKvfQqjIXbvFdumPM/GxMDfyuGMJaJAU6TO4zc1Jf1i8Q==", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/template": {"version": "7.25.0", "license": "MIT", "dependencies": {"@babel/code-frame": "^7.24.7", "@babel/parser": "^7.25.0", "@babel/types": "^7.25.0"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/traverse": {"version": "7.25.4", "license": "MIT", "dependencies": {"@babel/code-frame": "^7.24.7", "@babel/generator": "^7.25.4", "@babel/parser": "^7.25.4", "@babel/template": "^7.25.0", "@babel/types": "^7.25.4", "debug": "^4.3.1", "globals": "^11.1.0"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/types": {"version": "7.25.4", "license": "MIT", "dependencies": {"@babel/helper-string-parser": "^7.24.8", "@babel/helper-validator-identifier": "^7.24.7", "to-fast-properties": "^2.0.0"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@chakra-ui/accordion": {"version": "2.2.0", "resolved": "https://registry.npmjs.org/@chakra-ui/accordion/-/accordion-2.2.0.tgz", "integrity": "sha512-2IK1iLzTZ22u8GKPPPn65mqJdZidn4AvkgAbv17ISdKA07VHJ8jSd4QF1T5iCXjKfZ0XaXozmhP4kDhjwF2IbQ==", "license": "MIT", "dependencies": {"@chakra-ui/descendant": "3.0.14", "@chakra-ui/icon": "3.0.16", "@chakra-ui/react-context": "2.0.8", "@chakra-ui/react-use-controllable-state": "2.0.8", "@chakra-ui/react-use-merge-refs": "2.0.7", "@chakra-ui/shared-utils": "2.0.5", "@chakra-ui/transition": "2.0.16"}, "peerDependencies": {"@chakra-ui/system": ">=2.0.0", "framer-motion": ">=4.0.0", "react": ">=18"}}, "node_modules/@chakra-ui/alert": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/@chakra-ui/alert/-/alert-2.1.0.tgz", "integrity": "sha512-OcfHwoXI5VrmM+tHJTHT62Bx6TfyfCxSa0PWUOueJzSyhlUOKBND5we6UtrOB7D0jwX45qKKEDJOLG5yCG21jQ==", "license": "MIT", "dependencies": {"@chakra-ui/icon": "3.0.16", "@chakra-ui/react-context": "2.0.8", "@chakra-ui/shared-utils": "2.0.5", "@chakra-ui/spinner": "2.0.13"}, "peerDependencies": {"@chakra-ui/system": ">=2.0.0", "react": ">=18"}}, "node_modules/@chakra-ui/anatomy": {"version": "2.1.2", "resolved": "https://registry.npmjs.org/@chakra-ui/anatomy/-/anatomy-2.1.2.tgz", "integrity": "sha512-pKfOS/mztc4sUXHNc8ypJ1gPWSolWT770jrgVRfolVbYlki8y5Y+As996zMF6k5lewTu6j9DQequ7Cc9a69IVQ==", "license": "MIT"}, "node_modules/@chakra-ui/avatar": {"version": "2.2.11", "resolved": "https://registry.npmjs.org/@chakra-ui/avatar/-/avatar-2.2.11.tgz", "integrity": "sha512-CJFkoWvlCTDJTUBrKA/aVyG5Zz6TBEIVmmsJtqC6VcQuVDTxkWod8ruXnjb0LT2DUveL7xR5qZM9a5IXcsH3zg==", "license": "MIT", "dependencies": {"@chakra-ui/image": "2.0.16", "@chakra-ui/react-children-utils": "2.0.6", "@chakra-ui/react-context": "2.0.8", "@chakra-ui/shared-utils": "2.0.5"}, "peerDependencies": {"@chakra-ui/system": ">=2.0.0", "react": ">=18"}}, "node_modules/@chakra-ui/breadcrumb": {"version": "2.1.5", "resolved": "https://registry.npmjs.org/@chakra-ui/breadcrumb/-/breadcrumb-2.1.5.tgz", "integrity": "sha512-p3eQQrHQBkRB69xOmNyBJqEdfCrMt+e0eOH+Pm/DjFWfIVIbnIaFbmDCeWClqlLa21Ypc6h1hR9jEmvg8kmOog==", "license": "MIT", "dependencies": {"@chakra-ui/react-children-utils": "2.0.6", "@chakra-ui/react-context": "2.0.8", "@chakra-ui/shared-utils": "2.0.5"}, "peerDependencies": {"@chakra-ui/system": ">=2.0.0", "react": ">=18"}}, "node_modules/@chakra-ui/breakpoint-utils": {"version": "2.0.8", "resolved": "https://registry.npmjs.org/@chakra-ui/breakpoint-utils/-/breakpoint-utils-2.0.8.tgz", "integrity": "sha512-Pq32MlEX9fwb5j5xx8s18zJMARNHlQZH2VH1RZgfgRDpp7DcEgtRW5AInfN5CfqdHLO1dGxA7I3MqEuL5JnIsA==", "license": "MIT", "dependencies": {"@chakra-ui/shared-utils": "2.0.5"}}, "node_modules/@chakra-ui/button": {"version": "2.0.18", "resolved": "https://registry.npmjs.org/@chakra-ui/button/-/button-2.0.18.tgz", "integrity": "sha512-E3c99+lOm6ou4nQVOTLkG+IdOPMjsQK+Qe7VyP8A/xeAMFONuibrWPRPpprr4ZkB4kEoLMfNuyH2+aEza3ScUA==", "license": "MIT", "dependencies": {"@chakra-ui/react-context": "2.0.8", "@chakra-ui/react-use-merge-refs": "2.0.7", "@chakra-ui/shared-utils": "2.0.5", "@chakra-ui/spinner": "2.0.13"}, "peerDependencies": {"@chakra-ui/system": ">=2.0.0", "react": ">=18"}}, "node_modules/@chakra-ui/card": {"version": "2.1.6", "resolved": "https://registry.npmjs.org/@chakra-ui/card/-/card-2.1.6.tgz", "integrity": "sha512-fFd/WAdRNVY/WOSQv4skpy0WeVhhI0f7dTY1Sm0jVl0KLmuP/GnpsWtKtqWjNcV00K963EXDyhlk6+9oxbP4gw==", "license": "MIT", "dependencies": {"@chakra-ui/shared-utils": "2.0.5"}, "peerDependencies": {"@chakra-ui/system": ">=2.0.0", "react": ">=18"}}, "node_modules/@chakra-ui/checkbox": {"version": "2.2.15", "resolved": "https://registry.npmjs.org/@chakra-ui/checkbox/-/checkbox-2.2.15.tgz", "integrity": "sha512-Ju2yQjX8azgFa5f6VLPuwdGYobZ+rdbcYqjiks848JvPc75UsPhpS05cb4XlrKT7M16I8txDA5rPJdqqFicHCA==", "license": "MIT", "dependencies": {"@chakra-ui/form-control": "2.0.18", "@chakra-ui/react-context": "2.0.8", "@chakra-ui/react-types": "2.0.7", "@chakra-ui/react-use-callback-ref": "2.0.7", "@chakra-ui/react-use-controllable-state": "2.0.8", "@chakra-ui/react-use-merge-refs": "2.0.7", "@chakra-ui/react-use-safe-layout-effect": "2.0.5", "@chakra-ui/react-use-update-effect": "2.0.7", "@chakra-ui/shared-utils": "2.0.5", "@chakra-ui/visually-hidden": "2.0.15", "@zag-js/focus-visible": "0.2.2"}, "peerDependencies": {"@chakra-ui/system": ">=2.0.0", "react": ">=18"}}, "node_modules/@chakra-ui/clickable": {"version": "2.0.14", "resolved": "https://registry.npmjs.org/@chakra-ui/clickable/-/clickable-2.0.14.tgz", "integrity": "sha512-jfsM1qaD74ZykLHmvmsKRhDyokLUxEfL8Il1VoZMNX5RBI0xW/56vKpLTFF/v/+vLPLS+Te2cZdD4+2O+G6ulA==", "license": "MIT", "dependencies": {"@chakra-ui/react-use-merge-refs": "2.0.7", "@chakra-ui/shared-utils": "2.0.5"}, "peerDependencies": {"react": ">=18"}}, "node_modules/@chakra-ui/close-button": {"version": "2.0.17", "resolved": "https://registry.npmjs.org/@chakra-ui/close-button/-/close-button-2.0.17.tgz", "integrity": "sha512-05YPXk456t1Xa3KpqTrvm+7smx+95dmaPiwjiBN3p7LHUQVHJd8ZXSDB0V+WKi419k3cVQeJUdU/azDO2f40sw==", "license": "MIT", "dependencies": {"@chakra-ui/icon": "3.0.16"}, "peerDependencies": {"@chakra-ui/system": ">=2.0.0", "react": ">=18"}}, "node_modules/@chakra-ui/color-mode": {"version": "2.1.12", "resolved": "https://registry.npmjs.org/@chakra-ui/color-mode/-/color-mode-2.1.12.tgz", "integrity": "sha512-sYyfJGDoJSLYO+V2hxV9r033qhte5Nw/wAn5yRGGZnEEN1dKPEdWQ3XZvglWSDTNd0w9zkoH2w6vP4FBBYb/iw==", "license": "MIT", "dependencies": {"@chakra-ui/react-use-safe-layout-effect": "2.0.5"}, "peerDependencies": {"react": ">=18"}}, "node_modules/@chakra-ui/control-box": {"version": "2.0.13", "resolved": "https://registry.npmjs.org/@chakra-ui/control-box/-/control-box-2.0.13.tgz", "integrity": "sha512-FEyrU4crxati80KUF/+1Z1CU3eZK6Sa0Yv7Z/ydtz9/tvGblXW9NFanoomXAOvcIFLbaLQPPATm9Gmpr7VG05A==", "license": "MIT", "peerDependencies": {"@chakra-ui/system": ">=2.0.0", "react": ">=18"}}, "node_modules/@chakra-ui/counter": {"version": "2.0.14", "resolved": "https://registry.npmjs.org/@chakra-ui/counter/-/counter-2.0.14.tgz", "integrity": "sha512-KxcSRfUbb94dP77xTip2myoE7P2HQQN4V5fRJmNAGbzcyLciJ+aDylUU/UxgNcEjawUp6Q242NbWb1TSbKoqog==", "license": "MIT", "dependencies": {"@chakra-ui/number-utils": "2.0.7", "@chakra-ui/react-use-callback-ref": "2.0.7", "@chakra-ui/shared-utils": "2.0.5"}, "peerDependencies": {"react": ">=18"}}, "node_modules/@chakra-ui/css-reset": {"version": "2.1.2", "resolved": "https://registry.npmjs.org/@chakra-ui/css-reset/-/css-reset-2.1.2.tgz", "integrity": "sha512-4ySTLd+3iRpp4lX0yI9Yo2uQm2f+qwYGNOZF0cNcfN+4UJCd3IsaWxYRR/Anz+M51NVldZbYzC+TEYC/kpJc4A==", "license": "MIT", "peerDependencies": {"@emotion/react": ">=10.0.35", "react": ">=18"}}, "node_modules/@chakra-ui/descendant": {"version": "3.0.14", "resolved": "https://registry.npmjs.org/@chakra-ui/descendant/-/descendant-3.0.14.tgz", "integrity": "sha512-+Ahvp9H4HMpfScIv9w1vaecGz7qWAaK1YFHHolz/SIsGLaLGlbdp+5UNabQC7L6TUnzzJDQDxzwif78rTD7ang==", "license": "MIT", "dependencies": {"@chakra-ui/react-context": "2.0.8", "@chakra-ui/react-use-merge-refs": "2.0.7"}, "peerDependencies": {"react": ">=18"}}, "node_modules/@chakra-ui/dom-utils": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/@chakra-ui/dom-utils/-/dom-utils-2.1.0.tgz", "integrity": "sha512-ZmF2qRa1QZ0CMLU8M1zCfmw29DmPNtfjR9iTo74U5FPr3i1aoAh7fbJ4qAlZ197Xw9eAW28tvzQuoVWeL5C7fQ==", "license": "MIT"}, "node_modules/@chakra-ui/editable": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/@chakra-ui/editable/-/editable-3.0.0.tgz", "integrity": "sha512-q/7C/TM3iLaoQKlEiM8AY565i9NoaXtS6N6N4HWIEL5mZJPbMeHKxrCHUZlHxYuQJqFOGc09ZPD9fAFx1GkYwQ==", "license": "MIT", "dependencies": {"@chakra-ui/react-context": "2.0.8", "@chakra-ui/react-types": "2.0.7", "@chakra-ui/react-use-callback-ref": "2.0.7", "@chakra-ui/react-use-controllable-state": "2.0.8", "@chakra-ui/react-use-focus-on-pointer-down": "2.0.6", "@chakra-ui/react-use-merge-refs": "2.0.7", "@chakra-ui/react-use-safe-layout-effect": "2.0.5", "@chakra-ui/react-use-update-effect": "2.0.7", "@chakra-ui/shared-utils": "2.0.5"}, "peerDependencies": {"@chakra-ui/system": ">=2.0.0", "react": ">=18"}}, "node_modules/@chakra-ui/event-utils": {"version": "2.0.8", "resolved": "https://registry.npmjs.org/@chakra-ui/event-utils/-/event-utils-2.0.8.tgz", "integrity": "sha512-IGM/yGUHS+8TOQrZGpAKOJl/xGBrmRYJrmbHfUE7zrG3PpQyXvbLDP1M+RggkCFVgHlJi2wpYIf0QtQlU0XZfw==", "license": "MIT"}, "node_modules/@chakra-ui/focus-lock": {"version": "2.0.17", "resolved": "https://registry.npmjs.org/@chakra-ui/focus-lock/-/focus-lock-2.0.17.tgz", "integrity": "sha512-V+m4Ml9E8QY66DUpHX/imInVvz5XJ5zx59Tl0aNancXgeVY1Rt/ZdxuZdPLCAmPC/MF3GUOgnEA+WU8i+VL6Gw==", "license": "MIT", "dependencies": {"@chakra-ui/dom-utils": "2.1.0", "react-focus-lock": "^2.9.4"}, "peerDependencies": {"react": ">=18"}}, "node_modules/@chakra-ui/form-control": {"version": "2.0.18", "resolved": "https://registry.npmjs.org/@chakra-ui/form-control/-/form-control-2.0.18.tgz", "integrity": "sha512-I0a0jG01IAtRPccOXSNugyRdUAe8Dy40ctqedZvznMweOXzbMCF1m+sHPLdWeWC/VI13VoAispdPY0/zHOdjsQ==", "license": "MIT", "dependencies": {"@chakra-ui/icon": "3.0.16", "@chakra-ui/react-context": "2.0.8", "@chakra-ui/react-types": "2.0.7", "@chakra-ui/react-use-merge-refs": "2.0.7", "@chakra-ui/shared-utils": "2.0.5"}, "peerDependencies": {"@chakra-ui/system": ">=2.0.0", "react": ">=18"}}, "node_modules/@chakra-ui/hooks": {"version": "2.2.0", "resolved": "https://registry.npmjs.org/@chakra-ui/hooks/-/hooks-2.2.0.tgz", "integrity": "sha512-GZE64mcr20w+3KbCUPqQJHHmiFnX5Rcp8jS3YntGA4D5X2qU85jka7QkjfBwv/iduZ5Ei0YpCMYGCpi91dhD1Q==", "license": "MIT", "dependencies": {"@chakra-ui/react-utils": "2.0.12", "@chakra-ui/utils": "2.0.15", "compute-scroll-into-view": "1.0.20", "copy-to-clipboard": "3.3.3"}, "peerDependencies": {"react": ">=18"}}, "node_modules/@chakra-ui/icon": {"version": "3.0.16", "resolved": "https://registry.npmjs.org/@chakra-ui/icon/-/icon-3.0.16.tgz", "integrity": "sha512-RpA1X5Ptz8Mt39HSyEIW1wxAz2AXyf9H0JJ5HVx/dBdMZaGMDJ0HyyPBVci0m4RCoJuyG1HHG/DXJaVfUTVAeg==", "license": "MIT", "dependencies": {"@chakra-ui/shared-utils": "2.0.5"}, "peerDependencies": {"@chakra-ui/system": ">=2.0.0", "react": ">=18"}}, "node_modules/@chakra-ui/image": {"version": "2.0.16", "resolved": "https://registry.npmjs.org/@chakra-ui/image/-/image-2.0.16.tgz", "integrity": "sha512-iFypk1slgP3OK7VIPOtkB0UuiqVxNalgA59yoRM43xLIeZAEZpKngUVno4A2kFS61yKN0eIY4hXD3Xjm+25EJA==", "license": "MIT", "dependencies": {"@chakra-ui/react-use-safe-layout-effect": "2.0.5", "@chakra-ui/shared-utils": "2.0.5"}, "peerDependencies": {"@chakra-ui/system": ">=2.0.0", "react": ">=18"}}, "node_modules/@chakra-ui/input": {"version": "2.0.22", "resolved": "https://registry.npmjs.org/@chakra-ui/input/-/input-2.0.22.tgz", "integrity": "sha512-dCIC0/Q7mjZf17YqgoQsnXn0bus6vgriTRn8VmxOc+WcVl+KBSTBWujGrS5yu85WIFQ0aeqQvziDnDQybPqAbA==", "license": "MIT", "dependencies": {"@chakra-ui/form-control": "2.0.18", "@chakra-ui/object-utils": "2.1.0", "@chakra-ui/react-children-utils": "2.0.6", "@chakra-ui/react-context": "2.0.8", "@chakra-ui/shared-utils": "2.0.5"}, "peerDependencies": {"@chakra-ui/system": ">=2.0.0", "react": ">=18"}}, "node_modules/@chakra-ui/layout": {"version": "2.2.0", "resolved": "https://registry.npmjs.org/@chakra-ui/layout/-/layout-2.2.0.tgz", "integrity": "sha512-WvfsWQjqzbCxv7pbpPGVKxj9eQr7MC2i37ag4Wn7ClIG7uPuwHYTUWOnjnu27O3H/zA4cRVZ4Hs3GpSPbojZFQ==", "license": "MIT", "dependencies": {"@chakra-ui/breakpoint-utils": "2.0.8", "@chakra-ui/icon": "3.0.16", "@chakra-ui/object-utils": "2.1.0", "@chakra-ui/react-children-utils": "2.0.6", "@chakra-ui/react-context": "2.0.8", "@chakra-ui/shared-utils": "2.0.5"}, "peerDependencies": {"@chakra-ui/system": ">=2.0.0", "react": ">=18"}}, "node_modules/@chakra-ui/lazy-utils": {"version": "2.0.5", "resolved": "https://registry.npmjs.org/@chakra-ui/lazy-utils/-/lazy-utils-2.0.5.tgz", "integrity": "sha512-UULqw7FBvcckQk2n3iPO56TMJvDsNv0FKZI6PlUNJVaGsPbsYxK/8IQ60vZgaTVPtVcjY6BE+y6zg8u9HOqpyg==", "license": "MIT"}, "node_modules/@chakra-ui/live-region": {"version": "2.0.13", "resolved": "https://registry.npmjs.org/@chakra-ui/live-region/-/live-region-2.0.13.tgz", "integrity": "sha512-Ja+Slk6ZkxSA5oJzU2VuGU7TpZpbMb/4P4OUhIf2D30ctmIeXkxTWw1Bs1nGJAVtAPcGS5sKA+zb89i8g+0cTQ==", "license": "MIT", "peerDependencies": {"react": ">=18"}}, "node_modules/@chakra-ui/media-query": {"version": "3.2.12", "resolved": "https://registry.npmjs.org/@chakra-ui/media-query/-/media-query-3.2.12.tgz", "integrity": "sha512-8pSLDf3oxxhFrhd40rs7vSeIBfvOmIKHA7DJlGUC/y+9irD24ZwgmCtFnn+y3gI47hTJsopbSX+wb8nr7XPswA==", "license": "MIT", "dependencies": {"@chakra-ui/breakpoint-utils": "2.0.8", "@chakra-ui/react-env": "3.0.0", "@chakra-ui/shared-utils": "2.0.5"}, "peerDependencies": {"@chakra-ui/system": ">=2.0.0", "react": ">=18"}}, "node_modules/@chakra-ui/menu": {"version": "2.1.15", "resolved": "https://registry.npmjs.org/@chakra-ui/menu/-/menu-2.1.15.tgz", "integrity": "sha512-+1fh7KBKZyhy8wi7Q6nQAzrvjM6xggyhGMnSna0rt6FJVA2jlfkjb5FozyIVPnkfJKjkKd8THVhrs9E7pHNV/w==", "license": "MIT", "dependencies": {"@chakra-ui/clickable": "2.0.14", "@chakra-ui/descendant": "3.0.14", "@chakra-ui/lazy-utils": "2.0.5", "@chakra-ui/popper": "3.0.14", "@chakra-ui/react-children-utils": "2.0.6", "@chakra-ui/react-context": "2.0.8", "@chakra-ui/react-use-animation-state": "2.0.9", "@chakra-ui/react-use-controllable-state": "2.0.8", "@chakra-ui/react-use-disclosure": "2.0.8", "@chakra-ui/react-use-focus-effect": "2.0.11", "@chakra-ui/react-use-merge-refs": "2.0.7", "@chakra-ui/react-use-outside-click": "2.1.0", "@chakra-ui/react-use-update-effect": "2.0.7", "@chakra-ui/shared-utils": "2.0.5", "@chakra-ui/transition": "2.0.16"}, "peerDependencies": {"@chakra-ui/system": ">=2.0.0", "framer-motion": ">=4.0.0", "react": ">=18"}}, "node_modules/@chakra-ui/modal": {"version": "2.2.12", "resolved": "https://registry.npmjs.org/@chakra-ui/modal/-/modal-2.2.12.tgz", "integrity": "sha512-F1nNmYGvyqlmxidbwaBM3y57NhZ/Qeyc8BE9tb1FL1v9nxQhkfrPvMQ9miK0O1syPN6aZ5MMj+uD3AsRFE+/tA==", "license": "MIT", "dependencies": {"@chakra-ui/close-button": "2.0.17", "@chakra-ui/focus-lock": "2.0.17", "@chakra-ui/portal": "2.0.16", "@chakra-ui/react-context": "2.0.8", "@chakra-ui/react-types": "2.0.7", "@chakra-ui/react-use-merge-refs": "2.0.7", "@chakra-ui/shared-utils": "2.0.5", "@chakra-ui/transition": "2.0.16", "aria-hidden": "^1.2.2", "react-remove-scroll": "^2.5.5"}, "peerDependencies": {"@chakra-ui/system": ">=2.0.0", "framer-motion": ">=4.0.0", "react": ">=18", "react-dom": ">=18"}}, "node_modules/@chakra-ui/next-js": {"version": "2.2.0", "license": "MIT", "dependencies": {"@emotion/cache": "^11.11.0"}, "peerDependencies": {"@chakra-ui/react": ">=2.4.0", "@emotion/react": ">=11", "next": ">=13", "react": ">=18"}}, "node_modules/@chakra-ui/number-input": {"version": "2.0.19", "resolved": "https://registry.npmjs.org/@chakra-ui/number-input/-/number-input-2.0.19.tgz", "integrity": "sha512-HDaITvtMEqOauOrCPsARDxKD9PSHmhWywpcyCSOX0lMe4xx2aaGhU0QQFhsJsykj8Er6pytMv6t0KZksdDv3YA==", "license": "MIT", "dependencies": {"@chakra-ui/counter": "2.0.14", "@chakra-ui/form-control": "2.0.18", "@chakra-ui/icon": "3.0.16", "@chakra-ui/react-context": "2.0.8", "@chakra-ui/react-types": "2.0.7", "@chakra-ui/react-use-callback-ref": "2.0.7", "@chakra-ui/react-use-event-listener": "2.0.7", "@chakra-ui/react-use-interval": "2.0.5", "@chakra-ui/react-use-merge-refs": "2.0.7", "@chakra-ui/react-use-safe-layout-effect": "2.0.5", "@chakra-ui/react-use-update-effect": "2.0.7", "@chakra-ui/shared-utils": "2.0.5"}, "peerDependencies": {"@chakra-ui/system": ">=2.0.0", "react": ">=18"}}, "node_modules/@chakra-ui/number-utils": {"version": "2.0.7", "resolved": "https://registry.npmjs.org/@chakra-ui/number-utils/-/number-utils-2.0.7.tgz", "integrity": "sha512-yOGxBjXNvLTBvQyhMDqGU0Oj26s91mbAlqKHiuw737AXHt0aPllOthVUqQMeaYLwLCjGMg0jtI7JReRzyi94Dg==", "license": "MIT"}, "node_modules/@chakra-ui/object-utils": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/@chakra-ui/object-utils/-/object-utils-2.1.0.tgz", "integrity": "sha512-tgIZOgLHaoti5PYGPTwK3t/cqtcycW0owaiOXoZOcpwwX/vlVb+H1jFsQyWiiwQVPt9RkoSLtxzXamx+aHH+bQ==", "license": "MIT"}, "node_modules/@chakra-ui/pin-input": {"version": "2.0.20", "resolved": "https://registry.npmjs.org/@chakra-ui/pin-input/-/pin-input-2.0.20.tgz", "integrity": "sha512-IHVmerrtHN8F+jRB3W1HnMir1S1TUCWhI7qDInxqPtoRffHt6mzZgLZ0izx8p1fD4HkW4c1d4/ZLEz9uH9bBRg==", "license": "MIT", "dependencies": {"@chakra-ui/descendant": "3.0.14", "@chakra-ui/react-children-utils": "2.0.6", "@chakra-ui/react-context": "2.0.8", "@chakra-ui/react-use-controllable-state": "2.0.8", "@chakra-ui/react-use-merge-refs": "2.0.7", "@chakra-ui/shared-utils": "2.0.5"}, "peerDependencies": {"@chakra-ui/system": ">=2.0.0", "react": ">=18"}}, "node_modules/@chakra-ui/popover": {"version": "2.1.12", "resolved": "https://registry.npmjs.org/@chakra-ui/popover/-/popover-2.1.12.tgz", "integrity": "sha512-Corh8trA1f3ydcMQqomgSvYNNhAlpxiBpMY2sglwYazOJcueHA8CI05cJVD0T/wwoTob7BShabhCGFZThn61Ng==", "license": "MIT", "dependencies": {"@chakra-ui/close-button": "2.0.17", "@chakra-ui/lazy-utils": "2.0.5", "@chakra-ui/popper": "3.0.14", "@chakra-ui/react-context": "2.0.8", "@chakra-ui/react-types": "2.0.7", "@chakra-ui/react-use-animation-state": "2.0.9", "@chakra-ui/react-use-disclosure": "2.0.8", "@chakra-ui/react-use-focus-effect": "2.0.11", "@chakra-ui/react-use-focus-on-pointer-down": "2.0.6", "@chakra-ui/react-use-merge-refs": "2.0.7", "@chakra-ui/shared-utils": "2.0.5"}, "peerDependencies": {"@chakra-ui/system": ">=2.0.0", "framer-motion": ">=4.0.0", "react": ">=18"}}, "node_modules/@chakra-ui/popper": {"version": "3.0.14", "resolved": "https://registry.npmjs.org/@chakra-ui/popper/-/popper-3.0.14.tgz", "integrity": "sha512-RDMmmSfjsmHJbVn2agDyoJpTbQK33fxx//njwJdeyM0zTG/3/4xjI/Cxru3acJ2Y+1jFGmPqhO81stFjnbtfIw==", "license": "MIT", "dependencies": {"@chakra-ui/react-types": "2.0.7", "@chakra-ui/react-use-merge-refs": "2.0.7", "@popperjs/core": "^2.9.3"}, "peerDependencies": {"react": ">=18"}}, "node_modules/@chakra-ui/portal": {"version": "2.0.16", "resolved": "https://registry.npmjs.org/@chakra-ui/portal/-/portal-2.0.16.tgz", "integrity": "sha512-bVID0qbQ0l4xq38LdqAN4EKD4/uFkDnXzFwOlviC9sl0dNhzICDb1ltuH/Adl1d2HTMqyN60O3GO58eHy7plnQ==", "license": "MIT", "dependencies": {"@chakra-ui/react-context": "2.0.8", "@chakra-ui/react-use-safe-layout-effect": "2.0.5"}, "peerDependencies": {"react": ">=18", "react-dom": ">=18"}}, "node_modules/@chakra-ui/progress": {"version": "2.1.6", "resolved": "https://registry.npmjs.org/@chakra-ui/progress/-/progress-2.1.6.tgz", "integrity": "sha512-hHh5Ysv4z6bK+j2GJbi/FT9CVyto2PtNUNwBmr3oNMVsoOUMoRjczfXvvYqp0EHr9PCpxqrq7sRwgQXUzhbDSw==", "license": "MIT", "dependencies": {"@chakra-ui/react-context": "2.0.8"}, "peerDependencies": {"@chakra-ui/system": ">=2.0.0", "react": ">=18"}}, "node_modules/@chakra-ui/provider": {"version": "2.3.0", "resolved": "https://registry.npmjs.org/@chakra-ui/provider/-/provider-2.3.0.tgz", "integrity": "sha512-vKgmjoLVS3NnHW8RSYwmhhda2ZTi3fQc1egkYSVwngGky4CsN15I+XDhxJitVd66H41cjah/UNJyoeq7ACseLA==", "license": "MIT", "dependencies": {"@chakra-ui/css-reset": "2.1.2", "@chakra-ui/portal": "2.0.16", "@chakra-ui/react-env": "3.0.0", "@chakra-ui/system": "2.5.8", "@chakra-ui/utils": "2.0.15"}, "peerDependencies": {"@emotion/react": "^11.0.0", "@emotion/styled": "^11.0.0", "react": ">=18", "react-dom": ">=18"}}, "node_modules/@chakra-ui/radio": {"version": "2.0.22", "resolved": "https://registry.npmjs.org/@chakra-ui/radio/-/radio-2.0.22.tgz", "integrity": "sha512-GsQ5WAnLwivWl6gPk8P1x+tCcpVakCt5R5T0HumF7DGPXKdJbjS+RaFySrbETmyTJsKY4QrfXn+g8CWVrMjPjw==", "license": "MIT", "dependencies": {"@chakra-ui/form-control": "2.0.18", "@chakra-ui/react-context": "2.0.8", "@chakra-ui/react-types": "2.0.7", "@chakra-ui/react-use-merge-refs": "2.0.7", "@chakra-ui/shared-utils": "2.0.5", "@zag-js/focus-visible": "0.2.2"}, "peerDependencies": {"@chakra-ui/system": ">=2.0.0", "react": ">=18"}}, "node_modules/@chakra-ui/react": {"version": "2.7.0", "resolved": "https://registry.npmjs.org/@chakra-ui/react/-/react-2.7.0.tgz", "integrity": "sha512-+FcUFQMsPfhWuM9Iu7uqufwwhmHN2IX6FWsBixYGOalO86dpgETsILMZP9PuWfgj7GpWiy2Dum6HXekh0Tk2Mg==", "license": "MIT", "dependencies": {"@chakra-ui/accordion": "2.2.0", "@chakra-ui/alert": "2.1.0", "@chakra-ui/avatar": "2.2.11", "@chakra-ui/breadcrumb": "2.1.5", "@chakra-ui/button": "2.0.18", "@chakra-ui/card": "2.1.6", "@chakra-ui/checkbox": "2.2.15", "@chakra-ui/close-button": "2.0.17", "@chakra-ui/control-box": "2.0.13", "@chakra-ui/counter": "2.0.14", "@chakra-ui/css-reset": "2.1.2", "@chakra-ui/editable": "3.0.0", "@chakra-ui/focus-lock": "2.0.17", "@chakra-ui/form-control": "2.0.18", "@chakra-ui/hooks": "2.2.0", "@chakra-ui/icon": "3.0.16", "@chakra-ui/image": "2.0.16", "@chakra-ui/input": "2.0.22", "@chakra-ui/layout": "2.2.0", "@chakra-ui/live-region": "2.0.13", "@chakra-ui/media-query": "3.2.12", "@chakra-ui/menu": "2.1.15", "@chakra-ui/modal": "2.2.12", "@chakra-ui/number-input": "2.0.19", "@chakra-ui/pin-input": "2.0.20", "@chakra-ui/popover": "2.1.12", "@chakra-ui/popper": "3.0.14", "@chakra-ui/portal": "2.0.16", "@chakra-ui/progress": "2.1.6", "@chakra-ui/provider": "2.3.0", "@chakra-ui/radio": "2.0.22", "@chakra-ui/react-env": "3.0.0", "@chakra-ui/select": "2.0.19", "@chakra-ui/skeleton": "2.0.24", "@chakra-ui/slider": "2.0.25", "@chakra-ui/spinner": "2.0.13", "@chakra-ui/stat": "2.0.18", "@chakra-ui/stepper": "2.2.0", "@chakra-ui/styled-system": "2.9.1", "@chakra-ui/switch": "2.0.27", "@chakra-ui/system": "2.5.8", "@chakra-ui/table": "2.0.17", "@chakra-ui/tabs": "2.1.9", "@chakra-ui/tag": "3.0.0", "@chakra-ui/textarea": "2.0.19", "@chakra-ui/theme": "3.1.2", "@chakra-ui/theme-utils": "2.0.18", "@chakra-ui/toast": "6.1.4", "@chakra-ui/tooltip": "2.2.9", "@chakra-ui/transition": "2.0.16", "@chakra-ui/utils": "2.0.15", "@chakra-ui/visually-hidden": "2.0.15"}, "peerDependencies": {"@emotion/react": "^11.0.0", "@emotion/styled": "^11.0.0", "framer-motion": ">=4.0.0", "react": ">=18", "react-dom": ">=18"}}, "node_modules/@chakra-ui/react-children-utils": {"version": "2.0.6", "resolved": "https://registry.npmjs.org/@chakra-ui/react-children-utils/-/react-children-utils-2.0.6.tgz", "integrity": "sha512-QVR2RC7QsOsbWwEnq9YduhpqSFnZGvjjGREV8ygKi8ADhXh93C8azLECCUVgRJF2Wc+So1fgxmjLcbZfY2VmBA==", "license": "MIT", "peerDependencies": {"react": ">=18"}}, "node_modules/@chakra-ui/react-context": {"version": "2.0.8", "resolved": "https://registry.npmjs.org/@chakra-ui/react-context/-/react-context-2.0.8.tgz", "integrity": "sha512-tRTKdn6lCTXM6WPjSokAAKCw2ioih7Eg8cNgaYRSwKBck8nkz9YqxgIIEj3dJD7MGtpl24S/SNI98iRWkRwR/A==", "license": "MIT", "peerDependencies": {"react": ">=18"}}, "node_modules/@chakra-ui/react-env": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/@chakra-ui/react-env/-/react-env-3.0.0.tgz", "integrity": "sha512-tfMRO2v508HQWAqSADFrwZgR9oU10qC97oV6zGbjHh9ALP0/IcFR+Bi71KRTveDTm85fMeAzZYGj57P3Dsipkw==", "license": "MIT", "dependencies": {"@chakra-ui/react-use-safe-layout-effect": "2.0.5"}, "peerDependencies": {"react": ">=18"}}, "node_modules/@chakra-ui/react-types": {"version": "2.0.7", "resolved": "https://registry.npmjs.org/@chakra-ui/react-types/-/react-types-2.0.7.tgz", "integrity": "sha512-12zv2qIZ8EHwiytggtGvo4iLT0APris7T0qaAWqzpUGS0cdUtR8W+V1BJ5Ocq+7tA6dzQ/7+w5hmXih61TuhWQ==", "license": "MIT", "peerDependencies": {"react": ">=18"}}, "node_modules/@chakra-ui/react-use-animation-state": {"version": "2.0.9", "resolved": "https://registry.npmjs.org/@chakra-ui/react-use-animation-state/-/react-use-animation-state-2.0.9.tgz", "integrity": "sha512-WFoD5OG03PBmzJCoRwM8rVfU442AvKBPPgA0yGGlKioH29OGuX7W78Ml+cYdXxonTiB03YSRZzUwaUnP4wAy1Q==", "license": "MIT", "dependencies": {"@chakra-ui/dom-utils": "2.1.0", "@chakra-ui/react-use-event-listener": "2.0.7"}, "peerDependencies": {"react": ">=18"}}, "node_modules/@chakra-ui/react-use-callback-ref": {"version": "2.0.7", "resolved": "https://registry.npmjs.org/@chakra-ui/react-use-callback-ref/-/react-use-callback-ref-2.0.7.tgz", "integrity": "sha512-YjT76nTpfHAK5NxplAlZsQwNju5KmQExnqsWNPFeOR6vvbC34+iPSTr+r91i1Hdy7gBSbevsOsd5Wm6RN3GuMw==", "license": "MIT", "peerDependencies": {"react": ">=18"}}, "node_modules/@chakra-ui/react-use-controllable-state": {"version": "2.0.8", "resolved": "https://registry.npmjs.org/@chakra-ui/react-use-controllable-state/-/react-use-controllable-state-2.0.8.tgz", "integrity": "sha512-F7rdCbLEmRjwwODqWZ3y+mKgSSHPcLQxeUygwk1BkZPXbKkJJKymOIjIynil2cbH7ku3hcSIWRvuhpCcfQWJ7Q==", "license": "MIT", "dependencies": {"@chakra-ui/react-use-callback-ref": "2.0.7"}, "peerDependencies": {"react": ">=18"}}, "node_modules/@chakra-ui/react-use-disclosure": {"version": "2.0.8", "resolved": "https://registry.npmjs.org/@chakra-ui/react-use-disclosure/-/react-use-disclosure-2.0.8.tgz", "integrity": "sha512-2ir/mHe1YND40e+FyLHnDsnDsBQPwzKDLzfe9GZri7y31oU83JSbHdlAXAhp3bpjohslwavtRCp+S/zRxfO9aQ==", "license": "MIT", "dependencies": {"@chakra-ui/react-use-callback-ref": "2.0.7"}, "peerDependencies": {"react": ">=18"}}, "node_modules/@chakra-ui/react-use-event-listener": {"version": "2.0.7", "resolved": "https://registry.npmjs.org/@chakra-ui/react-use-event-listener/-/react-use-event-listener-2.0.7.tgz", "integrity": "sha512-4wvpx4yudIO3B31pOrXuTHDErawmwiXnvAN7gLEOVREi16+YGNcFnRJ5X5nRrmB7j2MDUtsEDpRBFfw5Z9xQ5g==", "license": "MIT", "dependencies": {"@chakra-ui/react-use-callback-ref": "2.0.7"}, "peerDependencies": {"react": ">=18"}}, "node_modules/@chakra-ui/react-use-focus-effect": {"version": "2.0.11", "resolved": "https://registry.npmjs.org/@chakra-ui/react-use-focus-effect/-/react-use-focus-effect-2.0.11.tgz", "integrity": "sha512-/zadgjaCWD50TfuYsO1vDS2zSBs2p/l8P2DPEIA8FuaowbBubKrk9shKQDWmbfDU7KArGxPxrvo+VXvskPPjHw==", "license": "MIT", "dependencies": {"@chakra-ui/dom-utils": "2.1.0", "@chakra-ui/react-use-event-listener": "2.0.7", "@chakra-ui/react-use-safe-layout-effect": "2.0.5", "@chakra-ui/react-use-update-effect": "2.0.7"}, "peerDependencies": {"react": ">=18"}}, "node_modules/@chakra-ui/react-use-focus-on-pointer-down": {"version": "2.0.6", "resolved": "https://registry.npmjs.org/@chakra-ui/react-use-focus-on-pointer-down/-/react-use-focus-on-pointer-down-2.0.6.tgz", "integrity": "sha512-OigXiLRVySn3tyVqJ/rn57WGuukW8TQe8fJYiLwXbcNyAMuYYounvRxvCy2b53sQ7QIZamza0N0jhirbH5FNoQ==", "license": "MIT", "dependencies": {"@chakra-ui/react-use-event-listener": "2.0.7"}, "peerDependencies": {"react": ">=18"}}, "node_modules/@chakra-ui/react-use-interval": {"version": "2.0.5", "resolved": "https://registry.npmjs.org/@chakra-ui/react-use-interval/-/react-use-interval-2.0.5.tgz", "integrity": "sha512-1nbdwMi2K87V6p5f5AseOKif2CkldLaJlq1TOqaPRwb7v3aU9rltBtYdf+fIyuHSToNJUV6wd9budCFdLCl3Fg==", "license": "MIT", "dependencies": {"@chakra-ui/react-use-callback-ref": "2.0.7"}, "peerDependencies": {"react": ">=18"}}, "node_modules/@chakra-ui/react-use-latest-ref": {"version": "2.0.5", "resolved": "https://registry.npmjs.org/@chakra-ui/react-use-latest-ref/-/react-use-latest-ref-2.0.5.tgz", "integrity": "sha512-3mIuFzMyIo3Ok/D8uhV9voVg7KkrYVO/pwVvNPJOHsDQqCA6DpYE4WDsrIx+fVcwad3Ta7SupexR5PoI+kq6QQ==", "license": "MIT", "peerDependencies": {"react": ">=18"}}, "node_modules/@chakra-ui/react-use-merge-refs": {"version": "2.0.7", "resolved": "https://registry.npmjs.org/@chakra-ui/react-use-merge-refs/-/react-use-merge-refs-2.0.7.tgz", "integrity": "sha512-zds4Uhsc+AMzdH8JDDkLVet9baUBgtOjPbhC5r3A0ZXjZvGhCztFAVE3aExYiVoMPoHLKbLcqvCWE6ioFKz1lw==", "license": "MIT", "peerDependencies": {"react": ">=18"}}, "node_modules/@chakra-ui/react-use-outside-click": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/@chakra-ui/react-use-outside-click/-/react-use-outside-click-2.1.0.tgz", "integrity": "sha512-JanCo4QtWvMl9ZZUpKJKV62RlMWDFdPCE0Q64a7eWTOQgWWcpyBW7TOYRunQTqrK30FqkYFJCOlAWOtn+6Rw7A==", "license": "MIT", "dependencies": {"@chakra-ui/react-use-callback-ref": "2.0.7"}, "peerDependencies": {"react": ">=18"}}, "node_modules/@chakra-ui/react-use-pan-event": {"version": "2.0.9", "resolved": "https://registry.npmjs.org/@chakra-ui/react-use-pan-event/-/react-use-pan-event-2.0.9.tgz", "integrity": "sha512-xu35QXkiyrgsHUOnctl+SwNcwf9Rl62uYE5y8soKOZdBm8E+FvZIt2hxUzK1EoekbJCMzEZ0Yv1ZQCssVkSLaQ==", "license": "MIT", "dependencies": {"@chakra-ui/event-utils": "2.0.8", "@chakra-ui/react-use-latest-ref": "2.0.5", "framesync": "6.1.2"}, "peerDependencies": {"react": ">=18"}}, "node_modules/@chakra-ui/react-use-previous": {"version": "2.0.5", "resolved": "https://registry.npmjs.org/@chakra-ui/react-use-previous/-/react-use-previous-2.0.5.tgz", "integrity": "sha512-BIZgjycPE4Xr+MkhKe0h67uHXzQQkBX/u5rYPd65iMGdX1bCkbE0oorZNfOHLKdTmnEb4oVsNvfN6Rfr+Mnbxw==", "license": "MIT", "peerDependencies": {"react": ">=18"}}, "node_modules/@chakra-ui/react-use-safe-layout-effect": {"version": "2.0.5", "resolved": "https://registry.npmjs.org/@chakra-ui/react-use-safe-layout-effect/-/react-use-safe-layout-effect-2.0.5.tgz", "integrity": "sha512-MwAQBz3VxoeFLaesaSEN87reVNVbjcQBDex2WGexAg6hUB6n4gc1OWYH/iXp4tzp4kuggBNhEHkk9BMYXWfhJQ==", "license": "MIT", "peerDependencies": {"react": ">=18"}}, "node_modules/@chakra-ui/react-use-size": {"version": "2.0.10", "resolved": "https://registry.npmjs.org/@chakra-ui/react-use-size/-/react-use-size-2.0.10.tgz", "integrity": "sha512-fdIkH14GDnKQrtQfxX8N3gxbXRPXEl67Y3zeD9z4bKKcQUAYIMqs0MsPZY+FMpGQw8QqafM44nXfL038aIrC5w==", "license": "MIT", "dependencies": {"@zag-js/element-size": "0.3.2"}, "peerDependencies": {"react": ">=18"}}, "node_modules/@chakra-ui/react-use-timeout": {"version": "2.0.5", "resolved": "https://registry.npmjs.org/@chakra-ui/react-use-timeout/-/react-use-timeout-2.0.5.tgz", "integrity": "sha512-QqmB+jVphh3h/CS60PieorpY7UqSPkrQCB7f7F+i9vwwIjtP8fxVHMmkb64K7VlzQiMPzv12nlID5dqkzlv0mw==", "license": "MIT", "dependencies": {"@chakra-ui/react-use-callback-ref": "2.0.7"}, "peerDependencies": {"react": ">=18"}}, "node_modules/@chakra-ui/react-use-update-effect": {"version": "2.0.7", "resolved": "https://registry.npmjs.org/@chakra-ui/react-use-update-effect/-/react-use-update-effect-2.0.7.tgz", "integrity": "sha512-vBM2bmmM83ZdDtasWv3PXPznpTUd+FvqBC8J8rxoRmvdMEfrxTiQRBJhiGHLpS9BPLLPQlosN6KdFU97csB6zg==", "license": "MIT", "peerDependencies": {"react": ">=18"}}, "node_modules/@chakra-ui/react-utils": {"version": "2.0.12", "resolved": "https://registry.npmjs.org/@chakra-ui/react-utils/-/react-utils-2.0.12.tgz", "integrity": "sha512-GbSfVb283+YA3kA8w8xWmzbjNWk14uhNpntnipHCftBibl0lxtQ9YqMFQLwuFOO0U2gYVocszqqDWX+XNKq9hw==", "license": "MIT", "dependencies": {"@chakra-ui/utils": "2.0.15"}, "peerDependencies": {"react": ">=18"}}, "node_modules/@chakra-ui/select": {"version": "2.0.19", "resolved": "https://registry.npmjs.org/@chakra-ui/select/-/select-2.0.19.tgz", "integrity": "sha512-eAlFh+JhwtJ17OrB6fO6gEAGOMH18ERNrXLqWbYLrs674Le7xuREgtuAYDoxUzvYXYYTTdOJtVbcHGriI3o6rA==", "license": "MIT", "dependencies": {"@chakra-ui/form-control": "2.0.18", "@chakra-ui/shared-utils": "2.0.5"}, "peerDependencies": {"@chakra-ui/system": ">=2.0.0", "react": ">=18"}}, "node_modules/@chakra-ui/shared-utils": {"version": "2.0.5", "resolved": "https://registry.npmjs.org/@chakra-ui/shared-utils/-/shared-utils-2.0.5.tgz", "integrity": "sha512-4/Wur0FqDov7Y0nCXl7HbHzCg4aq86h+SXdoUeuCMD3dSj7dpsVnStLYhng1vxvlbUnLpdF4oz5Myt3i/a7N3Q==", "license": "MIT"}, "node_modules/@chakra-ui/skeleton": {"version": "2.0.24", "resolved": "https://registry.npmjs.org/@chakra-ui/skeleton/-/skeleton-2.0.24.tgz", "integrity": "sha512-1jXtVKcl/jpbrJlc/TyMsFyI651GTXY5ma30kWyTXoby2E+cxbV6OR8GB/NMZdGxbQBax8/VdtYVjI0n+OBqWA==", "license": "MIT", "dependencies": {"@chakra-ui/media-query": "3.2.12", "@chakra-ui/react-use-previous": "2.0.5", "@chakra-ui/shared-utils": "2.0.5"}, "peerDependencies": {"@chakra-ui/system": ">=2.0.0", "react": ">=18"}}, "node_modules/@chakra-ui/slider": {"version": "2.0.25", "resolved": "https://registry.npmjs.org/@chakra-ui/slider/-/slider-2.0.25.tgz", "integrity": "sha512-FnWSi0AIXP+9sHMCPboOKGqm902k8dJtsJ7tu3D0AcKkE62WtYLZ2sTqvwJxCfSl4KqVI1i571SrF9WadnnJ8w==", "license": "MIT", "dependencies": {"@chakra-ui/number-utils": "2.0.7", "@chakra-ui/react-context": "2.0.8", "@chakra-ui/react-types": "2.0.7", "@chakra-ui/react-use-callback-ref": "2.0.7", "@chakra-ui/react-use-controllable-state": "2.0.8", "@chakra-ui/react-use-latest-ref": "2.0.5", "@chakra-ui/react-use-merge-refs": "2.0.7", "@chakra-ui/react-use-pan-event": "2.0.9", "@chakra-ui/react-use-size": "2.0.10", "@chakra-ui/react-use-update-effect": "2.0.7"}, "peerDependencies": {"@chakra-ui/system": ">=2.0.0", "react": ">=18"}}, "node_modules/@chakra-ui/spinner": {"version": "2.0.13", "resolved": "https://registry.npmjs.org/@chakra-ui/spinner/-/spinner-2.0.13.tgz", "integrity": "sha512-T1/aSkVpUIuiYyrjfn1+LsQEG7Onbi1UE9ccS/evgf61Dzy4GgTXQUnDuWFSgpV58owqirqOu6jn/9eCwDlzlg==", "license": "MIT", "dependencies": {"@chakra-ui/shared-utils": "2.0.5"}, "peerDependencies": {"@chakra-ui/system": ">=2.0.0", "react": ">=18"}}, "node_modules/@chakra-ui/stat": {"version": "2.0.18", "resolved": "https://registry.npmjs.org/@chakra-ui/stat/-/stat-2.0.18.tgz", "integrity": "sha512-wKyfBqhVlIs9bkSerUc6F9KJMw0yTIEKArW7dejWwzToCLPr47u+CtYO6jlJHV6lRvkhi4K4Qc6pyvtJxZ3VpA==", "license": "MIT", "dependencies": {"@chakra-ui/icon": "3.0.16", "@chakra-ui/react-context": "2.0.8", "@chakra-ui/shared-utils": "2.0.5"}, "peerDependencies": {"@chakra-ui/system": ">=2.0.0", "react": ">=18"}}, "node_modules/@chakra-ui/stepper": {"version": "2.2.0", "resolved": "https://registry.npmjs.org/@chakra-ui/stepper/-/stepper-2.2.0.tgz", "integrity": "sha512-8ZLxV39oghSVtOUGK8dX8Z6sWVSQiKVmsK4c3OQDa8y2TvxP0VtFD0Z5U1xJlOjQMryZRWhGj9JBc3iQLukuGg==", "license": "MIT", "dependencies": {"@chakra-ui/icon": "3.0.16", "@chakra-ui/react-context": "2.0.8", "@chakra-ui/shared-utils": "2.0.5"}, "peerDependencies": {"@chakra-ui/system": ">=2.0.0", "react": ">=18"}}, "node_modules/@chakra-ui/styled-system": {"version": "2.9.1", "resolved": "https://registry.npmjs.org/@chakra-ui/styled-system/-/styled-system-2.9.1.tgz", "integrity": "sha512-jhYKBLxwOPi9/bQt9kqV3ELa/4CjmNNruTyXlPp5M0v0+pDMUngPp48mVLoskm9RKZGE0h1qpvj/jZ3K7c7t8w==", "license": "MIT", "dependencies": {"@chakra-ui/shared-utils": "2.0.5", "csstype": "^3.0.11", "lodash.mergewith": "4.6.2"}}, "node_modules/@chakra-ui/switch": {"version": "2.0.27", "resolved": "https://registry.npmjs.org/@chakra-ui/switch/-/switch-2.0.27.tgz", "integrity": "sha512-z76y2fxwMlvRBrC5W8xsZvo3gP+zAEbT3Nqy5P8uh/IPd5OvDsGeac90t5cgnQTyxMOpznUNNK+1eUZqtLxWnQ==", "license": "MIT", "dependencies": {"@chakra-ui/checkbox": "2.2.15", "@chakra-ui/shared-utils": "2.0.5"}, "peerDependencies": {"@chakra-ui/system": ">=2.0.0", "framer-motion": ">=4.0.0", "react": ">=18"}}, "node_modules/@chakra-ui/system": {"version": "2.5.8", "resolved": "https://registry.npmjs.org/@chakra-ui/system/-/system-2.5.8.tgz", "integrity": "sha512-Vy8UUaCxikOzOGE54IP8tKouvU38rEYU1HCSquU9+oe7Jd70HaiLa4vmUKvHyMUmxkOzDHIkgZLbVQCubSnN5w==", "license": "MIT", "dependencies": {"@chakra-ui/color-mode": "2.1.12", "@chakra-ui/object-utils": "2.1.0", "@chakra-ui/react-utils": "2.0.12", "@chakra-ui/styled-system": "2.9.1", "@chakra-ui/theme-utils": "2.0.18", "@chakra-ui/utils": "2.0.15", "react-fast-compare": "3.2.1"}, "peerDependencies": {"@emotion/react": "^11.0.0", "@emotion/styled": "^11.0.0", "react": ">=18"}}, "node_modules/@chakra-ui/table": {"version": "2.0.17", "resolved": "https://registry.npmjs.org/@chakra-ui/table/-/table-2.0.17.tgz", "integrity": "sha512-OScheTEp1LOYvTki2NFwnAYvac8siAhW9BI5RKm5f5ORL2gVJo4I72RUqE0aKe1oboxgm7CYt5afT5PS5cG61A==", "license": "MIT", "dependencies": {"@chakra-ui/react-context": "2.0.8", "@chakra-ui/shared-utils": "2.0.5"}, "peerDependencies": {"@chakra-ui/system": ">=2.0.0", "react": ">=18"}}, "node_modules/@chakra-ui/tabs": {"version": "2.1.9", "resolved": "https://registry.npmjs.org/@chakra-ui/tabs/-/tabs-2.1.9.tgz", "integrity": "sha512-Yf8e0kRvaGM6jfkJum0aInQ0U3ZlCafmrYYni2lqjcTtThqu+Yosmo3iYlnullXxCw5MVznfrkb9ySvgQowuYg==", "license": "MIT", "dependencies": {"@chakra-ui/clickable": "2.0.14", "@chakra-ui/descendant": "3.0.14", "@chakra-ui/lazy-utils": "2.0.5", "@chakra-ui/react-children-utils": "2.0.6", "@chakra-ui/react-context": "2.0.8", "@chakra-ui/react-use-controllable-state": "2.0.8", "@chakra-ui/react-use-merge-refs": "2.0.7", "@chakra-ui/react-use-safe-layout-effect": "2.0.5", "@chakra-ui/shared-utils": "2.0.5"}, "peerDependencies": {"@chakra-ui/system": ">=2.0.0", "react": ">=18"}}, "node_modules/@chakra-ui/tag": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/@chakra-ui/tag/-/tag-3.0.0.tgz", "integrity": "sha512-YWdMmw/1OWRwNkG9pX+wVtZio+B89odaPj6XeMn5nfNN8+jyhIEpouWv34+CO9G0m1lupJTxPSfgLAd7cqXZMA==", "license": "MIT", "dependencies": {"@chakra-ui/icon": "3.0.16", "@chakra-ui/react-context": "2.0.8"}, "peerDependencies": {"@chakra-ui/system": ">=2.0.0", "react": ">=18"}}, "node_modules/@chakra-ui/textarea": {"version": "2.0.19", "resolved": "https://registry.npmjs.org/@chakra-ui/textarea/-/textarea-2.0.19.tgz", "integrity": "sha512-adJk+qVGsFeJDvfn56CcJKKse8k7oMGlODrmpnpTdF+xvlsiTM+1GfaJvgNSpHHuQFdz/A0z1uJtfGefk0G2ZA==", "license": "MIT", "dependencies": {"@chakra-ui/form-control": "2.0.18", "@chakra-ui/shared-utils": "2.0.5"}, "peerDependencies": {"@chakra-ui/system": ">=2.0.0", "react": ">=18"}}, "node_modules/@chakra-ui/theme": {"version": "3.1.2", "resolved": "https://registry.npmjs.org/@chakra-ui/theme/-/theme-3.1.2.tgz", "integrity": "sha512-ebUXMS3LZw2OZxEQNYaFw3/XuA3jpyprhS/frjHMvZKSOaCjMW+c9z25S0jp1NnpQff08VGI8EWbyVZECXU1QA==", "license": "MIT", "dependencies": {"@chakra-ui/anatomy": "2.1.2", "@chakra-ui/shared-utils": "2.0.5", "@chakra-ui/theme-tools": "2.0.18"}, "peerDependencies": {"@chakra-ui/styled-system": ">=2.8.0"}}, "node_modules/@chakra-ui/theme-tools": {"version": "2.0.18", "resolved": "https://registry.npmjs.org/@chakra-ui/theme-tools/-/theme-tools-2.0.18.tgz", "integrity": "sha512-MbiRuXb2tb41FbnW41zhsYYAU0znlpfYZnu0mxCf8U2otCwPekJCfESUGYypjq4JnydQ7TDOk+Kz/Wi974l4mw==", "license": "MIT", "dependencies": {"@chakra-ui/anatomy": "2.1.2", "@chakra-ui/shared-utils": "2.0.5", "color2k": "^2.0.0"}, "peerDependencies": {"@chakra-ui/styled-system": ">=2.0.0"}}, "node_modules/@chakra-ui/theme-utils": {"version": "2.0.18", "resolved": "https://registry.npmjs.org/@chakra-ui/theme-utils/-/theme-utils-2.0.18.tgz", "integrity": "sha512-aSbkUUiFpc1NHC7lQdA6uYlr6EcZFXz6b4aJ7VRDpqTiywvqYnvfGzhmsB0z94vgtS9qXc6HoIwBp25jYGV2MA==", "license": "MIT", "dependencies": {"@chakra-ui/shared-utils": "2.0.5", "@chakra-ui/styled-system": "2.9.1", "@chakra-ui/theme": "3.1.2", "lodash.mergewith": "4.6.2"}}, "node_modules/@chakra-ui/toast": {"version": "6.1.4", "resolved": "https://registry.npmjs.org/@chakra-ui/toast/-/toast-6.1.4.tgz", "integrity": "sha512-wAcPHq/N/ar4jQxkUGhnsbp+lx2eKOpHxn1KaWdHXUkqCNUA1z09fvBsoMyzObSiiwbDuQPZG5RxsOhzfPZX4Q==", "license": "MIT", "dependencies": {"@chakra-ui/alert": "2.1.0", "@chakra-ui/close-button": "2.0.17", "@chakra-ui/portal": "2.0.16", "@chakra-ui/react-context": "2.0.8", "@chakra-ui/react-use-timeout": "2.0.5", "@chakra-ui/react-use-update-effect": "2.0.7", "@chakra-ui/shared-utils": "2.0.5", "@chakra-ui/styled-system": "2.9.1", "@chakra-ui/theme": "3.1.2"}, "peerDependencies": {"@chakra-ui/system": "2.5.8", "framer-motion": ">=4.0.0", "react": ">=18", "react-dom": ">=18"}}, "node_modules/@chakra-ui/tooltip": {"version": "2.2.9", "resolved": "https://registry.npmjs.org/@chakra-ui/tooltip/-/tooltip-2.2.9.tgz", "integrity": "sha512-ZoksllanqXRUyMDaiogvUVJ+RdFXwZrfrwx3RV22fejYZIQ602hZ3QHtHLB5ZnKFLbvXKMZKM23HxFTSb0Ytqg==", "license": "MIT", "dependencies": {"@chakra-ui/dom-utils": "2.1.0", "@chakra-ui/popper": "3.0.14", "@chakra-ui/portal": "2.0.16", "@chakra-ui/react-types": "2.0.7", "@chakra-ui/react-use-disclosure": "2.0.8", "@chakra-ui/react-use-event-listener": "2.0.7", "@chakra-ui/react-use-merge-refs": "2.0.7", "@chakra-ui/shared-utils": "2.0.5"}, "peerDependencies": {"@chakra-ui/system": ">=2.0.0", "framer-motion": ">=4.0.0", "react": ">=18", "react-dom": ">=18"}}, "node_modules/@chakra-ui/transition": {"version": "2.0.16", "resolved": "https://registry.npmjs.org/@chakra-ui/transition/-/transition-2.0.16.tgz", "integrity": "sha512-E+RkwlPc3H7P1crEXmXwDXMB2lqY2LLia2P5siQ4IEnRWIgZXlIw+8Em+NtHNgusel2N+9yuB0wT9SeZZeZ3CQ==", "license": "MIT", "dependencies": {"@chakra-ui/shared-utils": "2.0.5"}, "peerDependencies": {"framer-motion": ">=4.0.0", "react": ">=18"}}, "node_modules/@chakra-ui/utils": {"version": "2.0.15", "resolved": "https://registry.npmjs.org/@chakra-ui/utils/-/utils-2.0.15.tgz", "integrity": "sha512-El4+jL0WSaYYs+rJbuYFDbjmfCcfGDmRY95GO4xwzit6YAPZBLcR65rOEwLps+XWluZTy1xdMrusg/hW0c1aAA==", "license": "MIT", "dependencies": {"@types/lodash.mergewith": "4.6.7", "css-box-model": "1.2.1", "framesync": "6.1.2", "lodash.mergewith": "4.6.2"}}, "node_modules/@chakra-ui/visually-hidden": {"version": "2.0.15", "resolved": "https://registry.npmjs.org/@chakra-ui/visually-hidden/-/visually-hidden-2.0.15.tgz", "integrity": "sha512-WWULIiucYRBIewHKFA7BssQ2ABLHLVd9lrUo3N3SZgR0u4ZRDDVEUNOy+r+9ruDze8+36dGbN9wsN1IdELtdOw==", "license": "MIT", "peerDependencies": {"@chakra-ui/system": ">=2.0.0", "react": ">=18"}}, "node_modules/@colors/colors": {"version": "1.5.0", "license": "MIT", "optional": true, "engines": {"node": ">=0.1.90"}}, "node_modules/@cypress/request": {"version": "2.88.12", "license": "Apache-2.0", "dependencies": {"aws-sign2": "~0.7.0", "aws4": "^1.8.0", "caseless": "~0.12.0", "combined-stream": "~1.0.6", "extend": "~3.0.2", "forever-agent": "~0.6.1", "form-data": "~2.3.2", "http-signature": "~1.3.6", "is-typedarray": "~1.0.0", "isstream": "~0.1.2", "json-stringify-safe": "~5.0.1", "mime-types": "~2.1.19", "performance-now": "^2.1.0", "qs": "~6.10.3", "safe-buffer": "^5.1.2", "tough-cookie": "^4.1.3", "tunnel-agent": "^0.6.0", "uuid": "^8.3.2"}, "engines": {"node": ">= 6"}}, "node_modules/@cypress/request/node_modules/form-data": {"version": "2.3.3", "license": "MIT", "dependencies": {"asynckit": "^0.4.0", "combined-stream": "^1.0.6", "mime-types": "^2.1.12"}, "engines": {"node": ">= 0.12"}}, "node_modules/@cypress/request/node_modules/uuid": {"version": "8.3.2", "resolved": "https://registry.npmjs.org/uuid/-/uuid-8.3.2.tgz", "integrity": "sha512-+NYs2QeMWy+GWFOEm9xnn6HCDp0l7QBD7ml8zLUmJ+93Q5NF0NocErnwkTkXVFNiX3/fpC6afS8Dhb/gz7R7eg==", "license": "MIT", "bin": {"uuid": "dist/bin/uuid"}}, "node_modules/@cypress/xvfb": {"version": "1.2.4", "license": "MIT", "dependencies": {"debug": "^3.1.0", "lodash.once": "^4.1.1"}}, "node_modules/@cypress/xvfb/node_modules/debug": {"version": "3.2.7", "license": "MIT", "dependencies": {"ms": "^2.1.1"}}, "node_modules/@emotion/babel-plugin": {"version": "11.13.5", "resolved": "https://registry.npmjs.org/@emotion/babel-plugin/-/babel-plugin-11.13.5.tgz", "integrity": "sha512-pxHCpT2ex+0q+HH91/zsdHkw/lXd468DIN2zvfvLtPKLLMo6gQj7oLObq8PhkrxOZb/gGCq03S3Z7PDhS8pduQ==", "license": "MIT", "dependencies": {"@babel/helper-module-imports": "^7.16.7", "@babel/runtime": "^7.18.3", "@emotion/hash": "^0.9.2", "@emotion/memoize": "^0.9.0", "@emotion/serialize": "^1.3.3", "babel-plugin-macros": "^3.1.0", "convert-source-map": "^1.5.0", "escape-string-regexp": "^4.0.0", "find-root": "^1.1.0", "source-map": "^0.5.7", "stylis": "4.2.0"}}, "node_modules/@emotion/cache": {"version": "11.13.5", "resolved": "https://registry.npmjs.org/@emotion/cache/-/cache-11.13.5.tgz", "integrity": "sha512-Z3xbtJ+UcK76eWkagZ1onvn/wAVb1GOMuR15s30Fm2wrMgC7jzpnO2JZXr4eujTTqoQFUrZIw/rT0c6Zzjca1g==", "license": "MIT", "dependencies": {"@emotion/memoize": "^0.9.0", "@emotion/sheet": "^1.4.0", "@emotion/utils": "^1.4.2", "@emotion/weak-memoize": "^0.4.0", "stylis": "4.2.0"}}, "node_modules/@emotion/hash": {"version": "0.9.2", "license": "MIT"}, "node_modules/@emotion/is-prop-valid": {"version": "1.3.1", "resolved": "https://registry.npmjs.org/@emotion/is-prop-valid/-/is-prop-valid-1.3.1.tgz", "integrity": "sha512-/ACwoqx7XQi9knQs/G0qKvv5teDMhD7bXYns9N/wM8ah8iNb8jZ2uNO0YOgiq2o2poIvVtJS2YALasQuMSQ7Kw==", "license": "MIT", "dependencies": {"@emotion/memoize": "^0.9.0"}}, "node_modules/@emotion/memoize": {"version": "0.9.0", "license": "MIT"}, "node_modules/@emotion/react": {"version": "11.11.1", "resolved": "https://registry.npmjs.org/@emotion/react/-/react-11.11.1.tgz", "integrity": "sha512-5mlW1DquU5HaxjLkfkGN1GA/fvVGdyHURRiX/0FHl2cfIfRxSOfmxEH5YS43edp0OldZrZ+dkBKbngxcNCdZvA==", "license": "MIT", "dependencies": {"@babel/runtime": "^7.18.3", "@emotion/babel-plugin": "^11.11.0", "@emotion/cache": "^11.11.0", "@emotion/serialize": "^1.1.2", "@emotion/use-insertion-effect-with-fallbacks": "^1.0.1", "@emotion/utils": "^1.2.1", "@emotion/weak-memoize": "^0.3.1", "hoist-non-react-statics": "^3.3.1"}, "peerDependencies": {"react": ">=16.8.0"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "node_modules/@emotion/react/node_modules/@emotion/weak-memoize": {"version": "0.3.1", "resolved": "https://registry.npmjs.org/@emotion/weak-memoize/-/weak-memoize-0.3.1.tgz", "integrity": "sha512-EsBwpc7hBUJWAsNPBmJy4hxWx12v6bshQsldrVmjxJoc3isbxhOrF2IcCpaXxfvq03NwkI7sbsOLXbYuqF/8Ww==", "license": "MIT"}, "node_modules/@emotion/serialize": {"version": "1.3.3", "resolved": "https://registry.npmjs.org/@emotion/serialize/-/serialize-1.3.3.tgz", "integrity": "sha512-EISGqt7sSNWHGI76hC7x1CksiXPahbxEOrC5RjmFRJTqLyEK9/9hZvBbiYn70dw4wuwMKiEMCUlR6ZXTSWQqxA==", "license": "MIT", "dependencies": {"@emotion/hash": "^0.9.2", "@emotion/memoize": "^0.9.0", "@emotion/unitless": "^0.10.0", "@emotion/utils": "^1.4.2", "csstype": "^3.0.2"}}, "node_modules/@emotion/sheet": {"version": "1.4.0", "license": "MIT"}, "node_modules/@emotion/styled": {"version": "11.11.0", "resolved": "https://registry.npmjs.org/@emotion/styled/-/styled-11.11.0.tgz", "integrity": "sha512-hM5Nnvu9P3midq5aaXj4I+lnSfNi7Pmd4EWk1fOZ3pxookaQTNew6bp4JaCBYM4HVFZF9g7UjJmsUmC2JlxOng==", "dependencies": {"@babel/runtime": "^7.18.3", "@emotion/babel-plugin": "^11.11.0", "@emotion/is-prop-valid": "^1.2.1", "@emotion/serialize": "^1.1.2", "@emotion/use-insertion-effect-with-fallbacks": "^1.0.1", "@emotion/utils": "^1.2.1"}, "peerDependencies": {"@emotion/react": "^11.0.0-rc.0", "react": ">=16.8.0"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "node_modules/@emotion/unitless": {"version": "0.10.0", "license": "MIT"}, "node_modules/@emotion/use-insertion-effect-with-fallbacks": {"version": "1.1.0", "license": "MIT", "peerDependencies": {"react": ">=16.8.0"}}, "node_modules/@emotion/utils": {"version": "1.4.2", "resolved": "https://registry.npmjs.org/@emotion/utils/-/utils-1.4.2.tgz", "integrity": "sha512-3vLclRofFziIa3J2wDh9jjbkUz9qk5Vi3IZ/FSTKViB0k+ef0fPV7dYrUIugbgupYDx7v9ud/SjrtEP8Y4xLoA==", "license": "MIT"}, "node_modules/@emotion/weak-memoize": {"version": "0.4.0", "license": "MIT"}, "node_modules/@eslint-community/eslint-utils": {"version": "4.4.0", "license": "MIT", "dependencies": {"eslint-visitor-keys": "^3.3.0"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "peerDependencies": {"eslint": "^6.0.0 || ^7.0.0 || >=8.0.0"}}, "node_modules/@eslint-community/regexpp": {"version": "4.11.0", "license": "MIT", "engines": {"node": "^12.0.0 || ^14.0.0 || >=16.0.0"}}, "node_modules/@eslint/eslintrc": {"version": "2.1.4", "license": "MIT", "dependencies": {"ajv": "^6.12.4", "debug": "^4.3.2", "espree": "^9.6.0", "globals": "^13.19.0", "ignore": "^5.2.0", "import-fresh": "^3.2.1", "js-yaml": "^4.1.0", "minimatch": "^3.1.2", "strip-json-comments": "^3.1.1"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/@eslint/eslintrc/node_modules/globals": {"version": "13.24.0", "license": "MIT", "dependencies": {"type-fest": "^0.20.2"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/@eslint/eslintrc/node_modules/type-fest": {"version": "0.20.2", "license": "(MIT OR CC0-1.0)", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/@eslint/js": {"version": "8.41.0", "license": "MIT", "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}}, "node_modules/@faker-js/faker": {"version": "8.4.1", "funding": [{"type": "opencollective", "url": "https://opencollective.com/fakerjs"}], "license": "MIT", "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0", "npm": ">=6.14.13"}}, "node_modules/@floating-ui/core": {"version": "1.6.7", "license": "MIT", "dependencies": {"@floating-ui/utils": "^0.2.7"}}, "node_modules/@floating-ui/dom": {"version": "1.6.12", "resolved": "https://registry.npmjs.org/@floating-ui/dom/-/dom-1.6.12.tgz", "integrity": "sha512-NP83c0HjokcGVEMeoStg317VD9W7eDlGK7457dMBANbKA6GJZdc7rjujdgqzTaz93jkGgc5P/jeWbaCHnMNc+w==", "license": "MIT", "dependencies": {"@floating-ui/core": "^1.6.0", "@floating-ui/utils": "^0.2.8"}}, "node_modules/@floating-ui/react-dom": {"version": "2.1.2", "resolved": "https://registry.npmjs.org/@floating-ui/react-dom/-/react-dom-2.1.2.tgz", "integrity": "sha512-06okr5cgPzMNBy+Ycse2A6udMi4bqwW/zgBF/rwjcNqWkyr82Mcg8b0vjX8OJpZFy/FKjJmw6wV7t44kK6kW7A==", "dependencies": {"@floating-ui/dom": "^1.0.0"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}}, "node_modules/@floating-ui/utils": {"version": "0.2.8", "resolved": "https://registry.npmjs.org/@floating-ui/utils/-/utils-0.2.8.tgz", "integrity": "sha512-kym7SodPp8/wloecOpcmSnWJsK7M0E5Wg8UcFA+uO4B9s5d0ywXOEro/8HM9x0rW+TljRzul/14UYz3TleT3ig=="}, "node_modules/@hookform/resolvers": {"version": "3.9.1", "resolved": "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-3.9.1.tgz", "integrity": "sha512-ud2HqmGBM0P0IABqoskKWI6PEf6ZDDBZkFqe2Vnl+mTHCEHzr3ISjjZyCwTjC/qpL25JC9aIDkloQejvMeq0ug==", "license": "MIT", "peerDependencies": {"react-hook-form": "^7.0.0"}}, "node_modules/@humanwhocodes/config-array": {"version": "0.11.14", "license": "Apache-2.0", "dependencies": {"@humanwhocodes/object-schema": "^2.0.2", "debug": "^4.3.1", "minimatch": "^3.0.5"}, "engines": {"node": ">=10.10.0"}}, "node_modules/@humanwhocodes/module-importer": {"version": "1.0.1", "license": "Apache-2.0", "engines": {"node": ">=12.22"}, "funding": {"type": "github", "url": "https://github.com/sponsors/nzakas"}}, "node_modules/@humanwhocodes/object-schema": {"version": "2.0.3", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/@isaacs/cliui": {"version": "8.0.2", "license": "ISC", "dependencies": {"string-width": "^5.1.2", "string-width-cjs": "npm:string-width@^4.2.0", "strip-ansi": "^7.0.1", "strip-ansi-cjs": "npm:strip-ansi@^6.0.1", "wrap-ansi": "^8.1.0", "wrap-ansi-cjs": "npm:wrap-ansi@^7.0.0"}, "engines": {"node": ">=12"}}, "node_modules/@isaacs/cliui/node_modules/ansi-regex": {"version": "6.0.1", "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/ansi-regex?sponsor=1"}}, "node_modules/@isaacs/cliui/node_modules/ansi-styles": {"version": "6.2.1", "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/@isaacs/cliui/node_modules/emoji-regex": {"version": "9.2.2", "license": "MIT"}, "node_modules/@isaacs/cliui/node_modules/string-width": {"version": "5.1.2", "license": "MIT", "dependencies": {"eastasianwidth": "^0.2.0", "emoji-regex": "^9.2.2", "strip-ansi": "^7.0.1"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/@isaacs/cliui/node_modules/strip-ansi": {"version": "7.1.0", "license": "MIT", "dependencies": {"ansi-regex": "^6.0.1"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/strip-ansi?sponsor=1"}}, "node_modules/@isaacs/cliui/node_modules/wrap-ansi": {"version": "8.1.0", "license": "MIT", "dependencies": {"ansi-styles": "^6.1.0", "string-width": "^5.0.1", "strip-ansi": "^7.0.1"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/wrap-ansi?sponsor=1"}}, "node_modules/@jridgewell/gen-mapping": {"version": "0.3.5", "license": "MIT", "dependencies": {"@jridgewell/set-array": "^1.2.1", "@jridgewell/sourcemap-codec": "^1.4.10", "@jridgewell/trace-mapping": "^0.3.24"}, "engines": {"node": ">=6.0.0"}}, "node_modules/@jridgewell/resolve-uri": {"version": "3.1.2", "license": "MIT", "engines": {"node": ">=6.0.0"}}, "node_modules/@jridgewell/set-array": {"version": "1.2.1", "license": "MIT", "engines": {"node": ">=6.0.0"}}, "node_modules/@jridgewell/sourcemap-codec": {"version": "1.5.0", "license": "MIT"}, "node_modules/@jridgewell/trace-mapping": {"version": "0.3.25", "license": "MIT", "dependencies": {"@jridgewell/resolve-uri": "^3.1.0", "@jridgewell/sourcemap-codec": "^1.4.14"}}, "node_modules/@kurkle/color": {"version": "0.3.2", "license": "MIT"}, "node_modules/@next/env": {"version": "14.2.8", "resolved": "https://registry.npmjs.org/@next/env/-/env-14.2.8.tgz", "integrity": "sha512-L44a+ynqkolyNBnYfF8VoCiSrjSZWgEHYKkKLGcs/a80qh7AkfVUD/MduVPgdsWZ31tgROR+yJRA0PZjSVBXWQ==", "license": "MIT"}, "node_modules/@next/eslint-plugin-next": {"version": "13.4.5", "dev": true, "license": "MIT", "dependencies": {"glob": "7.1.7"}}, "node_modules/@next/eslint-plugin-next/node_modules/glob": {"version": "7.1.7", "resolved": "https://registry.npmjs.org/glob/-/glob-7.1.7.tgz", "integrity": "sha512-OvD9ENzPLbegENnYP5UUfJIirTg4+XwMWGaQfQTY0JenxNvvIKP3U3/tAQSPIu/lHxXYSZmpXlUHeqAIdKzBLQ==", "deprecated": "Glob versions prior to v9 are no longer supported", "dev": true, "license": "ISC", "dependencies": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.0.4", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}, "engines": {"node": "*"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/@next/swc-darwin-arm64": {"version": "14.2.8", "resolved": "https://registry.npmjs.org/@next/swc-darwin-arm64/-/swc-darwin-arm64-14.2.8.tgz", "integrity": "sha512-1VrQlG8OzdyvvGZhGJFnaNE2P10Jjy/2FopnqbY0nSa/gr8If3iINxvOEW3cmVeoAYkmW0RsBazQecA2dBFOSw==", "cpu": ["arm64"], "license": "MIT", "optional": true, "os": ["darwin"], "engines": {"node": ">= 10"}}, "node_modules/@next/swc-darwin-x64": {"version": "14.2.8", "resolved": "https://registry.npmjs.org/@next/swc-darwin-x64/-/swc-darwin-x64-14.2.8.tgz", "integrity": "sha512-87t3I86rNRSOJB1gXIUzaQWWSWrkWPDyZGsR0Z7JAPtLeX3uUOW2fHxl7dNWD2BZvbvftctTQjgtfpp7nMtmWg==", "cpu": ["x64"], "license": "MIT", "optional": true, "os": ["darwin"], "engines": {"node": ">= 10"}}, "node_modules/@next/swc-linux-arm64-gnu": {"version": "14.2.8", "resolved": "https://registry.npmjs.org/@next/swc-linux-arm64-gnu/-/swc-linux-arm64-gnu-14.2.8.tgz", "integrity": "sha512-ta2sfVzbOpTbgBrF9HM5m+U58dv6QPuwU4n5EX4LLyCJGKc433Z0D9h9gay/HSOjLEXJ2fJYrMP5JYYbHdxhtw==", "cpu": ["arm64"], "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">= 10"}}, "node_modules/@next/swc-linux-arm64-musl": {"version": "14.2.8", "resolved": "https://registry.npmjs.org/@next/swc-linux-arm64-musl/-/swc-linux-arm64-musl-14.2.8.tgz", "integrity": "sha512-+IoLTPK6Z5uIgDhgeWnQF5/o5GBN7+zyUNrs4Bes1W3g9++YELb8y0unFybS8s87ntAKMDl6jeQ+mD7oNwp/Ng==", "cpu": ["arm64"], "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">= 10"}}, "node_modules/@next/swc-linux-x64-gnu": {"version": "14.2.8", "resolved": "https://registry.npmjs.org/@next/swc-linux-x64-gnu/-/swc-linux-x64-gnu-14.2.8.tgz", "integrity": "sha512-pO+hVXC+mvzUOQJJRG4RX4wJsRJ5BkURSf6dD6EjUXAX4Ml9es1WsEfkaZ4lcpmFzFvY47IkDaffks/GdCn9ag==", "cpu": ["x64"], "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">= 10"}}, "node_modules/@next/swc-linux-x64-musl": {"version": "14.2.8", "resolved": "https://registry.npmjs.org/@next/swc-linux-x64-musl/-/swc-linux-x64-musl-14.2.8.tgz", "integrity": "sha512-bCat9izctychCtf3uL1nqHq31N5e1VxvdyNcBQflkudPMLbxVnlrw45Vi87K+lt1CwrtVayHqzo4ie0Szcpwzg==", "cpu": ["x64"], "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">= 10"}}, "node_modules/@next/swc-win32-arm64-msvc": {"version": "14.2.8", "resolved": "https://registry.npmjs.org/@next/swc-win32-arm64-msvc/-/swc-win32-arm64-msvc-14.2.8.tgz", "integrity": "sha512-gbxfUaSPV7EyUobpavida2Hwi62GhSJaSg7iBjmBWoxkxlmETOD7U4tWt763cGIsyE6jM7IoNavq0BXqwdW2QA==", "cpu": ["arm64"], "license": "MIT", "optional": true, "os": ["win32"], "engines": {"node": ">= 10"}}, "node_modules/@next/swc-win32-ia32-msvc": {"version": "14.2.8", "resolved": "https://registry.npmjs.org/@next/swc-win32-ia32-msvc/-/swc-win32-ia32-msvc-14.2.8.tgz", "integrity": "sha512-PUXzEzjTTlUh3b5VAn1nlpwvujTnuCMMwbiCnaTazoVlN1nA3kWjlmp42IfURA2N/nyrlVEw7pURa/o4Qxj1cw==", "cpu": ["ia32"], "license": "MIT", "optional": true, "os": ["win32"], "engines": {"node": ">= 10"}}, "node_modules/@next/swc-win32-x64-msvc": {"version": "14.2.8", "resolved": "https://registry.npmjs.org/@next/swc-win32-x64-msvc/-/swc-win32-x64-msvc-14.2.8.tgz", "integrity": "sha512-EnPKv0ttq02E9/1KZ/8Dn7kuutv6hy1CKc0HlNcvzOQcm4/SQtvfws5gY0zrG9tuupd3HfC2L/zcTrnBhpjTuQ==", "cpu": ["x64"], "license": "MIT", "optional": true, "os": ["win32"], "engines": {"node": ">= 10"}}, "node_modules/@nodelib/fs.scandir": {"version": "2.1.5", "license": "MIT", "dependencies": {"@nodelib/fs.stat": "2.0.5", "run-parallel": "^1.1.9"}, "engines": {"node": ">= 8"}}, "node_modules/@nodelib/fs.stat": {"version": "2.0.5", "license": "MIT", "engines": {"node": ">= 8"}}, "node_modules/@nodelib/fs.walk": {"version": "1.2.8", "license": "MIT", "dependencies": {"@nodelib/fs.scandir": "2.1.5", "fastq": "^1.6.0"}, "engines": {"node": ">= 8"}}, "node_modules/@nolyfill/is-core-module": {"version": "1.0.39", "license": "MIT", "engines": {"node": ">=12.4.0"}}, "node_modules/@opentelemetry/api": {"version": "1.9.0", "resolved": "https://registry.npmjs.org/@opentelemetry/api/-/api-1.9.0.tgz", "integrity": "sha512-3giAOQvZiH5F9bMlMiv8+GSPMeqg0dbaeo58/0SlA9sxSqZhnUtxzX9/2FzyhS9sWQf5S0GJE0AKBrFqjpeYcg==", "license": "Apache-2.0", "engines": {"node": ">=8.0.0"}}, "node_modules/@panva/hkdf": {"version": "1.2.1", "license": "MIT", "funding": {"url": "https://github.com/sponsors/panva"}}, "node_modules/@pkgjs/parseargs": {"version": "0.11.0", "license": "MIT", "optional": true, "engines": {"node": ">=14"}}, "node_modules/@popperjs/core": {"version": "2.11.8", "resolved": "https://registry.npmjs.org/@popperjs/core/-/core-2.11.8.tgz", "integrity": "sha512-P1st0aksCrn9sGZhp8GMYwBnQsbvAWsZAX44oXNNvLHGqAOcoVxmjZiohstwQ7SqKnbR47akdNi+uleWD8+g6A==", "license": "MIT", "funding": {"type": "opencollective", "url": "https://opencollective.com/popperjs"}}, "node_modules/@radix-ui/number": {"version": "1.1.0", "license": "MIT"}, "node_modules/@radix-ui/primitive": {"version": "1.1.0", "license": "MIT"}, "node_modules/@radix-ui/react-accordion": {"version": "1.2.1", "resolved": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-1.2.1.tgz", "integrity": "sha512-bg/l7l5QzUjgsh8kjwDFommzAshnUsuVMV5NM56QVCm+7ZckYdd9P/ExR8xG/Oup0OajVxNLaHJ1tb8mXk+nzQ==", "license": "MIT", "dependencies": {"@radix-ui/primitive": "1.1.0", "@radix-ui/react-collapsible": "1.1.1", "@radix-ui/react-collection": "1.1.0", "@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-id": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"@types/react": "*", "@types/react-dom": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "node_modules/@radix-ui/react-accordion/node_modules/@radix-ui/react-context": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/@radix-ui/react-context/-/react-context-1.1.1.tgz", "integrity": "sha512-UASk9zi+crv9WteK/NU4PLvOoL3OuE6BWVKNF6hPRBtYBDXQ2u5iu3O59zUlJiTVvkyuycnqrztsHVJwcK9K+Q==", "license": "MIT", "peerDependencies": {"@types/react": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "node_modules/@radix-ui/react-arrow": {"version": "1.1.0", "license": "MIT", "dependencies": {"@radix-ui/react-primitive": "2.0.0"}, "peerDependencies": {"@types/react": "*", "@types/react-dom": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "node_modules/@radix-ui/react-checkbox": {"version": "1.3.1", "resolved": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.3.1.tgz", "integrity": "sha512-xTaLKAO+XXMPK/BpVTSaAAhlefmvMSACjIhK9mGsImvX2ljcTDm8VGR1CuS1uYcNdR5J+oiOhoJZc5un6bh3VQ==", "license": "MIT", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.4", "@radix-ui/react-primitive": "2.1.2", "@radix-ui/react-use-controllable-state": "1.2.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-use-size": "1.1.1"}, "peerDependencies": {"@types/react": "*", "@types/react-dom": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "node_modules/@radix-ui/react-checkbox/node_modules/@radix-ui/primitive": {"version": "1.1.2", "resolved": "https://registry.npmjs.org/@radix-ui/primitive/-/primitive-1.1.2.tgz", "integrity": "sha512-XnbHrrprsNqZKQhStrSwgRUQzoCI1glLzdw79xiZPoofhGICeZRSQ3dIxAKH1gb3OHfNf4d6f+vAv3kil2eggA==", "license": "MIT"}, "node_modules/@radix-ui/react-checkbox/node_modules/@radix-ui/react-compose-refs": {"version": "1.1.2", "resolved": "https://registry.npmjs.org/@radix-ui/react-compose-refs/-/react-compose-refs-1.1.2.tgz", "integrity": "sha512-z4eqJvfiNnFMHIIvXP3CY57y2WJs5g2v3X0zm9mEJkrkNv4rDxu+sg9Jh8EkXyeqBkB7SOcboo9dMVqhyrACIg==", "license": "MIT", "peerDependencies": {"@types/react": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "node_modules/@radix-ui/react-checkbox/node_modules/@radix-ui/react-context": {"version": "1.1.2", "resolved": "https://registry.npmjs.org/@radix-ui/react-context/-/react-context-1.1.2.tgz", "integrity": "sha512-jCi/QKUM2r1Ju5a3J64TH2A5SpKAgh0LpknyqdQ4m6DCV0xJ2HG1xARRwNGPQfi1SLdLWZ1OJz6F4OMBBNiGJA==", "license": "MIT", "peerDependencies": {"@types/react": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "node_modules/@radix-ui/react-checkbox/node_modules/@radix-ui/react-presence": {"version": "1.1.4", "resolved": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-1.1.4.tgz", "integrity": "sha512-ueDqRbdc4/bkaQT3GIpLQssRlFgWaL/U2z/S31qRwwLWoxHLgry3SIfCwhxeQNbirEUXFa+lq3RL3oBYXtcmIA==", "license": "MIT", "dependencies": {"@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-layout-effect": "1.1.1"}, "peerDependencies": {"@types/react": "*", "@types/react-dom": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "node_modules/@radix-ui/react-checkbox/node_modules/@radix-ui/react-primitive": {"version": "2.1.2", "resolved": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-2.1.2.tgz", "integrity": "sha512-uHa+l/lKfxuDD2zjN/0peM/RhhSmRjr5YWdk/37EnSv1nJ88uvG85DPexSm8HdFQROd2VdERJ6ynXbkCFi+APw==", "license": "MIT", "dependencies": {"@radix-ui/react-slot": "1.2.2"}, "peerDependencies": {"@types/react": "*", "@types/react-dom": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "node_modules/@radix-ui/react-checkbox/node_modules/@radix-ui/react-use-controllable-state": {"version": "1.2.2", "resolved": "https://registry.npmjs.org/@radix-ui/react-use-controllable-state/-/react-use-controllable-state-1.2.2.tgz", "integrity": "sha512-BjasUjixPFdS+NKkypcyyN5Pmg83Olst0+c6vGov0diwTEo6mgdqVR6hxcEgFuh4QrAs7Rc+9KuGJ9TVCj0Zzg==", "license": "MIT", "dependencies": {"@radix-ui/react-use-effect-event": "0.0.2", "@radix-ui/react-use-layout-effect": "1.1.1"}, "peerDependencies": {"@types/react": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "node_modules/@radix-ui/react-checkbox/node_modules/@radix-ui/react-use-layout-effect": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/@radix-ui/react-use-layout-effect/-/react-use-layout-effect-1.1.1.tgz", "integrity": "sha512-RbJRS4UWQFkzHTTwVymMTUv8EqYhOp8dOOviLj2ugtTiXRaRQS7GLGxZTLL1jWhMeoSCf5zmcZkqTl9IiYfXcQ==", "license": "MIT", "peerDependencies": {"@types/react": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "node_modules/@radix-ui/react-checkbox/node_modules/@radix-ui/react-use-previous": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/@radix-ui/react-use-previous/-/react-use-previous-1.1.1.tgz", "integrity": "sha512-2dHfToCj/pzca2Ck724OZ5L0EVrr3eHRNsG/b3xQJLA2hZpVCS99bLAX+hm1IHXDEnzU6by5z/5MIY794/a8NQ==", "license": "MIT", "peerDependencies": {"@types/react": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "node_modules/@radix-ui/react-checkbox/node_modules/@radix-ui/react-use-size": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/@radix-ui/react-use-size/-/react-use-size-1.1.1.tgz", "integrity": "sha512-ewrXRDTAqAXlkl6t/fkXWNAhFX9I+CkKlw6zjEwk86RSPKwZr3xpBRso655aqYafwtnbpHLj6toFzmd6xdVptQ==", "license": "MIT", "dependencies": {"@radix-ui/react-use-layout-effect": "1.1.1"}, "peerDependencies": {"@types/react": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "node_modules/@radix-ui/react-collapsible": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-1.1.1.tgz", "integrity": "sha512-1///SnrfQHJEofLokyczERxQbWfCGQlQ2XsCZMucVs6it+lq9iw4vXy+uDn1edlb58cOZOWSldnfPAYcT4O/Yg==", "license": "MIT", "dependencies": {"@radix-ui/primitive": "1.1.0", "@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-id": "1.1.0", "@radix-ui/react-presence": "1.1.1", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-use-controllable-state": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0"}, "peerDependencies": {"@types/react": "*", "@types/react-dom": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "node_modules/@radix-ui/react-collapsible/node_modules/@radix-ui/react-context": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/@radix-ui/react-context/-/react-context-1.1.1.tgz", "integrity": "sha512-UASk9zi+crv9WteK/NU4PLvOoL3OuE6BWVKNF6hPRBtYBDXQ2u5iu3O59zUlJiTVvkyuycnqrztsHVJwcK9K+Q==", "license": "MIT", "peerDependencies": {"@types/react": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "node_modules/@radix-ui/react-collapsible/node_modules/@radix-ui/react-presence": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-1.1.1.tgz", "integrity": "sha512-IeFXVi4YS1K0wVZzXNrbaaUvIJ3qdY+/Ih4eHFhWA9SwGR9UDX7Ck8abvL57C4cv3wwMvUE0OG69Qc3NCcTe/A==", "license": "MIT", "dependencies": {"@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0"}, "peerDependencies": {"@types/react": "*", "@types/react-dom": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "node_modules/@radix-ui/react-collection": {"version": "1.1.0", "license": "MIT", "dependencies": {"@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-context": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-slot": "1.1.0"}, "peerDependencies": {"@types/react": "*", "@types/react-dom": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "node_modules/@radix-ui/react-collection/node_modules/@radix-ui/react-slot": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/@radix-ui/react-slot/-/react-slot-1.1.0.tgz", "integrity": "sha512-FUCf5XMfmW4dtYl69pdS4DbxKy8nj4M7SafBgPllysxmdachynNflAdp/gCsnYWNDnge6tI9onzMp5ARYc1KNw==", "license": "MIT", "dependencies": {"@radix-ui/react-compose-refs": "1.1.0"}, "peerDependencies": {"@types/react": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "node_modules/@radix-ui/react-compose-refs": {"version": "1.1.0", "license": "MIT", "peerDependencies": {"@types/react": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "node_modules/@radix-ui/react-context": {"version": "1.1.0", "license": "MIT", "peerDependencies": {"@types/react": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "node_modules/@radix-ui/react-dialog": {"version": "1.1.2", "resolved": "https://registry.npmjs.org/@radix-ui/react-dialog/-/react-dialog-1.1.2.tgz", "integrity": "sha512-Yj4dZtqa2o+kG61fzB0H2qUvmwBA2oyQroGLyNtBj1beo1khoQ3q1a2AO8rrQYjd8256CO9+N8L9tvsS+bnIyA==", "license": "MIT", "dependencies": {"@radix-ui/primitive": "1.1.0", "@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-dismissable-layer": "1.1.1", "@radix-ui/react-focus-guards": "1.1.1", "@radix-ui/react-focus-scope": "1.1.0", "@radix-ui/react-id": "1.1.0", "@radix-ui/react-portal": "1.1.2", "@radix-ui/react-presence": "1.1.1", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-slot": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0", "aria-hidden": "^1.1.1", "react-remove-scroll": "2.6.0"}, "peerDependencies": {"@types/react": "*", "@types/react-dom": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "node_modules/@radix-ui/react-dialog/node_modules/@radix-ui/react-context": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/@radix-ui/react-context/-/react-context-1.1.1.tgz", "integrity": "sha512-UASk9zi+crv9WteK/NU4PLvOoL3OuE6BWVKNF6hPRBtYBDXQ2u5iu3O59zUlJiTVvkyuycnqrztsHVJwcK9K+Q==", "license": "MIT", "peerDependencies": {"@types/react": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "node_modules/@radix-ui/react-dialog/node_modules/@radix-ui/react-dismissable-layer": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/@radix-ui/react-dismissable-layer/-/react-dismissable-layer-1.1.1.tgz", "integrity": "sha512-QSxg29lfr/xcev6kSz7MAlmDnzbP1eI/Dwn3Tp1ip0KT5CUELsxkekFEMVBEoykI3oV39hKT4TKZzBNMbcTZYQ==", "license": "MIT", "dependencies": {"@radix-ui/primitive": "1.1.0", "@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-use-callback-ref": "1.1.0", "@radix-ui/react-use-escape-keydown": "1.1.0"}, "peerDependencies": {"@types/react": "*", "@types/react-dom": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "node_modules/@radix-ui/react-dialog/node_modules/@radix-ui/react-focus-guards": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/@radix-ui/react-focus-guards/-/react-focus-guards-1.1.1.tgz", "integrity": "sha512-pSIwfrT1a6sIoDASCSpFwOasEwKTZWDw/iBdtnqKO7v6FeOzYJ7U53cPzYFVR3geGGXgVHaH+CdngrrAzqUGxg==", "license": "MIT", "peerDependencies": {"@types/react": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "node_modules/@radix-ui/react-dialog/node_modules/@radix-ui/react-portal": {"version": "1.1.2", "resolved": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-1.1.2.tgz", "integrity": "sha512-WeDYLGPxJb/5EGBoedyJbT0MpoULmwnIPMJMSldkuiMsBAv7N1cRdsTWZWht9vpPOiN3qyiGAtbK2is47/uMFg==", "license": "MIT", "dependencies": {"@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-use-layout-effect": "1.1.0"}, "peerDependencies": {"@types/react": "*", "@types/react-dom": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "node_modules/@radix-ui/react-dialog/node_modules/@radix-ui/react-presence": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-1.1.1.tgz", "integrity": "sha512-IeFXVi4YS1K0wVZzXNrbaaUvIJ3qdY+/Ih4eHFhWA9SwGR9UDX7Ck8abvL57C4cv3wwMvUE0OG69Qc3NCcTe/A==", "license": "MIT", "dependencies": {"@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0"}, "peerDependencies": {"@types/react": "*", "@types/react-dom": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "node_modules/@radix-ui/react-dialog/node_modules/@radix-ui/react-slot": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/@radix-ui/react-slot/-/react-slot-1.1.0.tgz", "integrity": "sha512-FUCf5XMfmW4dtYl69pdS4DbxKy8nj4M7SafBgPllysxmdachynNflAdp/gCsnYWNDnge6tI9onzMp5ARYc1KNw==", "license": "MIT", "dependencies": {"@radix-ui/react-compose-refs": "1.1.0"}, "peerDependencies": {"@types/react": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "node_modules/@radix-ui/react-direction": {"version": "1.1.0", "license": "MIT", "peerDependencies": {"@types/react": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "node_modules/@radix-ui/react-dismissable-layer": {"version": "1.1.0", "license": "MIT", "dependencies": {"@radix-ui/primitive": "1.1.0", "@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-use-callback-ref": "1.1.0", "@radix-ui/react-use-escape-keydown": "1.1.0"}, "peerDependencies": {"@types/react": "*", "@types/react-dom": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "node_modules/@radix-ui/react-dropdown-menu": {"version": "2.1.1", "license": "MIT", "dependencies": {"@radix-ui/primitive": "1.1.0", "@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-context": "1.1.0", "@radix-ui/react-id": "1.1.0", "@radix-ui/react-menu": "2.1.1", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"@types/react": "*", "@types/react-dom": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "node_modules/@radix-ui/react-focus-guards": {"version": "1.1.0", "license": "MIT", "peerDependencies": {"@types/react": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "node_modules/@radix-ui/react-focus-scope": {"version": "1.1.0", "license": "MIT", "dependencies": {"@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-use-callback-ref": "1.1.0"}, "peerDependencies": {"@types/react": "*", "@types/react-dom": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "node_modules/@radix-ui/react-id": {"version": "1.1.0", "license": "MIT", "dependencies": {"@radix-ui/react-use-layout-effect": "1.1.0"}, "peerDependencies": {"@types/react": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "node_modules/@radix-ui/react-label": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-2.1.0.tgz", "integrity": "sha512-peLblDlFw/ngk3UWq0VnYaOLy6agTZZ+MUO/WhVfm14vJGML+xH4FAl2XQGLqdefjNb7ApRg6Yn7U42ZhmYXdw==", "license": "MIT", "dependencies": {"@radix-ui/react-primitive": "2.0.0"}, "peerDependencies": {"@types/react": "*", "@types/react-dom": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "node_modules/@radix-ui/react-menu": {"version": "2.1.1", "license": "MIT", "dependencies": {"@radix-ui/primitive": "1.1.0", "@radix-ui/react-collection": "1.1.0", "@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-context": "1.1.0", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-dismissable-layer": "1.1.0", "@radix-ui/react-focus-guards": "1.1.0", "@radix-ui/react-focus-scope": "1.1.0", "@radix-ui/react-id": "1.1.0", "@radix-ui/react-popper": "1.2.0", "@radix-ui/react-portal": "1.1.1", "@radix-ui/react-presence": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-roving-focus": "1.1.0", "@radix-ui/react-slot": "1.1.0", "@radix-ui/react-use-callback-ref": "1.1.0", "aria-hidden": "^1.1.1", "react-remove-scroll": "2.5.7"}, "peerDependencies": {"@types/react": "*", "@types/react-dom": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "node_modules/@radix-ui/react-menu/node_modules/@radix-ui/react-slot": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/@radix-ui/react-slot/-/react-slot-1.1.0.tgz", "integrity": "sha512-FUCf5XMfmW4dtYl69pdS4DbxKy8nj4M7SafBgPllysxmdachynNflAdp/gCsnYWNDnge6tI9onzMp5ARYc1KNw==", "license": "MIT", "dependencies": {"@radix-ui/react-compose-refs": "1.1.0"}, "peerDependencies": {"@types/react": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "node_modules/@radix-ui/react-menu/node_modules/react-remove-scroll": {"version": "2.5.7", "license": "MIT", "dependencies": {"react-remove-scroll-bar": "^2.3.4", "react-style-singleton": "^2.2.1", "tslib": "^2.1.0", "use-callback-ref": "^1.3.0", "use-sidecar": "^1.1.2"}, "engines": {"node": ">=10"}, "peerDependencies": {"@types/react": "^16.8.0 || ^17.0.0 || ^18.0.0", "react": "^16.8.0 || ^17.0.0 || ^18.0.0"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "node_modules/@radix-ui/react-popover": {"version": "1.1.1", "license": "MIT", "dependencies": {"@radix-ui/primitive": "1.1.0", "@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-context": "1.1.0", "@radix-ui/react-dismissable-layer": "1.1.0", "@radix-ui/react-focus-guards": "1.1.0", "@radix-ui/react-focus-scope": "1.1.0", "@radix-ui/react-id": "1.1.0", "@radix-ui/react-popper": "1.2.0", "@radix-ui/react-portal": "1.1.1", "@radix-ui/react-presence": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-slot": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0", "aria-hidden": "^1.1.1", "react-remove-scroll": "2.5.7"}, "peerDependencies": {"@types/react": "*", "@types/react-dom": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "node_modules/@radix-ui/react-popover/node_modules/@radix-ui/react-slot": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/@radix-ui/react-slot/-/react-slot-1.1.0.tgz", "integrity": "sha512-FUCf5XMfmW4dtYl69pdS4DbxKy8nj4M7SafBgPllysxmdachynNflAdp/gCsnYWNDnge6tI9onzMp5ARYc1KNw==", "license": "MIT", "dependencies": {"@radix-ui/react-compose-refs": "1.1.0"}, "peerDependencies": {"@types/react": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "node_modules/@radix-ui/react-popover/node_modules/react-remove-scroll": {"version": "2.5.7", "license": "MIT", "dependencies": {"react-remove-scroll-bar": "^2.3.4", "react-style-singleton": "^2.2.1", "tslib": "^2.1.0", "use-callback-ref": "^1.3.0", "use-sidecar": "^1.1.2"}, "engines": {"node": ">=10"}, "peerDependencies": {"@types/react": "^16.8.0 || ^17.0.0 || ^18.0.0", "react": "^16.8.0 || ^17.0.0 || ^18.0.0"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "node_modules/@radix-ui/react-popper": {"version": "1.2.0", "license": "MIT", "dependencies": {"@floating-ui/react-dom": "^2.0.0", "@radix-ui/react-arrow": "1.1.0", "@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-context": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-use-callback-ref": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0", "@radix-ui/react-use-rect": "1.1.0", "@radix-ui/react-use-size": "1.1.0", "@radix-ui/rect": "1.1.0"}, "peerDependencies": {"@types/react": "*", "@types/react-dom": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "node_modules/@radix-ui/react-portal": {"version": "1.1.1", "license": "MIT", "dependencies": {"@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-use-layout-effect": "1.1.0"}, "peerDependencies": {"@types/react": "*", "@types/react-dom": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "node_modules/@radix-ui/react-presence": {"version": "1.1.0", "license": "MIT", "dependencies": {"@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0"}, "peerDependencies": {"@types/react": "*", "@types/react-dom": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "node_modules/@radix-ui/react-primitive": {"version": "2.0.0", "license": "MIT", "dependencies": {"@radix-ui/react-slot": "1.1.0"}, "peerDependencies": {"@types/react": "*", "@types/react-dom": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "node_modules/@radix-ui/react-primitive/node_modules/@radix-ui/react-slot": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/@radix-ui/react-slot/-/react-slot-1.1.0.tgz", "integrity": "sha512-FUCf5XMfmW4dtYl69pdS4DbxKy8nj4M7SafBgPllysxmdachynNflAdp/gCsnYWNDnge6tI9onzMp5ARYc1KNw==", "license": "MIT", "dependencies": {"@radix-ui/react-compose-refs": "1.1.0"}, "peerDependencies": {"@types/react": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "node_modules/@radix-ui/react-progress": {"version": "1.1.7", "resolved": "https://registry.npmjs.org/@radix-ui/react-progress/-/react-progress-1.1.7.tgz", "integrity": "sha512-vPdg/tF6YC/ynuBIJlk1mm7Le0VgW6ub6J2UWnTQ7/D23KXcPI1qy+0vBkgKgd38RCMJavBXpB83HPNFMTb0Fg==", "dependencies": {"@radix-ui/react-context": "1.1.2", "@radix-ui/react-primitive": "2.1.3"}, "peerDependencies": {"@types/react": "*", "@types/react-dom": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "node_modules/@radix-ui/react-progress/node_modules/@radix-ui/react-compose-refs": {"version": "1.1.2", "resolved": "https://registry.npmjs.org/@radix-ui/react-compose-refs/-/react-compose-refs-1.1.2.tgz", "integrity": "sha512-z4eqJvfiNnFMHIIvXP3CY57y2WJs5g2v3X0zm9mEJkrkNv4rDxu+sg9Jh8EkXyeqBkB7SOcboo9dMVqhyrACIg==", "peerDependencies": {"@types/react": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "node_modules/@radix-ui/react-progress/node_modules/@radix-ui/react-context": {"version": "1.1.2", "resolved": "https://registry.npmjs.org/@radix-ui/react-context/-/react-context-1.1.2.tgz", "integrity": "sha512-jCi/QKUM2r1Ju5a3J64TH2A5SpKAgh0LpknyqdQ4m6DCV0xJ2HG1xARRwNGPQfi1SLdLWZ1OJz6F4OMBBNiGJA==", "peerDependencies": {"@types/react": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "node_modules/@radix-ui/react-progress/node_modules/@radix-ui/react-primitive": {"version": "2.1.3", "resolved": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-2.1.3.tgz", "integrity": "sha512-m9gTwRkhy2lvCPe6QJp4d3G1TYEUHn/FzJUtq9MjH46an1wJU+GdoGC5VLof8RX8Ft/DlpshApkhswDLZzHIcQ==", "dependencies": {"@radix-ui/react-slot": "1.2.3"}, "peerDependencies": {"@types/react": "*", "@types/react-dom": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "node_modules/@radix-ui/react-progress/node_modules/@radix-ui/react-slot": {"version": "1.2.3", "resolved": "https://registry.npmjs.org/@radix-ui/react-slot/-/react-slot-1.2.3.tgz", "integrity": "sha512-aeNmHnBxbi2St0au6VBVC7JXFlhLlOnvIIlePNniyUNAClzmtAUEY8/pBiK3iHjufOlwA+c20/8jngo7xcrg8A==", "dependencies": {"@radix-ui/react-compose-refs": "1.1.2"}, "peerDependencies": {"@types/react": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "node_modules/@radix-ui/react-roving-focus": {"version": "1.1.0", "license": "MIT", "dependencies": {"@radix-ui/primitive": "1.1.0", "@radix-ui/react-collection": "1.1.0", "@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-context": "1.1.0", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-id": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-use-callback-ref": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"@types/react": "*", "@types/react-dom": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "node_modules/@radix-ui/react-select": {"version": "2.1.2", "resolved": "https://registry.npmjs.org/@radix-ui/react-select/-/react-select-2.1.2.tgz", "integrity": "sha512-rZJtWmorC7dFRi0owDmoijm6nSJH1tVw64QGiNIZ9PNLyBDtG+iAq+XGsya052At4BfarzY/Dhv9wrrUr6IMZA==", "license": "MIT", "dependencies": {"@radix-ui/number": "1.1.0", "@radix-ui/primitive": "1.1.0", "@radix-ui/react-collection": "1.1.0", "@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-dismissable-layer": "1.1.1", "@radix-ui/react-focus-guards": "1.1.1", "@radix-ui/react-focus-scope": "1.1.0", "@radix-ui/react-id": "1.1.0", "@radix-ui/react-popper": "1.2.0", "@radix-ui/react-portal": "1.1.2", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-slot": "1.1.0", "@radix-ui/react-use-callback-ref": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0", "@radix-ui/react-use-previous": "1.1.0", "@radix-ui/react-visually-hidden": "1.1.0", "aria-hidden": "^1.1.1", "react-remove-scroll": "2.6.0"}, "peerDependencies": {"@types/react": "*", "@types/react-dom": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "node_modules/@radix-ui/react-select/node_modules/@radix-ui/react-context": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/@radix-ui/react-context/-/react-context-1.1.1.tgz", "integrity": "sha512-UASk9zi+crv9WteK/NU4PLvOoL3OuE6BWVKNF6hPRBtYBDXQ2u5iu3O59zUlJiTVvkyuycnqrztsHVJwcK9K+Q==", "license": "MIT", "peerDependencies": {"@types/react": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "node_modules/@radix-ui/react-select/node_modules/@radix-ui/react-dismissable-layer": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/@radix-ui/react-dismissable-layer/-/react-dismissable-layer-1.1.1.tgz", "integrity": "sha512-QSxg29lfr/xcev6kSz7MAlmDnzbP1eI/Dwn3Tp1ip0KT5CUELsxkekFEMVBEoykI3oV39hKT4TKZzBNMbcTZYQ==", "license": "MIT", "dependencies": {"@radix-ui/primitive": "1.1.0", "@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-use-callback-ref": "1.1.0", "@radix-ui/react-use-escape-keydown": "1.1.0"}, "peerDependencies": {"@types/react": "*", "@types/react-dom": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "node_modules/@radix-ui/react-select/node_modules/@radix-ui/react-focus-guards": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/@radix-ui/react-focus-guards/-/react-focus-guards-1.1.1.tgz", "integrity": "sha512-pSIwfrT1a6sIoDASCSpFwOasEwKTZWDw/iBdtnqKO7v6FeOzYJ7U53cPzYFVR3geGGXgVHaH+CdngrrAzqUGxg==", "license": "MIT", "peerDependencies": {"@types/react": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "node_modules/@radix-ui/react-select/node_modules/@radix-ui/react-portal": {"version": "1.1.2", "resolved": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-1.1.2.tgz", "integrity": "sha512-WeDYLGPxJb/5EGBoedyJbT0MpoULmwnIPMJMSldkuiMsBAv7N1cRdsTWZWht9vpPOiN3qyiGAtbK2is47/uMFg==", "license": "MIT", "dependencies": {"@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-use-layout-effect": "1.1.0"}, "peerDependencies": {"@types/react": "*", "@types/react-dom": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "node_modules/@radix-ui/react-select/node_modules/@radix-ui/react-slot": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/@radix-ui/react-slot/-/react-slot-1.1.0.tgz", "integrity": "sha512-FUCf5XMfmW4dtYl69pdS4DbxKy8nj4M7SafBgPllysxmdachynNflAdp/gCsnYWNDnge6tI9onzMp5ARYc1KNw==", "license": "MIT", "dependencies": {"@radix-ui/react-compose-refs": "1.1.0"}, "peerDependencies": {"@types/react": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "node_modules/@radix-ui/react-separator": {"version": "1.1.7", "resolved": "https://registry.npmjs.org/@radix-ui/react-separator/-/react-separator-1.1.7.tgz", "integrity": "sha512-0HEb8R9E8A+jZjvmFCy/J4xhbXy3TV+9XSnGJ3KvTtjlIUy/YQ/p6UYZvi7YbeoeXdyU9+Y3scizK6hkY37baA==", "dependencies": {"@radix-ui/react-primitive": "2.1.3"}, "peerDependencies": {"@types/react": "*", "@types/react-dom": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "node_modules/@radix-ui/react-separator/node_modules/@radix-ui/react-compose-refs": {"version": "1.1.2", "resolved": "https://registry.npmjs.org/@radix-ui/react-compose-refs/-/react-compose-refs-1.1.2.tgz", "integrity": "sha512-z4eqJvfiNnFMHIIvXP3CY57y2WJs5g2v3X0zm9mEJkrkNv4rDxu+sg9Jh8EkXyeqBkB7SOcboo9dMVqhyrACIg==", "peerDependencies": {"@types/react": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "node_modules/@radix-ui/react-separator/node_modules/@radix-ui/react-primitive": {"version": "2.1.3", "resolved": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-2.1.3.tgz", "integrity": "sha512-m9gTwRkhy2lvCPe6QJp4d3G1TYEUHn/FzJUtq9MjH46an1wJU+GdoGC5VLof8RX8Ft/DlpshApkhswDLZzHIcQ==", "dependencies": {"@radix-ui/react-slot": "1.2.3"}, "peerDependencies": {"@types/react": "*", "@types/react-dom": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "node_modules/@radix-ui/react-separator/node_modules/@radix-ui/react-slot": {"version": "1.2.3", "resolved": "https://registry.npmjs.org/@radix-ui/react-slot/-/react-slot-1.2.3.tgz", "integrity": "sha512-aeNmHnBxbi2St0au6VBVC7JXFlhLlOnvIIlePNniyUNAClzmtAUEY8/pBiK3iHjufOlwA+c20/8jngo7xcrg8A==", "dependencies": {"@radix-ui/react-compose-refs": "1.1.2"}, "peerDependencies": {"@types/react": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "node_modules/@radix-ui/react-slot": {"version": "1.2.2", "resolved": "https://registry.npmjs.org/@radix-ui/react-slot/-/react-slot-1.2.2.tgz", "integrity": "sha512-y7TBO4xN4Y94FvcWIOIh18fM4R1A8S4q1jhoz4PNzOoHsFcN8pogcFmZrTYAm4F9VRUrWP/Mw7xSKybIeRI+CQ==", "license": "MIT", "dependencies": {"@radix-ui/react-compose-refs": "1.1.2"}, "peerDependencies": {"@types/react": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "node_modules/@radix-ui/react-slot/node_modules/@radix-ui/react-compose-refs": {"version": "1.1.2", "resolved": "https://registry.npmjs.org/@radix-ui/react-compose-refs/-/react-compose-refs-1.1.2.tgz", "integrity": "sha512-z4eqJvfiNnFMHIIvXP3CY57y2WJs5g2v3X0zm9mEJkrkNv4rDxu+sg9Jh8EkXyeqBkB7SOcboo9dMVqhyrACIg==", "license": "MIT", "peerDependencies": {"@types/react": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "node_modules/@radix-ui/react-tabs": {"version": "1.1.0", "license": "MIT", "dependencies": {"@radix-ui/primitive": "1.1.0", "@radix-ui/react-context": "1.1.0", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-id": "1.1.0", "@radix-ui/react-presence": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-roving-focus": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"@types/react": "*", "@types/react-dom": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "node_modules/@radix-ui/react-toast": {"version": "1.2.1", "license": "MIT", "dependencies": {"@radix-ui/primitive": "1.1.0", "@radix-ui/react-collection": "1.1.0", "@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-context": "1.1.0", "@radix-ui/react-dismissable-layer": "1.1.0", "@radix-ui/react-portal": "1.1.1", "@radix-ui/react-presence": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-use-callback-ref": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0", "@radix-ui/react-visually-hidden": "1.1.0"}, "peerDependencies": {"@types/react": "*", "@types/react-dom": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "node_modules/@radix-ui/react-tooltip": {"version": "1.1.4", "resolved": "https://registry.npmjs.org/@radix-ui/react-tooltip/-/react-tooltip-1.1.4.tgz", "integrity": "sha512-QpObUH/ZlpaO4YgHSaYzrLO2VuO+ZBFFgGzjMUPwtiYnAzzNNDPJeEGRrT7qNOrWm/Jr08M1vlp+vTHtnSQ0Uw==", "license": "MIT", "dependencies": {"@radix-ui/primitive": "1.1.0", "@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-dismissable-layer": "1.1.1", "@radix-ui/react-id": "1.1.0", "@radix-ui/react-popper": "1.2.0", "@radix-ui/react-portal": "1.1.2", "@radix-ui/react-presence": "1.1.1", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-slot": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0", "@radix-ui/react-visually-hidden": "1.1.0"}, "peerDependencies": {"@types/react": "*", "@types/react-dom": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "node_modules/@radix-ui/react-tooltip/node_modules/@radix-ui/react-context": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/@radix-ui/react-context/-/react-context-1.1.1.tgz", "integrity": "sha512-UASk9zi+crv9WteK/NU4PLvOoL3OuE6BWVKNF6hPRBtYBDXQ2u5iu3O59zUlJiTVvkyuycnqrztsHVJwcK9K+Q==", "license": "MIT", "peerDependencies": {"@types/react": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "node_modules/@radix-ui/react-tooltip/node_modules/@radix-ui/react-dismissable-layer": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/@radix-ui/react-dismissable-layer/-/react-dismissable-layer-1.1.1.tgz", "integrity": "sha512-QSxg29lfr/xcev6kSz7MAlmDnzbP1eI/Dwn3Tp1ip0KT5CUELsxkekFEMVBEoykI3oV39hKT4TKZzBNMbcTZYQ==", "license": "MIT", "dependencies": {"@radix-ui/primitive": "1.1.0", "@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-use-callback-ref": "1.1.0", "@radix-ui/react-use-escape-keydown": "1.1.0"}, "peerDependencies": {"@types/react": "*", "@types/react-dom": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "node_modules/@radix-ui/react-tooltip/node_modules/@radix-ui/react-portal": {"version": "1.1.2", "resolved": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-1.1.2.tgz", "integrity": "sha512-WeDYLGPxJb/5EGBoedyJbT0MpoULmwnIPMJMSldkuiMsBAv7N1cRdsTWZWht9vpPOiN3qyiGAtbK2is47/uMFg==", "license": "MIT", "dependencies": {"@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-use-layout-effect": "1.1.0"}, "peerDependencies": {"@types/react": "*", "@types/react-dom": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "node_modules/@radix-ui/react-tooltip/node_modules/@radix-ui/react-presence": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-1.1.1.tgz", "integrity": "sha512-IeFXVi4YS1K0wVZzXNrbaaUvIJ3qdY+/Ih4eHFhWA9SwGR9UDX7Ck8abvL57C4cv3wwMvUE0OG69Qc3NCcTe/A==", "license": "MIT", "dependencies": {"@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0"}, "peerDependencies": {"@types/react": "*", "@types/react-dom": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "node_modules/@radix-ui/react-tooltip/node_modules/@radix-ui/react-slot": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/@radix-ui/react-slot/-/react-slot-1.1.0.tgz", "integrity": "sha512-FUCf5XMfmW4dtYl69pdS4DbxKy8nj4M7SafBgPllysxmdachynNflAdp/gCsnYWNDnge6tI9onzMp5ARYc1KNw==", "license": "MIT", "dependencies": {"@radix-ui/react-compose-refs": "1.1.0"}, "peerDependencies": {"@types/react": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "node_modules/@radix-ui/react-use-callback-ref": {"version": "1.1.0", "license": "MIT", "peerDependencies": {"@types/react": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "node_modules/@radix-ui/react-use-controllable-state": {"version": "1.1.0", "license": "MIT", "dependencies": {"@radix-ui/react-use-callback-ref": "1.1.0"}, "peerDependencies": {"@types/react": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "node_modules/@radix-ui/react-use-effect-event": {"version": "0.0.2", "resolved": "https://registry.npmjs.org/@radix-ui/react-use-effect-event/-/react-use-effect-event-0.0.2.tgz", "integrity": "sha512-Qp8WbZOBe+blgpuUT+lw2xheLP8q0oatc9UpmiemEICxGvFLYmHm9QowVZGHtJlGbS6A6yJ3iViad/2cVjnOiA==", "license": "MIT", "dependencies": {"@radix-ui/react-use-layout-effect": "1.1.1"}, "peerDependencies": {"@types/react": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "node_modules/@radix-ui/react-use-effect-event/node_modules/@radix-ui/react-use-layout-effect": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/@radix-ui/react-use-layout-effect/-/react-use-layout-effect-1.1.1.tgz", "integrity": "sha512-RbJRS4UWQFkzHTTwVymMTUv8EqYhOp8dOOviLj2ugtTiXRaRQS7GLGxZTLL1jWhMeoSCf5zmcZkqTl9IiYfXcQ==", "license": "MIT", "peerDependencies": {"@types/react": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "node_modules/@radix-ui/react-use-escape-keydown": {"version": "1.1.0", "license": "MIT", "dependencies": {"@radix-ui/react-use-callback-ref": "1.1.0"}, "peerDependencies": {"@types/react": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "node_modules/@radix-ui/react-use-layout-effect": {"version": "1.1.0", "license": "MIT", "peerDependencies": {"@types/react": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "node_modules/@radix-ui/react-use-previous": {"version": "1.1.0", "license": "MIT", "peerDependencies": {"@types/react": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "node_modules/@radix-ui/react-use-rect": {"version": "1.1.0", "license": "MIT", "dependencies": {"@radix-ui/rect": "1.1.0"}, "peerDependencies": {"@types/react": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "node_modules/@radix-ui/react-use-size": {"version": "1.1.0", "license": "MIT", "dependencies": {"@radix-ui/react-use-layout-effect": "1.1.0"}, "peerDependencies": {"@types/react": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "node_modules/@radix-ui/react-visually-hidden": {"version": "1.1.0", "license": "MIT", "dependencies": {"@radix-ui/react-primitive": "2.0.0"}, "peerDependencies": {"@types/react": "*", "@types/react-dom": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "node_modules/@radix-ui/rect": {"version": "1.1.0", "license": "MIT"}, "node_modules/@react-pdf/fns": {"version": "2.2.1", "license": "MIT", "dependencies": {"@babel/runtime": "^7.20.13"}}, "node_modules/@react-pdf/font": {"version": "2.5.1", "license": "MIT", "dependencies": {"@babel/runtime": "^7.20.13", "@react-pdf/types": "^2.5.0", "cross-fetch": "^3.1.5", "fontkit": "^2.0.2", "is-url": "^1.2.4"}}, "node_modules/@react-pdf/image": {"version": "2.3.6", "license": "MIT", "dependencies": {"@babel/runtime": "^7.20.13", "@react-pdf/png-js": "^2.3.1", "cross-fetch": "^3.1.5", "jay-peg": "^1.0.2"}}, "node_modules/@react-pdf/layout": {"version": "3.11.5", "license": "MIT", "dependencies": {"@babel/runtime": "^7.20.13", "@react-pdf/fns": "2.2.1", "@react-pdf/image": "^2.3.6", "@react-pdf/pdfkit": "^3.1.9", "@react-pdf/primitives": "^3.1.1", "@react-pdf/stylesheet": "^4.2.4", "@react-pdf/textkit": "^4.4.1", "@react-pdf/types": "^2.4.1", "cross-fetch": "^3.1.5", "emoji-regex": "^10.3.0", "queue": "^6.0.1", "yoga-layout": "^2.0.1"}}, "node_modules/@react-pdf/pdfkit": {"version": "3.1.10", "license": "MIT", "dependencies": {"@babel/runtime": "^7.20.13", "@react-pdf/png-js": "^2.3.1", "browserify-zlib": "^0.2.0", "crypto-js": "^4.2.0", "fontkit": "^2.0.2", "jay-peg": "^1.0.2", "vite-compatible-readable-stream": "^3.6.1"}}, "node_modules/@react-pdf/png-js": {"version": "2.3.1", "license": "MIT", "dependencies": {"browserify-zlib": "^0.2.0"}}, "node_modules/@react-pdf/primitives": {"version": "3.1.1", "license": "MIT"}, "node_modules/@react-pdf/render": {"version": "3.4.4", "license": "MIT", "dependencies": {"@babel/runtime": "^7.20.13", "@react-pdf/fns": "2.2.1", "@react-pdf/primitives": "^3.1.1", "@react-pdf/textkit": "^4.4.1", "@react-pdf/types": "^2.5.0", "abs-svg-path": "^0.1.1", "color-string": "^1.9.1", "normalize-svg-path": "^1.1.0", "parse-svg-path": "^0.1.2", "svg-arc-to-cubic-bezier": "^3.2.0"}}, "node_modules/@react-pdf/renderer": {"version": "3.1.12", "license": "MIT", "dependencies": {"@babel/runtime": "^7.20.13", "@react-pdf/font": "^2.3.6", "@react-pdf/layout": "^3.6.2", "@react-pdf/pdfkit": "^3.0.2", "@react-pdf/primitives": "^3.0.0", "@react-pdf/render": "^3.2.6", "@react-pdf/types": "^2.3.3", "events": "^3.3.0", "object-assign": "^4.1.1", "prop-types": "^15.6.2", "queue": "^6.0.1", "scheduler": "^0.17.0"}, "peerDependencies": {"react": "^16.8.6 || ^17.0.0 || ^18.0.0"}}, "node_modules/@react-pdf/stylesheet": {"version": "4.2.5", "license": "MIT", "dependencies": {"@babel/runtime": "^7.20.13", "@react-pdf/fns": "2.2.1", "@react-pdf/types": "^2.5.0", "color-string": "^1.9.1", "hsl-to-hex": "^1.0.0", "media-engine": "^1.0.3", "postcss-value-parser": "^4.1.0"}}, "node_modules/@react-pdf/textkit": {"version": "4.4.1", "license": "MIT", "dependencies": {"@babel/runtime": "^7.20.13", "@react-pdf/fns": "2.2.1", "bidi-js": "^1.0.2", "hyphen": "^1.6.4", "unicode-properties": "^1.4.1"}}, "node_modules/@react-pdf/types": {"version": "2.5.0", "license": "MIT"}, "node_modules/@reduxjs/toolkit": {"version": "1.9.5", "license": "MIT", "dependencies": {"immer": "^9.0.21", "redux": "^4.2.1", "redux-thunk": "^2.4.2", "reselect": "^4.1.8"}, "peerDependencies": {"react": "^16.9.0 || ^17.0.0 || ^18", "react-redux": "^7.2.1 || ^8.0.2"}, "peerDependenciesMeta": {"react": {"optional": true}, "react-redux": {"optional": true}}}, "node_modules/@restart/hooks": {"version": "0.4.16", "resolved": "https://registry.npmjs.org/@restart/hooks/-/hooks-0.4.16.tgz", "integrity": "sha512-f7aCv7c+nU/3mF7NWLtVVr0Ra80RqsO89hO72r+Y/nvQr5+q0UFGkocElTH6MJApvReVh6JHUFYn2cw1WdHF3w==", "license": "MIT", "dependencies": {"dequal": "^2.0.3"}, "peerDependencies": {"react": ">=16.8.0"}}, "node_modules/@rushstack/eslint-patch": {"version": "1.10.4", "license": "MIT"}, "node_modules/@swc/counter": {"version": "0.1.3", "license": "Apache-2.0"}, "node_modules/@swc/helpers": {"version": "0.5.12", "license": "Apache-2.0", "dependencies": {"tslib": "^2.4.0"}}, "node_modules/@tanstack/query-core": {"version": "5.74.4", "resolved": "https://registry.npmjs.org/@tanstack/query-core/-/query-core-5.74.4.tgz", "integrity": "sha512-YuG0A0+3i9b2Gfo9fkmNnkUWh5+5cFhWBN0pJAHkHilTx6A0nv8kepkk4T4GRt4e5ahbtFj2eTtkiPcVU1xO4A==", "license": "MIT", "funding": {"type": "github", "url": "https://github.com/sponsors/tanner<PERSON>ley"}}, "node_modules/@tanstack/react-query": {"version": "5.74.4", "resolved": "https://registry.npmjs.org/@tanstack/react-query/-/react-query-5.74.4.tgz", "integrity": "sha512-mAbxw60d4ffQ4qmRYfkO1xzRBPUEf/72Dgo3qqea0J66nIKuDTLEqQt0ku++SDFlMGMnB6uKDnEG1xD/TDse4Q==", "license": "MIT", "dependencies": {"@tanstack/query-core": "5.74.4"}, "funding": {"type": "github", "url": "https://github.com/sponsors/tanner<PERSON>ley"}, "peerDependencies": {"react": "^18 || ^19"}}, "node_modules/@tanstack/react-table": {"version": "8.20.5", "license": "MIT", "dependencies": {"@tanstack/table-core": "8.20.5"}, "engines": {"node": ">=12"}, "funding": {"type": "github", "url": "https://github.com/sponsors/tanner<PERSON>ley"}, "peerDependencies": {"react": ">=16.8", "react-dom": ">=16.8"}}, "node_modules/@tanstack/table-core": {"version": "8.20.5", "license": "MIT", "engines": {"node": ">=12"}, "funding": {"type": "github", "url": "https://github.com/sponsors/tanner<PERSON>ley"}}, "node_modules/@types/cookie": {"version": "0.6.0", "license": "MIT"}, "node_modules/@types/d3-array": {"version": "3.2.1", "license": "MIT"}, "node_modules/@types/d3-color": {"version": "3.1.3", "license": "MIT"}, "node_modules/@types/d3-ease": {"version": "3.0.2", "license": "MIT"}, "node_modules/@types/d3-interpolate": {"version": "3.0.4", "license": "MIT", "dependencies": {"@types/d3-color": "*"}}, "node_modules/@types/d3-path": {"version": "3.1.0", "license": "MIT"}, "node_modules/@types/d3-scale": {"version": "4.0.8", "license": "MIT", "dependencies": {"@types/d3-time": "*"}}, "node_modules/@types/d3-shape": {"version": "3.1.6", "license": "MIT", "dependencies": {"@types/d3-path": "*"}}, "node_modules/@types/d3-time": {"version": "3.0.3", "license": "MIT"}, "node_modules/@types/d3-timer": {"version": "3.0.2", "license": "MIT"}, "node_modules/@types/date-arithmetic": {"version": "4.1.4", "resolved": "https://registry.npmjs.org/@types/date-arithmetic/-/date-arithmetic-4.1.4.tgz", "integrity": "sha512-p9eZ2X9B80iKiTW4ukVj8B4K6q9/+xFtQ5MGYA5HWToY9nL4EkhV9+6ftT2VHpVMEZb5Tv00Iel516bVdO+yRw==", "dev": true, "license": "MIT"}, "node_modules/@types/hoist-non-react-statics": {"version": "3.3.5", "license": "MIT", "dependencies": {"@types/react": "*", "hoist-non-react-statics": "^3.3.0"}}, "node_modules/@types/json-schema": {"version": "7.0.15", "dev": true, "license": "MIT"}, "node_modules/@types/json5": {"version": "0.0.29", "license": "MIT"}, "node_modules/@types/lodash": {"version": "4.17.7", "license": "MIT"}, "node_modules/@types/lodash.mergewith": {"version": "4.6.7", "resolved": "https://registry.npmjs.org/@types/lodash.mergewith/-/lodash.mergewith-4.6.7.tgz", "integrity": "sha512-3m+lkO5CLRRYU0fhGRp7zbsGi6+BZj0uTVSwvcKU+nSlhjA9/QRNfuSGnD2mX6hQA7ZbmcCkzk5h4ZYGOtk14A==", "license": "MIT", "dependencies": {"@types/lodash": "*"}}, "node_modules/@types/luxon": {"version": "3.4.2", "resolved": "https://registry.npmjs.org/@types/luxon/-/luxon-3.4.2.tgz", "integrity": "sha512-TifLZlFudklWlMBfhubvgqTXRzLDI5pCbGa4P8a3wPyUQSW+1xQ5eDsreP9DWHX3tjq1ke96uYG/nwundroWcA==", "dev": true, "license": "MIT"}, "node_modules/@types/node": {"version": "20.2.5", "license": "MIT"}, "node_modules/@types/nodemailer": {"version": "6.4.15", "resolved": "https://registry.npmjs.org/@types/nodemailer/-/nodemailer-6.4.15.tgz", "integrity": "sha512-0EBJxawVNjPkng1zm2vopRctuWVCxk34JcIlRuXSf54habUWdz1FB7wHDqOqvDa8Mtpt0Q3LTXQkAs2LNyK5jQ==", "dev": true, "license": "MIT", "dependencies": {"@types/node": "*"}}, "node_modules/@types/parse-json": {"version": "4.0.2", "license": "MIT"}, "node_modules/@types/prop-types": {"version": "15.7.13", "resolved": "https://registry.npmjs.org/@types/prop-types/-/prop-types-15.7.13.tgz", "integrity": "sha512-hCZTSvwbzWGvhqxp/RqVqwU999pBf2vp7hzIjiYOsl8wqOmUxkQ6ddw1cV3l8811+kdUFus/q4d1Y3E3SyEifA=="}, "node_modules/@types/raf": {"version": "3.4.3", "resolved": "https://registry.npmjs.org/@types/raf/-/raf-3.4.3.tgz", "integrity": "sha512-c4YAvMedbPZ5tEyxzQdMoOhhJ4RD3rngZIdwC2/qDN3d7JpEhB6fiBRKVY1lg5B7Wk+uPBjn5f39j1/2MY1oOw==", "optional": true}, "node_modules/@types/react": {"version": "18.3.4", "license": "MIT", "dependencies": {"@types/prop-types": "*", "csstype": "^3.0.2"}}, "node_modules/@types/react-big-calendar": {"version": "1.16.1", "resolved": "https://registry.npmjs.org/@types/react-big-calendar/-/react-big-calendar-1.16.1.tgz", "integrity": "sha512-pDHFcVWx+BvZbX6U39R4l8c9930vKnfx+09lf4W8r8HuxBDLzGk7Q63ncBmqqnQImEFNDKfwa6MDyu90cfzJ2A==", "dev": true, "license": "MIT", "dependencies": {"@types/date-arithmetic": "*", "@types/prop-types": "*", "@types/react": "*"}}, "node_modules/@types/react-dom": {"version": "18.2.4", "license": "MIT", "dependencies": {"@types/react": "*"}}, "node_modules/@types/react-signature-canvas": {"version": "1.0.5", "resolved": "https://registry.npmjs.org/@types/react-signature-canvas/-/react-signature-canvas-1.0.5.tgz", "integrity": "sha512-jzqEeDR6kxxhg1Ck462zH+C+qMu8hoV5zz0hqnQCp0Q0tHuFEAN2/f0c4NlHyDUfAALX+HPi+eK5SBRdqsEeEg==", "dev": true, "dependencies": {"@types/react": "*", "@types/signature_pad": "*"}}, "node_modules/@types/react-transition-group": {"version": "4.4.11", "license": "MIT", "dependencies": {"@types/react": "*"}}, "node_modules/@types/semver": {"version": "7.5.8", "dev": true, "license": "MIT"}, "node_modules/@types/signature_pad": {"version": "2.3.6", "resolved": "https://registry.npmjs.org/@types/signature_pad/-/signature_pad-2.3.6.tgz", "integrity": "sha512-v3j92gCQJoxomHhd+yaG4Vsf8tRS/XbzWKqDv85UsqjMGy4zhokuwKe4b6vhbgncKkh+thF+gpz6+fypTtnFqQ==", "dev": true}, "node_modules/@types/sinonjs__fake-timers": {"version": "8.1.1", "license": "MIT"}, "node_modules/@types/sizzle": {"version": "2.3.8", "license": "MIT"}, "node_modules/@types/trusted-types": {"version": "2.0.7", "resolved": "https://registry.npmjs.org/@types/trusted-types/-/trusted-types-2.0.7.tgz", "integrity": "sha512-ScaPdn1dQczgbl0QFTeTOmVHFULt394XJgOQNoyVhZ6r2vLnMLJfBPd53SB52T/3G36VI1/g2MZaX0cwDuXsfw==", "optional": true}, "node_modules/@types/use-sync-external-store": {"version": "0.0.3", "license": "MIT"}, "node_modules/@types/uuid": {"version": "10.0.0", "resolved": "https://registry.npmjs.org/@types/uuid/-/uuid-10.0.0.tgz", "integrity": "sha512-7gqG38EyHgyP1S+7+xomFtL+ZNHcKv6DwNaCZmJmo1vgMugyF3TCnXVg4t1uk89mLNwnLtnY3TpOpCOyp1/xHQ==", "dev": true, "license": "MIT"}, "node_modules/@types/warning": {"version": "3.0.3", "resolved": "https://registry.npmjs.org/@types/warning/-/warning-3.0.3.tgz", "integrity": "sha512-D1XC7WK8K+zZEveUPY+cf4+kgauk8N4eHr/XIHXGlGYkHLud6hK9lYfZk1ry1TNh798cZUCgb6MqGEG8DkJt6Q==", "license": "MIT"}, "node_modules/@types/yauzl": {"version": "2.10.3", "license": "MIT", "optional": true, "dependencies": {"@types/node": "*"}}, "node_modules/@types/yup": {"version": "0.32.0", "resolved": "https://registry.npmjs.org/@types/yup/-/yup-0.32.0.tgz", "integrity": "sha512-Gr2lllWTDxGVYHgWfL8szjdedERpNgm44L9BDL2cmcHG7Bfd6taEpiW3ayMFLaYvlJr/6bFXDJdh6L406AGlFg==", "deprecated": "This is a stub types definition. yup provides its own type definitions, so you do not need this installed.", "dependencies": {"yup": "*"}}, "node_modules/@typescript-eslint/eslint-plugin": {"version": "5.59.9", "dev": true, "license": "MIT", "dependencies": {"@eslint-community/regexpp": "^4.4.0", "@typescript-eslint/scope-manager": "5.59.9", "@typescript-eslint/type-utils": "5.59.9", "@typescript-eslint/utils": "5.59.9", "debug": "^4.3.4", "grapheme-splitter": "^1.0.4", "ignore": "^5.2.0", "natural-compare-lite": "^1.4.0", "semver": "^7.3.7", "tsutils": "^3.21.0"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependencies": {"@typescript-eslint/parser": "^5.0.0", "eslint": "^6.0.0 || ^7.0.0 || ^8.0.0"}, "peerDependenciesMeta": {"typescript": {"optional": true}}}, "node_modules/@typescript-eslint/parser": {"version": "5.59.9", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"@typescript-eslint/scope-manager": "5.59.9", "@typescript-eslint/types": "5.59.9", "@typescript-eslint/typescript-estree": "5.59.9", "debug": "^4.3.4"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependencies": {"eslint": "^6.0.0 || ^7.0.0 || ^8.0.0"}, "peerDependenciesMeta": {"typescript": {"optional": true}}}, "node_modules/@typescript-eslint/scope-manager": {"version": "5.59.9", "license": "MIT", "dependencies": {"@typescript-eslint/types": "5.59.9", "@typescript-eslint/visitor-keys": "5.59.9"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}}, "node_modules/@typescript-eslint/type-utils": {"version": "5.59.9", "dev": true, "license": "MIT", "dependencies": {"@typescript-eslint/typescript-estree": "5.59.9", "@typescript-eslint/utils": "5.59.9", "debug": "^4.3.4", "tsutils": "^3.21.0"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependencies": {"eslint": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}}, "node_modules/@typescript-eslint/types": {"version": "5.59.9", "license": "MIT", "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}}, "node_modules/@typescript-eslint/typescript-estree": {"version": "5.59.9", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"@typescript-eslint/types": "5.59.9", "@typescript-eslint/visitor-keys": "5.59.9", "debug": "^4.3.4", "globby": "^11.1.0", "is-glob": "^4.0.3", "semver": "^7.3.7", "tsutils": "^3.21.0"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependenciesMeta": {"typescript": {"optional": true}}}, "node_modules/@typescript-eslint/utils": {"version": "5.59.9", "dev": true, "license": "MIT", "dependencies": {"@eslint-community/eslint-utils": "^4.2.0", "@types/json-schema": "^7.0.9", "@types/semver": "^7.3.12", "@typescript-eslint/scope-manager": "5.59.9", "@typescript-eslint/types": "5.59.9", "@typescript-eslint/typescript-estree": "5.59.9", "eslint-scope": "^5.1.1", "semver": "^7.3.7"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependencies": {"eslint": "^6.0.0 || ^7.0.0 || ^8.0.0"}}, "node_modules/@typescript-eslint/visitor-keys": {"version": "5.59.9", "license": "MIT", "dependencies": {"@typescript-eslint/types": "5.59.9", "eslint-visitor-keys": "^3.3.0"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}}, "node_modules/@wojtekmaj/date-utils": {"version": "1.5.1", "resolved": "https://registry.npmjs.org/@wojtekmaj/date-utils/-/date-utils-1.5.1.tgz", "integrity": "sha512-+i7+JmNiE/3c9FKxzWFi2IjRJ+KzZl1QPu6QNrsgaa2MuBgXvUy4gA1TVzf/JMdIIloB76xSKikTWuyYAIVLww==", "funding": {"url": "https://github.com/wojtekmaj/date-utils?sponsor=1"}}, "node_modules/@zag-js/element-size": {"version": "0.3.2", "resolved": "https://registry.npmjs.org/@zag-js/element-size/-/element-size-0.3.2.tgz", "integrity": "sha512-bVvvigUGvAuj7PCkE5AbzvTJDTw5f3bg9nQdv+ErhVN8SfPPppLJEmmWdxqsRzrHXgx8ypJt/+Ty0kjtISVDsQ==", "license": "MIT"}, "node_modules/@zag-js/focus-visible": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/@zag-js/focus-visible/-/focus-visible-0.2.2.tgz", "integrity": "sha512-0j2gZq8HiZ51z4zNnSkF1iSkqlwRDvdH+son3wHdoz+7IUdMN/5Exd4TxMJ+gq2Of1DiXReYLL9qqh2PdQ4wgA==", "license": "MIT"}, "node_modules/abs-svg-path": {"version": "0.1.1", "license": "MIT"}, "node_modules/acorn": {"version": "8.12.1", "license": "MIT", "bin": {"acorn": "bin/acorn"}, "engines": {"node": ">=0.4.0"}}, "node_modules/acorn-jsx": {"version": "5.3.2", "license": "MIT", "peerDependencies": {"acorn": "^6.0.0 || ^7.0.0 || ^8.0.0"}}, "node_modules/adler-32": {"version": "1.3.1", "license": "Apache-2.0", "engines": {"node": ">=0.8"}}, "node_modules/aggregate-error": {"version": "3.1.0", "license": "MIT", "dependencies": {"clean-stack": "^2.0.0", "indent-string": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/ajv": {"version": "6.12.6", "license": "MIT", "dependencies": {"fast-deep-equal": "^3.1.1", "fast-json-stable-stringify": "^2.0.0", "json-schema-traverse": "^0.4.1", "uri-js": "^4.2.2"}, "funding": {"type": "github", "url": "https://github.com/sponsors/epoberezkin"}}, "node_modules/animate.css": {"version": "4.1.1", "license": "MIT"}, "node_modules/ansi-colors": {"version": "4.1.3", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/ansi-escapes": {"version": "4.3.2", "license": "MIT", "dependencies": {"type-fest": "^0.21.3"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/ansi-regex": {"version": "5.0.1", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/ansi-styles": {"version": "3.2.1", "license": "MIT", "dependencies": {"color-convert": "^1.9.0"}, "engines": {"node": ">=4"}}, "node_modules/any-promise": {"version": "1.3.0", "license": "MIT"}, "node_modules/anymatch": {"version": "3.1.3", "license": "ISC", "dependencies": {"normalize-path": "^3.0.0", "picomatch": "^2.0.4"}, "engines": {"node": ">= 8"}}, "node_modules/arch": {"version": "2.2.0", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT"}, "node_modules/arg": {"version": "5.0.2", "license": "MIT"}, "node_modules/argparse": {"version": "2.0.1", "license": "Python-2.0"}, "node_modules/aria-hidden": {"version": "1.2.4", "license": "MIT", "dependencies": {"tslib": "^2.0.0"}, "engines": {"node": ">=10"}}, "node_modules/aria-query": {"version": "5.1.3", "license": "Apache-2.0", "dependencies": {"deep-equal": "^2.0.5"}}, "node_modules/array-buffer-byte-length": {"version": "1.0.1", "license": "MIT", "dependencies": {"call-bind": "^1.0.5", "is-array-buffer": "^3.0.4"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/array-includes": {"version": "3.1.8", "license": "MIT", "dependencies": {"call-bind": "^1.0.7", "define-properties": "^1.2.1", "es-abstract": "^1.23.2", "es-object-atoms": "^1.0.0", "get-intrinsic": "^1.2.4", "is-string": "^1.0.7"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/array-union": {"version": "2.1.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/array.prototype.findlast": {"version": "1.2.5", "license": "MIT", "dependencies": {"call-bind": "^1.0.7", "define-properties": "^1.2.1", "es-abstract": "^1.23.2", "es-errors": "^1.3.0", "es-object-atoms": "^1.0.0", "es-shim-unscopables": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/array.prototype.findlastindex": {"version": "1.2.5", "license": "MIT", "dependencies": {"call-bind": "^1.0.7", "define-properties": "^1.2.1", "es-abstract": "^1.23.2", "es-errors": "^1.3.0", "es-object-atoms": "^1.0.0", "es-shim-unscopables": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/array.prototype.flat": {"version": "1.3.2", "license": "MIT", "dependencies": {"call-bind": "^1.0.2", "define-properties": "^1.2.0", "es-abstract": "^1.22.1", "es-shim-unscopables": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/array.prototype.flatmap": {"version": "1.3.2", "license": "MIT", "dependencies": {"call-bind": "^1.0.2", "define-properties": "^1.2.0", "es-abstract": "^1.22.1", "es-shim-unscopables": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/array.prototype.tosorted": {"version": "1.1.4", "license": "MIT", "dependencies": {"call-bind": "^1.0.7", "define-properties": "^1.2.1", "es-abstract": "^1.23.3", "es-errors": "^1.3.0", "es-shim-unscopables": "^1.0.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/arraybuffer.prototype.slice": {"version": "1.0.3", "license": "MIT", "dependencies": {"array-buffer-byte-length": "^1.0.1", "call-bind": "^1.0.5", "define-properties": "^1.2.1", "es-abstract": "^1.22.3", "es-errors": "^1.2.1", "get-intrinsic": "^1.2.3", "is-array-buffer": "^3.0.4", "is-shared-array-buffer": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/asn1": {"version": "0.2.6", "license": "MIT", "dependencies": {"safer-buffer": "~2.1.0"}}, "node_modules/assert-plus": {"version": "1.0.0", "license": "MIT", "engines": {"node": ">=0.8"}}, "node_modules/ast-types-flow": {"version": "0.0.8", "license": "MIT"}, "node_modules/astral-regex": {"version": "2.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/async": {"version": "3.2.6", "license": "MIT"}, "node_modules/asynckit": {"version": "0.4.0", "license": "MIT"}, "node_modules/at-least-node": {"version": "1.0.0", "license": "ISC", "engines": {"node": ">= 4.0.0"}}, "node_modules/atob": {"version": "2.1.2", "resolved": "https://registry.npmjs.org/atob/-/atob-2.1.2.tgz", "integrity": "sha512-Wm6ukoaOGJi/73p/cl2GvLjTI5JM1k/O14isD73YML8StrH/7/lRFgmg8nICZgD3bZZvjwCGxtMOD3wWNAu8cg==", "bin": {"atob": "bin/atob.js"}, "engines": {"node": ">= 4.5.0"}}, "node_modules/attr-accept": {"version": "2.2.5", "resolved": "https://registry.npmjs.org/attr-accept/-/attr-accept-2.2.5.tgz", "integrity": "sha512-0bDNnY/u6pPwHDMoF0FieU354oBi0a8rD9FcsLwzcGWbc8KS8KPIi7y+s13OlVY+gMWc/9xEMUgNE6Qm8ZllYQ==", "engines": {"node": ">=4"}}, "node_modules/autoprefixer": {"version": "10.4.14", "funding": [{"type": "opencollective", "url": "https://opencollective.com/postcss/"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/autoprefixer"}], "license": "MIT", "dependencies": {"browserslist": "^4.21.5", "caniuse-lite": "^1.0.30001464", "fraction.js": "^4.2.0", "normalize-range": "^0.1.2", "picocolors": "^1.0.0", "postcss-value-parser": "^4.2.0"}, "bin": {"autoprefixer": "bin/autoprefixer"}, "engines": {"node": "^10 || ^12 || >=14"}, "peerDependencies": {"postcss": "^8.1.0"}}, "node_modules/available-typed-arrays": {"version": "1.0.7", "license": "MIT", "dependencies": {"possible-typed-array-names": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/aws-sign2": {"version": "0.7.0", "license": "Apache-2.0", "engines": {"node": "*"}}, "node_modules/aws4": {"version": "1.13.1", "license": "MIT"}, "node_modules/axe-core": {"version": "4.10.0", "license": "MPL-2.0", "engines": {"node": ">=4"}}, "node_modules/axios": {"version": "1.4.0", "license": "MIT", "dependencies": {"follow-redirects": "^1.15.0", "form-data": "^4.0.0", "proxy-from-env": "^1.1.0"}}, "node_modules/axobject-query": {"version": "3.1.1", "license": "Apache-2.0", "dependencies": {"deep-equal": "^2.0.5"}}, "node_modules/babel-plugin-macros": {"version": "3.1.0", "license": "MIT", "dependencies": {"@babel/runtime": "^7.12.5", "cosmiconfig": "^7.0.0", "resolve": "^1.19.0"}, "engines": {"node": ">=10", "npm": ">=6"}}, "node_modules/balanced-match": {"version": "1.0.2", "license": "MIT"}, "node_modules/base64-arraybuffer": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/base64-arraybuffer/-/base64-arraybuffer-1.0.2.tgz", "integrity": "sha512-I3yl4r9QB5ZRY3XuJVEPfc2XhZO6YweFPI+UovAzn+8/hb3oJ6lnysaFcjVpkCPfVWFUDvoZ8kmVDP7WyRtYtQ==", "optional": true, "engines": {"node": ">= 0.6.0"}}, "node_modules/base64-js": {"version": "1.5.1", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT"}, "node_modules/bcrypt-pbkdf": {"version": "1.0.2", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"tweetnacl": "^0.14.3"}}, "node_modules/bidi-js": {"version": "1.0.3", "license": "MIT", "dependencies": {"require-from-string": "^2.0.2"}}, "node_modules/binary-extensions": {"version": "2.3.0", "license": "MIT", "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/bintrees": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/bintrees/-/bintrees-1.0.2.tgz", "integrity": "sha512-VOMgTMwjAaUG580SXn3LacVgjurrbMme7ZZNYGSSV7mmtY6QQRh0Eg3pwIcntQ77DErK1L0NxkbetjcoXzVwKw==", "license": "MIT"}, "node_modules/blob-util": {"version": "2.0.2", "license": "Apache-2.0"}, "node_modules/bluebird": {"version": "3.7.2", "license": "MIT"}, "node_modules/brace-expansion": {"version": "1.1.11", "license": "MIT", "dependencies": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "node_modules/braces": {"version": "3.0.3", "license": "MIT", "dependencies": {"fill-range": "^7.1.1"}, "engines": {"node": ">=8"}}, "node_modules/brotli": {"version": "1.3.3", "license": "MIT", "dependencies": {"base64-js": "^1.1.2"}}, "node_modules/browserify-zlib": {"version": "0.2.0", "license": "MIT", "dependencies": {"pako": "~1.0.5"}}, "node_modules/browserslist": {"version": "4.23.3", "funding": [{"type": "opencollective", "url": "https://opencollective.com/browserslist"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/browserslist"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "dependencies": {"caniuse-lite": "^1.0.30001646", "electron-to-chromium": "^1.5.4", "node-releases": "^2.0.18", "update-browserslist-db": "^1.1.0"}, "bin": {"browserslist": "cli.js"}, "engines": {"node": "^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7"}}, "node_modules/btoa": {"version": "1.2.1", "resolved": "https://registry.npmjs.org/btoa/-/btoa-1.2.1.tgz", "integrity": "sha512-SB4/MIGlsiVkMcHmT+pSmIPoNDoHg+7cMzmt3Uxt628MTz2487DKSqK/fuhFBrkuqrYv5UCEnACpF4dTFNKc/g==", "bin": {"btoa": "bin/btoa.js"}, "engines": {"node": ">= 0.4.0"}}, "node_modules/buffer": {"version": "5.7.1", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT", "dependencies": {"base64-js": "^1.3.1", "ieee754": "^1.1.13"}}, "node_modules/buffer-crc32": {"version": "0.2.13", "license": "MIT", "engines": {"node": "*"}}, "node_modules/busboy": {"version": "1.6.0", "dependencies": {"streamsearch": "^1.1.0"}, "engines": {"node": ">=10.16.0"}}, "node_modules/cachedir": {"version": "2.4.0", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/call-bind": {"version": "1.0.7", "license": "MIT", "dependencies": {"es-define-property": "^1.0.0", "es-errors": "^1.3.0", "function-bind": "^1.1.2", "get-intrinsic": "^1.2.4", "set-function-length": "^1.2.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/callsites": {"version": "3.1.0", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/camelcase-css": {"version": "2.0.1", "license": "MIT", "engines": {"node": ">= 6"}}, "node_modules/caniuse-lite": {"version": "1.0.30001653", "funding": [{"type": "opencollective", "url": "https://opencollective.com/browserslist"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/caniuse-lite"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "CC-BY-4.0"}, "node_modules/canvg": {"version": "3.0.11", "resolved": "https://registry.npmjs.org/canvg/-/canvg-3.0.11.tgz", "integrity": "sha512-5ON+q7jCTgMp9cjpu4Jo6XbvfYwSB2Ow3kzHKfIyJfaCAOHLbdKPQqGKgfED/R5B+3TFFfe8pegYA+b423SRyA==", "optional": true, "dependencies": {"@babel/runtime": "^7.12.5", "@types/raf": "^3.4.0", "core-js": "^3.8.3", "raf": "^3.4.1", "regenerator-runtime": "^0.13.7", "rgbcolor": "^1.0.1", "stackblur-canvas": "^2.0.0", "svg-pathdata": "^6.0.3"}, "engines": {"node": ">=10.0.0"}}, "node_modules/caseless": {"version": "0.12.0", "license": "Apache-2.0"}, "node_modules/cfb": {"version": "1.2.2", "license": "Apache-2.0", "dependencies": {"adler-32": "~1.3.0", "crc-32": "~1.2.0"}, "engines": {"node": ">=0.8"}}, "node_modules/chalk": {"version": "2.4.2", "license": "MIT", "dependencies": {"ansi-styles": "^3.2.1", "escape-string-regexp": "^1.0.5", "supports-color": "^5.3.0"}, "engines": {"node": ">=4"}}, "node_modules/chalk/node_modules/escape-string-regexp": {"version": "1.0.5", "license": "MIT", "engines": {"node": ">=0.8.0"}}, "node_modules/chart.js": {"version": "4.4.0", "license": "MIT", "dependencies": {"@kurkle/color": "^0.3.0"}, "engines": {"pnpm": ">=7"}}, "node_modules/check-more-types": {"version": "2.24.0", "license": "MIT", "engines": {"node": ">= 0.8.0"}}, "node_modules/chokidar": {"version": "3.6.0", "license": "MIT", "dependencies": {"anymatch": "~3.1.2", "braces": "~3.0.2", "glob-parent": "~5.1.2", "is-binary-path": "~2.1.0", "is-glob": "~4.0.1", "normalize-path": "~3.0.0", "readdirp": "~3.6.0"}, "engines": {"node": ">= 8.10.0"}, "funding": {"url": "https://paulmillr.com/funding/"}, "optionalDependencies": {"fsevents": "~2.3.2"}}, "node_modules/chokidar/node_modules/glob-parent": {"version": "5.1.2", "license": "ISC", "dependencies": {"is-glob": "^4.0.1"}, "engines": {"node": ">= 6"}}, "node_modules/ci-info": {"version": "3.9.0", "funding": [{"type": "github", "url": "https://github.com/sponsors/sibir<PERSON>-s"}], "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/class-variance-authority": {"version": "0.7.0", "license": "Apache-2.0", "dependencies": {"clsx": "2.0.0"}, "funding": {"url": "https://joebell.co.uk"}}, "node_modules/class-variance-authority/node_modules/clsx": {"version": "2.0.0", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/clean-stack": {"version": "2.2.0", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/cli-cursor": {"version": "3.1.0", "license": "MIT", "dependencies": {"restore-cursor": "^3.1.0"}, "engines": {"node": ">=8"}}, "node_modules/cli-table3": {"version": "0.6.5", "license": "MIT", "dependencies": {"string-width": "^4.2.0"}, "engines": {"node": "10.* || >= 12.*"}, "optionalDependencies": {"@colors/colors": "1.5.0"}}, "node_modules/cli-truncate": {"version": "3.1.0", "dev": true, "license": "MIT", "dependencies": {"slice-ansi": "^5.0.0", "string-width": "^5.0.0"}, "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/cli-truncate/node_modules/ansi-regex": {"version": "6.0.1", "dev": true, "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/ansi-regex?sponsor=1"}}, "node_modules/cli-truncate/node_modules/emoji-regex": {"version": "9.2.2", "dev": true, "license": "MIT"}, "node_modules/cli-truncate/node_modules/string-width": {"version": "5.1.2", "dev": true, "license": "MIT", "dependencies": {"eastasianwidth": "^0.2.0", "emoji-regex": "^9.2.2", "strip-ansi": "^7.0.1"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/cli-truncate/node_modules/strip-ansi": {"version": "7.1.0", "dev": true, "license": "MIT", "dependencies": {"ansi-regex": "^6.0.1"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/strip-ansi?sponsor=1"}}, "node_modules/client-only": {"version": "0.0.1", "license": "MIT"}, "node_modules/clone": {"version": "2.1.2", "license": "MIT", "engines": {"node": ">=0.8"}}, "node_modules/clsx": {"version": "2.1.1", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/cmdk": {"version": "0.2.1", "license": "MIT", "dependencies": {"@radix-ui/react-dialog": "1.0.0"}, "peerDependencies": {"react": "^18.0.0", "react-dom": "^18.0.0"}}, "node_modules/cmdk/node_modules/@radix-ui/primitive": {"version": "1.0.0", "license": "MIT", "dependencies": {"@babel/runtime": "^7.13.10"}}, "node_modules/cmdk/node_modules/@radix-ui/react-compose-refs": {"version": "1.0.0", "license": "MIT", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}}, "node_modules/cmdk/node_modules/@radix-ui/react-context": {"version": "1.0.0", "license": "MIT", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}}, "node_modules/cmdk/node_modules/@radix-ui/react-dialog": {"version": "1.0.0", "license": "MIT", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-dismissable-layer": "1.0.0", "@radix-ui/react-focus-guards": "1.0.0", "@radix-ui/react-focus-scope": "1.0.0", "@radix-ui/react-id": "1.0.0", "@radix-ui/react-portal": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-primitive": "1.0.0", "@radix-ui/react-slot": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0", "aria-hidden": "^1.1.1", "react-remove-scroll": "2.5.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}}, "node_modules/cmdk/node_modules/@radix-ui/react-dismissable-layer": {"version": "1.0.0", "license": "MIT", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-primitive": "1.0.0", "@radix-ui/react-use-callback-ref": "1.0.0", "@radix-ui/react-use-escape-keydown": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}}, "node_modules/cmdk/node_modules/@radix-ui/react-focus-guards": {"version": "1.0.0", "license": "MIT", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}}, "node_modules/cmdk/node_modules/@radix-ui/react-focus-scope": {"version": "1.0.0", "license": "MIT", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-primitive": "1.0.0", "@radix-ui/react-use-callback-ref": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}}, "node_modules/cmdk/node_modules/@radix-ui/react-id": {"version": "1.0.0", "license": "MIT", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-use-layout-effect": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}}, "node_modules/cmdk/node_modules/@radix-ui/react-portal": {"version": "1.0.0", "license": "MIT", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}}, "node_modules/cmdk/node_modules/@radix-ui/react-presence": {"version": "1.0.0", "license": "MIT", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}}, "node_modules/cmdk/node_modules/@radix-ui/react-primitive": {"version": "1.0.0", "license": "MIT", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}}, "node_modules/cmdk/node_modules/@radix-ui/react-slot": {"version": "1.0.0", "license": "MIT", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-compose-refs": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}}, "node_modules/cmdk/node_modules/@radix-ui/react-use-callback-ref": {"version": "1.0.0", "license": "MIT", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}}, "node_modules/cmdk/node_modules/@radix-ui/react-use-controllable-state": {"version": "1.0.0", "license": "MIT", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-use-callback-ref": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}}, "node_modules/cmdk/node_modules/@radix-ui/react-use-escape-keydown": {"version": "1.0.0", "license": "MIT", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-use-callback-ref": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}}, "node_modules/cmdk/node_modules/@radix-ui/react-use-layout-effect": {"version": "1.0.0", "license": "MIT", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}}, "node_modules/cmdk/node_modules/react-remove-scroll": {"version": "2.5.4", "license": "MIT", "dependencies": {"react-remove-scroll-bar": "^2.3.3", "react-style-singleton": "^2.2.1", "tslib": "^2.1.0", "use-callback-ref": "^1.3.0", "use-sidecar": "^1.1.2"}, "engines": {"node": ">=10"}, "peerDependencies": {"@types/react": "^16.8.0 || ^17.0.0 || ^18.0.0", "react": "^16.8.0 || ^17.0.0 || ^18.0.0"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "node_modules/codepage": {"version": "1.15.0", "license": "Apache-2.0", "engines": {"node": ">=0.8"}}, "node_modules/color-convert": {"version": "1.9.3", "license": "MIT", "dependencies": {"color-name": "1.1.3"}}, "node_modules/color-name": {"version": "1.1.3", "license": "MIT"}, "node_modules/color-string": {"version": "1.9.1", "license": "MIT", "dependencies": {"color-name": "^1.0.0", "simple-swizzle": "^0.2.2"}}, "node_modules/color2k": {"version": "2.0.3", "resolved": "https://registry.npmjs.org/color2k/-/color2k-2.0.3.tgz", "integrity": "sha512-zW190nQTIoXcGCaU08DvVNFTmQhUpnJfVuAKfWqUQkflXKpaDdpaYoM0iluLS9lgJNHyBF58KKA2FBEwkD7wog==", "license": "MIT"}, "node_modules/colorette": {"version": "2.0.20", "license": "MIT"}, "node_modules/combined-stream": {"version": "1.0.8", "license": "MIT", "dependencies": {"delayed-stream": "~1.0.0"}, "engines": {"node": ">= 0.8"}}, "node_modules/commander": {"version": "6.2.1", "license": "MIT", "engines": {"node": ">= 6"}}, "node_modules/common-tags": {"version": "1.8.2", "license": "MIT", "engines": {"node": ">=4.0.0"}}, "node_modules/compute-scroll-into-view": {"version": "1.0.20", "resolved": "https://registry.npmjs.org/compute-scroll-into-view/-/compute-scroll-into-view-1.0.20.tgz", "integrity": "sha512-UCB0ioiyj8CRjtrvaceBLqqhZCVP+1B8+NWQhmdsm0VXOJtobBCf1dBQmebCCo34qZmUwZfIH2MZLqNHazrfjg==", "license": "MIT"}, "node_modules/concat-map": {"version": "0.0.1", "license": "MIT"}, "node_modules/confusing-browser-globals": {"version": "1.0.11", "dev": true, "license": "MIT"}, "node_modules/convert-source-map": {"version": "1.9.0", "license": "MIT"}, "node_modules/cookie": {"version": "0.6.0", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/cookies-next": {"version": "4.2.1", "license": "MIT", "dependencies": {"@types/cookie": "^0.6.0", "cookie": "^0.6.0"}}, "node_modules/copy-to-clipboard": {"version": "3.3.3", "resolved": "https://registry.npmjs.org/copy-to-clipboard/-/copy-to-clipboard-3.3.3.tgz", "integrity": "sha512-2KV8NhB5JqC3ky0r9PMCAZKbUHSwtEo4CwCs0KXgruG43gX5PMqDEBbVU4OUzw2MuAWUfsuFmWvEKG5QRfSnJA==", "license": "MIT", "dependencies": {"toggle-selection": "^1.0.6"}}, "node_modules/core-js": {"version": "3.43.0", "resolved": "https://registry.npmjs.org/core-js/-/core-js-3.43.0.tgz", "integrity": "sha512-N6wEbTTZSYOY2rYAn85CuvWWkCK6QweMn7/4Nr3w+gDBeBhk/x4EJeY6FPo4QzDoJZxVTv8U7CMvgWk6pOHHqA==", "hasInstallScript": true, "optional": true, "funding": {"type": "opencollective", "url": "https://opencollective.com/core-js"}}, "node_modules/core-util-is": {"version": "1.0.2", "license": "MIT"}, "node_modules/cosmiconfig": {"version": "7.1.0", "license": "MIT", "dependencies": {"@types/parse-json": "^4.0.0", "import-fresh": "^3.2.1", "parse-json": "^5.0.0", "path-type": "^4.0.0", "yaml": "^1.10.0"}, "engines": {"node": ">=10"}}, "node_modules/crc-32": {"version": "1.2.2", "license": "Apache-2.0", "bin": {"crc32": "bin/crc32.njs"}, "engines": {"node": ">=0.8"}}, "node_modules/cross-fetch": {"version": "3.1.8", "license": "MIT", "dependencies": {"node-fetch": "^2.6.12"}}, "node_modules/cross-spawn": {"version": "7.0.3", "license": "MIT", "dependencies": {"path-key": "^3.1.0", "shebang-command": "^2.0.0", "which": "^2.0.1"}, "engines": {"node": ">= 8"}}, "node_modules/crypto-js": {"version": "4.2.0", "license": "MIT"}, "node_modules/css-box-model": {"version": "1.2.1", "resolved": "https://registry.npmjs.org/css-box-model/-/css-box-model-1.2.1.tgz", "integrity": "sha512-a7Vr4Q/kd/aw96bnJG332W9V9LkJO69JRcaCYDUqjp6/z0w6VcZjgAcTbgFxEPfBgdnAwlh3iwu+hLopa+flJw==", "license": "MIT", "dependencies": {"tiny-invariant": "^1.0.6"}}, "node_modules/css-line-break": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/css-line-break/-/css-line-break-2.1.0.tgz", "integrity": "sha512-FHcKFCZcAha3LwfVBhCQbW2nCNbkZXn7KVUJcsT5/P8YmfsVja0FMPJr0B903j/E69HUphKiV9iQArX8SDYA4w==", "optional": true, "dependencies": {"utrie": "^1.0.2"}}, "node_modules/cssesc": {"version": "3.0.0", "license": "MIT", "bin": {"cssesc": "bin/cssesc"}, "engines": {"node": ">=4"}}, "node_modules/csstype": {"version": "3.1.3", "license": "MIT"}, "node_modules/cypress": {"version": "12.14.0", "hasInstallScript": true, "license": "MIT", "dependencies": {"@cypress/request": "^2.88.10", "@cypress/xvfb": "^1.2.4", "@types/node": "^14.14.31", "@types/sinonjs__fake-timers": "8.1.1", "@types/sizzle": "^2.3.2", "arch": "^2.2.0", "blob-util": "^2.0.2", "bluebird": "^3.7.2", "buffer": "^5.6.0", "cachedir": "^2.3.0", "chalk": "^4.1.0", "check-more-types": "^2.24.0", "cli-cursor": "^3.1.0", "cli-table3": "~0.6.1", "commander": "^6.2.1", "common-tags": "^1.8.0", "dayjs": "^1.10.4", "debug": "^4.3.4", "enquirer": "^2.3.6", "eventemitter2": "6.4.7", "execa": "4.1.0", "executable": "^4.1.1", "extract-zip": "2.0.1", "figures": "^3.2.0", "fs-extra": "^9.1.0", "getos": "^3.2.1", "is-ci": "^3.0.0", "is-installed-globally": "~0.4.0", "lazy-ass": "^1.6.0", "listr2": "^3.8.3", "lodash": "^4.17.21", "log-symbols": "^4.0.0", "minimist": "^1.2.8", "ospath": "^1.2.2", "pretty-bytes": "^5.6.0", "proxy-from-env": "1.0.0", "request-progress": "^3.0.0", "semver": "^7.3.2", "supports-color": "^8.1.1", "tmp": "~0.2.1", "untildify": "^4.0.0", "yauzl": "^2.10.0"}, "bin": {"cypress": "bin/cypress"}, "engines": {"node": "^14.0.0 || ^16.0.0 || >=18.0.0"}}, "node_modules/cypress/node_modules/@types/node": {"version": "14.18.63", "license": "MIT"}, "node_modules/cypress/node_modules/ansi-styles": {"version": "4.3.0", "license": "MIT", "dependencies": {"color-convert": "^2.0.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/cypress/node_modules/chalk": {"version": "4.1.2", "license": "MIT", "dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/chalk?sponsor=1"}}, "node_modules/cypress/node_modules/chalk/node_modules/supports-color": {"version": "7.2.0", "license": "MIT", "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/cypress/node_modules/color-convert": {"version": "2.0.1", "license": "MIT", "dependencies": {"color-name": "~1.1.4"}, "engines": {"node": ">=7.0.0"}}, "node_modules/cypress/node_modules/color-name": {"version": "1.1.4", "license": "MIT"}, "node_modules/cypress/node_modules/has-flag": {"version": "4.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/cypress/node_modules/proxy-from-env": {"version": "1.0.0", "license": "MIT"}, "node_modules/cypress/node_modules/supports-color": {"version": "8.1.1", "license": "MIT", "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/supports-color?sponsor=1"}}, "node_modules/d3-array": {"version": "3.2.4", "license": "ISC", "dependencies": {"internmap": "1 - 2"}, "engines": {"node": ">=12"}}, "node_modules/d3-color": {"version": "3.1.0", "license": "ISC", "engines": {"node": ">=12"}}, "node_modules/d3-ease": {"version": "3.0.1", "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=12"}}, "node_modules/d3-format": {"version": "3.1.0", "license": "ISC", "engines": {"node": ">=12"}}, "node_modules/d3-interpolate": {"version": "3.0.1", "license": "ISC", "dependencies": {"d3-color": "1 - 3"}, "engines": {"node": ">=12"}}, "node_modules/d3-path": {"version": "3.1.0", "license": "ISC", "engines": {"node": ">=12"}}, "node_modules/d3-scale": {"version": "4.0.2", "license": "ISC", "dependencies": {"d3-array": "2.10.0 - 3", "d3-format": "1 - 3", "d3-interpolate": "1.2.0 - 3", "d3-time": "2.1.1 - 3", "d3-time-format": "2 - 4"}, "engines": {"node": ">=12"}}, "node_modules/d3-shape": {"version": "3.2.0", "license": "ISC", "dependencies": {"d3-path": "^3.1.0"}, "engines": {"node": ">=12"}}, "node_modules/d3-time": {"version": "3.1.0", "license": "ISC", "dependencies": {"d3-array": "2 - 3"}, "engines": {"node": ">=12"}}, "node_modules/d3-time-format": {"version": "4.1.0", "license": "ISC", "dependencies": {"d3-time": "1 - 3"}, "engines": {"node": ">=12"}}, "node_modules/d3-timer": {"version": "3.0.1", "license": "ISC", "engines": {"node": ">=12"}}, "node_modules/damerau-levenshtein": {"version": "1.0.8", "license": "BSD-2-<PERSON><PERSON>"}, "node_modules/dashdash": {"version": "1.14.1", "license": "MIT", "dependencies": {"assert-plus": "^1.0.0"}, "engines": {"node": ">=0.10"}}, "node_modules/data-view-buffer": {"version": "1.0.1", "license": "MIT", "dependencies": {"call-bind": "^1.0.6", "es-errors": "^1.3.0", "is-data-view": "^1.0.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/data-view-byte-length": {"version": "1.0.1", "license": "MIT", "dependencies": {"call-bind": "^1.0.7", "es-errors": "^1.3.0", "is-data-view": "^1.0.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/data-view-byte-offset": {"version": "1.0.0", "license": "MIT", "dependencies": {"call-bind": "^1.0.6", "es-errors": "^1.3.0", "is-data-view": "^1.0.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/date-arithmetic": {"version": "4.1.0", "resolved": "https://registry.npmjs.org/date-arithmetic/-/date-arithmetic-4.1.0.tgz", "integrity": "sha512-QWxYLR5P/6GStZcdem+V1xoto6DMadYWpMXU82ES3/RfR3Wdwr3D0+be7mgOJ+Ov0G9D5Dmb9T17sNLQYj9XOg==", "license": "MIT"}, "node_modules/date-fns": {"version": "2.30.0", "resolved": "https://registry.npmjs.org/date-fns/-/date-fns-2.30.0.tgz", "integrity": "sha512-fnULvOpxnC5/Vg3NCiWelDsLiUc9bRwAPs/+LfTLNvetFCtCTN+yQz15C/fs4AwX1R9K5GLtLfn8QW+dWisaAw==", "license": "MIT", "dependencies": {"@babel/runtime": "^7.21.0"}, "engines": {"node": ">=0.11"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/date-fns"}}, "node_modules/date-fns-tz": {"version": "2.0.0", "license": "MIT", "peerDependencies": {"date-fns": ">=2.0.0"}}, "node_modules/dayjs": {"version": "1.11.9", "license": "MIT"}, "node_modules/debug": {"version": "4.3.6", "license": "MIT", "dependencies": {"ms": "2.1.2"}, "engines": {"node": ">=6.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}}, "node_modules/decimal.js-light": {"version": "2.5.1", "license": "MIT"}, "node_modules/deep-equal": {"version": "2.2.3", "license": "MIT", "dependencies": {"array-buffer-byte-length": "^1.0.0", "call-bind": "^1.0.5", "es-get-iterator": "^1.1.3", "get-intrinsic": "^1.2.2", "is-arguments": "^1.1.1", "is-array-buffer": "^3.0.2", "is-date-object": "^1.0.5", "is-regex": "^1.1.4", "is-shared-array-buffer": "^1.0.2", "isarray": "^2.0.5", "object-is": "^1.1.5", "object-keys": "^1.1.1", "object.assign": "^4.1.4", "regexp.prototype.flags": "^1.5.1", "side-channel": "^1.0.4", "which-boxed-primitive": "^1.0.2", "which-collection": "^1.0.1", "which-typed-array": "^1.1.13"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/deep-is": {"version": "0.1.4", "license": "MIT"}, "node_modules/deepmerge": {"version": "2.2.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/define-data-property": {"version": "1.1.4", "license": "MIT", "dependencies": {"es-define-property": "^1.0.0", "es-errors": "^1.3.0", "gopd": "^1.0.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/define-properties": {"version": "1.2.1", "license": "MIT", "dependencies": {"define-data-property": "^1.0.1", "has-property-descriptors": "^1.0.0", "object-keys": "^1.1.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/delayed-stream": {"version": "1.0.0", "license": "MIT", "engines": {"node": ">=0.4.0"}}, "node_modules/dequal": {"version": "2.0.3", "resolved": "https://registry.npmjs.org/dequal/-/dequal-2.0.3.tgz", "integrity": "sha512-0je+qPKHEMohvfRTCEo3CrPG6cAzAYgmzKyxRiYSSDkS6eGJdyVJm7WaYA5ECaAD9wLB2T4EEeymA5aFVcYXCA==", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/detect-element-overflow": {"version": "1.4.2", "resolved": "https://registry.npmjs.org/detect-element-overflow/-/detect-element-overflow-1.4.2.tgz", "integrity": "sha512-4m6cVOtvm/GJLjo7WFkPfwXoEIIbM7GQwIh4WEa4g7IsNi1YzwUsGL5ApNLrrHL29bHeNeQ+/iZhw+YHqgE2Fw==", "funding": {"url": "https://github.com/wojtekmaj/detect-element-overflow?sponsor=1"}}, "node_modules/detect-node-es": {"version": "1.1.0", "license": "MIT"}, "node_modules/dfa": {"version": "1.2.0", "license": "MIT"}, "node_modules/didyoumean": {"version": "1.2.2", "license": "Apache-2.0"}, "node_modules/dir-glob": {"version": "3.0.1", "license": "MIT", "dependencies": {"path-type": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/dlv": {"version": "1.1.3", "license": "MIT"}, "node_modules/doctrine": {"version": "3.0.0", "license": "Apache-2.0", "dependencies": {"esutils": "^2.0.2"}, "engines": {"node": ">=6.0.0"}}, "node_modules/dom-helpers": {"version": "5.2.1", "license": "MIT", "dependencies": {"@babel/runtime": "^7.8.7", "csstype": "^3.0.2"}}, "node_modules/dompurify": {"version": "3.2.6", "resolved": "https://registry.npmjs.org/dompurify/-/dompurify-3.2.6.tgz", "integrity": "sha512-/2GogDQlohXPZe6D6NOgQvXLPSYBqIWMnZ8zzOhn09REE4eyAzb+Hed3jhoM9OkuaJ8P6ZGTTVWQKAi8ieIzfQ==", "optional": true, "optionalDependencies": {"@types/trusted-types": "^2.0.7"}}, "node_modules/eastasianwidth": {"version": "0.2.0", "license": "MIT"}, "node_modules/ecc-jsbn": {"version": "0.1.2", "license": "MIT", "dependencies": {"jsbn": "~0.1.0", "safer-buffer": "^2.1.0"}}, "node_modules/electron-to-chromium": {"version": "1.5.13", "license": "ISC"}, "node_modules/emoji-regex": {"version": "10.4.0", "license": "MIT"}, "node_modules/encoding": {"version": "0.1.13", "resolved": "https://registry.npmjs.org/encoding/-/encoding-0.1.13.tgz", "integrity": "sha512-ETBauow1T35Y/WZMkio9jiM0Z5xjHHmJ4XmjZOq1l/dXz3lr2sRn87nJy20RupqSh1F2m3HHPSp8ShIPQJrJ3A==", "devOptional": true, "license": "MIT", "dependencies": {"iconv-lite": "^0.6.2"}}, "node_modules/end-of-stream": {"version": "1.4.4", "license": "MIT", "dependencies": {"once": "^1.4.0"}}, "node_modules/enhanced-resolve": {"version": "5.17.1", "license": "MIT", "dependencies": {"graceful-fs": "^4.2.4", "tapable": "^2.2.0"}, "engines": {"node": ">=10.13.0"}}, "node_modules/enquirer": {"version": "2.4.1", "license": "MIT", "dependencies": {"ansi-colors": "^4.1.1", "strip-ansi": "^6.0.1"}, "engines": {"node": ">=8.6"}}, "node_modules/error-ex": {"version": "1.3.2", "license": "MIT", "dependencies": {"is-arrayish": "^0.2.1"}}, "node_modules/es-abstract": {"version": "1.23.3", "license": "MIT", "dependencies": {"array-buffer-byte-length": "^1.0.1", "arraybuffer.prototype.slice": "^1.0.3", "available-typed-arrays": "^1.0.7", "call-bind": "^1.0.7", "data-view-buffer": "^1.0.1", "data-view-byte-length": "^1.0.1", "data-view-byte-offset": "^1.0.0", "es-define-property": "^1.0.0", "es-errors": "^1.3.0", "es-object-atoms": "^1.0.0", "es-set-tostringtag": "^2.0.3", "es-to-primitive": "^1.2.1", "function.prototype.name": "^1.1.6", "get-intrinsic": "^1.2.4", "get-symbol-description": "^1.0.2", "globalthis": "^1.0.3", "gopd": "^1.0.1", "has-property-descriptors": "^1.0.2", "has-proto": "^1.0.3", "has-symbols": "^1.0.3", "hasown": "^2.0.2", "internal-slot": "^1.0.7", "is-array-buffer": "^3.0.4", "is-callable": "^1.2.7", "is-data-view": "^1.0.1", "is-negative-zero": "^2.0.3", "is-regex": "^1.1.4", "is-shared-array-buffer": "^1.0.3", "is-string": "^1.0.7", "is-typed-array": "^1.1.13", "is-weakref": "^1.0.2", "object-inspect": "^1.13.1", "object-keys": "^1.1.1", "object.assign": "^4.1.5", "regexp.prototype.flags": "^1.5.2", "safe-array-concat": "^1.1.2", "safe-regex-test": "^1.0.3", "string.prototype.trim": "^1.2.9", "string.prototype.trimend": "^1.0.8", "string.prototype.trimstart": "^1.0.8", "typed-array-buffer": "^1.0.2", "typed-array-byte-length": "^1.0.1", "typed-array-byte-offset": "^1.0.2", "typed-array-length": "^1.0.6", "unbox-primitive": "^1.0.2", "which-typed-array": "^1.1.15"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/es-define-property": {"version": "1.0.0", "license": "MIT", "dependencies": {"get-intrinsic": "^1.2.4"}, "engines": {"node": ">= 0.4"}}, "node_modules/es-errors": {"version": "1.3.0", "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/es-get-iterator": {"version": "1.1.3", "license": "MIT", "dependencies": {"call-bind": "^1.0.2", "get-intrinsic": "^1.1.3", "has-symbols": "^1.0.3", "is-arguments": "^1.1.1", "is-map": "^2.0.2", "is-set": "^2.0.2", "is-string": "^1.0.7", "isarray": "^2.0.5", "stop-iteration-iterator": "^1.0.0"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/es-iterator-helpers": {"version": "1.0.19", "license": "MIT", "dependencies": {"call-bind": "^1.0.7", "define-properties": "^1.2.1", "es-abstract": "^1.23.3", "es-errors": "^1.3.0", "es-set-tostringtag": "^2.0.3", "function-bind": "^1.1.2", "get-intrinsic": "^1.2.4", "globalthis": "^1.0.3", "has-property-descriptors": "^1.0.2", "has-proto": "^1.0.3", "has-symbols": "^1.0.3", "internal-slot": "^1.0.7", "iterator.prototype": "^1.1.2", "safe-array-concat": "^1.1.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/es-object-atoms": {"version": "1.0.0", "license": "MIT", "dependencies": {"es-errors": "^1.3.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/es-set-tostringtag": {"version": "2.0.3", "license": "MIT", "dependencies": {"get-intrinsic": "^1.2.4", "has-tostringtag": "^1.0.2", "hasown": "^2.0.1"}, "engines": {"node": ">= 0.4"}}, "node_modules/es-shim-unscopables": {"version": "1.0.2", "license": "MIT", "dependencies": {"hasown": "^2.0.0"}}, "node_modules/es-to-primitive": {"version": "1.2.1", "license": "MIT", "dependencies": {"is-callable": "^1.1.4", "is-date-object": "^1.0.1", "is-symbol": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/escalade": {"version": "3.1.2", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/escape-string-regexp": {"version": "4.0.0", "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/eslint": {"version": "8.41.0", "license": "MIT", "dependencies": {"@eslint-community/eslint-utils": "^4.2.0", "@eslint-community/regexpp": "^4.4.0", "@eslint/eslintrc": "^2.0.3", "@eslint/js": "8.41.0", "@humanwhocodes/config-array": "^0.11.8", "@humanwhocodes/module-importer": "^1.0.1", "@nodelib/fs.walk": "^1.2.8", "ajv": "^6.10.0", "chalk": "^4.0.0", "cross-spawn": "^7.0.2", "debug": "^4.3.2", "doctrine": "^3.0.0", "escape-string-regexp": "^4.0.0", "eslint-scope": "^7.2.0", "eslint-visitor-keys": "^3.4.1", "espree": "^9.5.2", "esquery": "^1.4.2", "esutils": "^2.0.2", "fast-deep-equal": "^3.1.3", "file-entry-cache": "^6.0.1", "find-up": "^5.0.0", "glob-parent": "^6.0.2", "globals": "^13.19.0", "graphemer": "^1.4.0", "ignore": "^5.2.0", "import-fresh": "^3.0.0", "imurmurhash": "^0.1.4", "is-glob": "^4.0.0", "is-path-inside": "^3.0.3", "js-yaml": "^4.1.0", "json-stable-stringify-without-jsonify": "^1.0.1", "levn": "^0.4.1", "lodash.merge": "^4.6.2", "minimatch": "^3.1.2", "natural-compare": "^1.4.0", "optionator": "^0.9.1", "strip-ansi": "^6.0.1", "strip-json-comments": "^3.1.0", "text-table": "^0.2.0"}, "bin": {"eslint": "bin/eslint.js"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/eslint-cli": {"version": "1.1.1", "license": "MIT", "dependencies": {"chalk": "^2.0.1", "debug": "^2.6.8", "resolve": "^1.3.3"}, "bin": {"eslint": "bin/eslint.js", "eslint-cli": "bin/eslint.js"}}, "node_modules/eslint-cli/node_modules/debug": {"version": "2.6.9", "license": "MIT", "dependencies": {"ms": "2.0.0"}}, "node_modules/eslint-cli/node_modules/ms": {"version": "2.0.0", "license": "MIT"}, "node_modules/eslint-config-airbnb": {"version": "19.0.4", "dev": true, "license": "MIT", "dependencies": {"eslint-config-airbnb-base": "^15.0.0", "object.assign": "^4.1.2", "object.entries": "^1.1.5"}, "engines": {"node": "^10.12.0 || ^12.22.0 || ^14.17.0 || >=16.0.0"}, "peerDependencies": {"eslint": "^7.32.0 || ^8.2.0", "eslint-plugin-import": "^2.25.3", "eslint-plugin-jsx-a11y": "^6.5.1", "eslint-plugin-react": "^7.28.0", "eslint-plugin-react-hooks": "^4.3.0"}}, "node_modules/eslint-config-airbnb-base": {"version": "15.0.0", "dev": true, "license": "MIT", "dependencies": {"confusing-browser-globals": "^1.0.10", "object.assign": "^4.1.2", "object.entries": "^1.1.5", "semver": "^6.3.0"}, "engines": {"node": "^10.12.0 || >=12.0.0"}, "peerDependencies": {"eslint": "^7.32.0 || ^8.2.0", "eslint-plugin-import": "^2.25.2"}}, "node_modules/eslint-config-airbnb-base/node_modules/semver": {"version": "6.3.1", "dev": true, "license": "ISC", "bin": {"semver": "bin/semver.js"}}, "node_modules/eslint-config-airbnb-typescript": {"version": "17.0.0", "dev": true, "license": "MIT", "dependencies": {"eslint-config-airbnb-base": "^15.0.0"}, "peerDependencies": {"@typescript-eslint/eslint-plugin": "^5.13.0", "@typescript-eslint/parser": "^5.0.0", "eslint": "^7.32.0 || ^8.2.0", "eslint-plugin-import": "^2.25.3"}}, "node_modules/eslint-config-next": {"version": "14.2.5", "license": "MIT", "dependencies": {"@next/eslint-plugin-next": "14.2.5", "@rushstack/eslint-patch": "^1.3.3", "@typescript-eslint/parser": "^5.4.2 || ^6.0.0 || 7.0.0 - 7.2.0", "eslint-import-resolver-node": "^0.3.6", "eslint-import-resolver-typescript": "^3.5.2", "eslint-plugin-import": "^2.28.1", "eslint-plugin-jsx-a11y": "^6.7.1", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.5.0 || 5.0.0-canary-7118f5dd7-20230705"}, "peerDependencies": {"eslint": "^7.23.0 || ^8.0.0", "typescript": ">=3.3.1"}, "peerDependenciesMeta": {"typescript": {"optional": true}}}, "node_modules/eslint-config-next/node_modules/@next/eslint-plugin-next": {"version": "14.2.5", "license": "MIT", "dependencies": {"glob": "10.3.10"}}, "node_modules/eslint-config-next/node_modules/brace-expansion": {"version": "2.0.1", "license": "MIT", "dependencies": {"balanced-match": "^1.0.0"}}, "node_modules/eslint-config-next/node_modules/eslint-import-resolver-typescript": {"version": "3.6.3", "license": "ISC", "dependencies": {"@nolyfill/is-core-module": "1.0.39", "debug": "^4.3.5", "enhanced-resolve": "^5.15.0", "eslint-module-utils": "^2.8.1", "fast-glob": "^3.3.2", "get-tsconfig": "^4.7.5", "is-bun-module": "^1.0.2", "is-glob": "^4.0.3"}, "engines": {"node": "^14.18.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/unts/projects/eslint-import-resolver-ts"}, "peerDependencies": {"eslint": "*", "eslint-plugin-import": "*", "eslint-plugin-import-x": "*"}, "peerDependenciesMeta": {"eslint-plugin-import": {"optional": true}, "eslint-plugin-import-x": {"optional": true}}}, "node_modules/eslint-config-next/node_modules/glob": {"version": "10.3.10", "license": "ISC", "dependencies": {"foreground-child": "^3.1.0", "jackspeak": "^2.3.5", "minimatch": "^9.0.1", "minipass": "^5.0.0 || ^6.0.2 || ^7.0.0", "path-scurry": "^1.10.1"}, "bin": {"glob": "dist/esm/bin.mjs"}, "engines": {"node": ">=16 || 14 >=14.17"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/eslint-config-next/node_modules/minimatch": {"version": "9.0.5", "license": "ISC", "dependencies": {"brace-expansion": "^2.0.1"}, "engines": {"node": ">=16 || 14 >=14.17"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/eslint-config-prettier": {"version": "8.8.0", "dev": true, "license": "MIT", "bin": {"eslint-config-prettier": "bin/cli.js"}, "peerDependencies": {"eslint": ">=7.0.0"}}, "node_modules/eslint-import-resolver-node": {"version": "0.3.9", "license": "MIT", "dependencies": {"debug": "^3.2.7", "is-core-module": "^2.13.0", "resolve": "^1.22.4"}}, "node_modules/eslint-import-resolver-node/node_modules/debug": {"version": "3.2.7", "license": "MIT", "dependencies": {"ms": "^2.1.1"}}, "node_modules/eslint-module-utils": {"version": "2.8.2", "license": "MIT", "dependencies": {"debug": "^3.2.7"}, "engines": {"node": ">=4"}, "peerDependenciesMeta": {"eslint": {"optional": true}}}, "node_modules/eslint-module-utils/node_modules/debug": {"version": "3.2.7", "license": "MIT", "dependencies": {"ms": "^2.1.1"}}, "node_modules/eslint-plugin-cypress": {"version": "2.13.3", "dev": true, "license": "MIT", "dependencies": {"globals": "^11.12.0"}, "peerDependencies": {"eslint": ">= 3.2.1"}}, "node_modules/eslint-plugin-import": {"version": "2.29.1", "license": "MIT", "dependencies": {"array-includes": "^3.1.7", "array.prototype.findlastindex": "^1.2.3", "array.prototype.flat": "^1.3.2", "array.prototype.flatmap": "^1.3.2", "debug": "^3.2.7", "doctrine": "^2.1.0", "eslint-import-resolver-node": "^0.3.9", "eslint-module-utils": "^2.8.0", "hasown": "^2.0.0", "is-core-module": "^2.13.1", "is-glob": "^4.0.3", "minimatch": "^3.1.2", "object.fromentries": "^2.0.7", "object.groupby": "^1.0.1", "object.values": "^1.1.7", "semver": "^6.3.1", "tsconfig-paths": "^3.15.0"}, "engines": {"node": ">=4"}, "peerDependencies": {"eslint": "^2 || ^3 || ^4 || ^5 || ^6 || ^7.2.0 || ^8"}}, "node_modules/eslint-plugin-import/node_modules/debug": {"version": "3.2.7", "license": "MIT", "dependencies": {"ms": "^2.1.1"}}, "node_modules/eslint-plugin-import/node_modules/doctrine": {"version": "2.1.0", "license": "Apache-2.0", "dependencies": {"esutils": "^2.0.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/eslint-plugin-import/node_modules/semver": {"version": "6.3.1", "license": "ISC", "bin": {"semver": "bin/semver.js"}}, "node_modules/eslint-plugin-jsx-a11y": {"version": "6.9.0", "license": "MIT", "dependencies": {"aria-query": "~5.1.3", "array-includes": "^3.1.8", "array.prototype.flatmap": "^1.3.2", "ast-types-flow": "^0.0.8", "axe-core": "^4.9.1", "axobject-query": "~3.1.1", "damerau-levenshtein": "^1.0.8", "emoji-regex": "^9.2.2", "es-iterator-helpers": "^1.0.19", "hasown": "^2.0.2", "jsx-ast-utils": "^3.3.5", "language-tags": "^1.0.9", "minimatch": "^3.1.2", "object.fromentries": "^2.0.8", "safe-regex-test": "^1.0.3", "string.prototype.includes": "^2.0.0"}, "engines": {"node": ">=4.0"}, "peerDependencies": {"eslint": "^3 || ^4 || ^5 || ^6 || ^7 || ^8"}}, "node_modules/eslint-plugin-jsx-a11y/node_modules/emoji-regex": {"version": "9.2.2", "license": "MIT"}, "node_modules/eslint-plugin-prettier": {"version": "4.2.1", "dev": true, "license": "MIT", "dependencies": {"prettier-linter-helpers": "^1.0.0"}, "engines": {"node": ">=12.0.0"}, "peerDependencies": {"eslint": ">=7.28.0", "prettier": ">=2.0.0"}, "peerDependenciesMeta": {"eslint-config-prettier": {"optional": true}}}, "node_modules/eslint-plugin-react": {"version": "7.35.0", "license": "MIT", "dependencies": {"array-includes": "^3.1.8", "array.prototype.findlast": "^1.2.5", "array.prototype.flatmap": "^1.3.2", "array.prototype.tosorted": "^1.1.4", "doctrine": "^2.1.0", "es-iterator-helpers": "^1.0.19", "estraverse": "^5.3.0", "hasown": "^2.0.2", "jsx-ast-utils": "^2.4.1 || ^3.0.0", "minimatch": "^3.1.2", "object.entries": "^1.1.8", "object.fromentries": "^2.0.8", "object.values": "^1.2.0", "prop-types": "^15.8.1", "resolve": "^2.0.0-next.5", "semver": "^6.3.1", "string.prototype.matchall": "^4.0.11", "string.prototype.repeat": "^1.0.0"}, "engines": {"node": ">=4"}, "peerDependencies": {"eslint": "^3 || ^4 || ^5 || ^6 || ^7 || ^8 || ^9.7"}}, "node_modules/eslint-plugin-react-hooks": {"version": "4.6.2", "license": "MIT", "engines": {"node": ">=10"}, "peerDependencies": {"eslint": "^3.0.0 || ^4.0.0 || ^5.0.0 || ^6.0.0 || ^7.0.0 || ^8.0.0-0"}}, "node_modules/eslint-plugin-react/node_modules/doctrine": {"version": "2.1.0", "license": "Apache-2.0", "dependencies": {"esutils": "^2.0.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/eslint-plugin-react/node_modules/resolve": {"version": "2.0.0-next.5", "license": "MIT", "dependencies": {"is-core-module": "^2.13.0", "path-parse": "^1.0.7", "supports-preserve-symlinks-flag": "^1.0.0"}, "bin": {"resolve": "bin/resolve"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/eslint-plugin-react/node_modules/semver": {"version": "6.3.1", "license": "ISC", "bin": {"semver": "bin/semver.js"}}, "node_modules/eslint-scope": {"version": "5.1.1", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"esrecurse": "^4.3.0", "estraverse": "^4.1.1"}, "engines": {"node": ">=8.0.0"}}, "node_modules/eslint-scope/node_modules/estraverse": {"version": "4.3.0", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=4.0"}}, "node_modules/eslint-visitor-keys": {"version": "3.4.3", "license": "Apache-2.0", "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/eslint/node_modules/ansi-styles": {"version": "4.3.0", "license": "MIT", "dependencies": {"color-convert": "^2.0.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/eslint/node_modules/chalk": {"version": "4.1.2", "license": "MIT", "dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/chalk?sponsor=1"}}, "node_modules/eslint/node_modules/color-convert": {"version": "2.0.1", "license": "MIT", "dependencies": {"color-name": "~1.1.4"}, "engines": {"node": ">=7.0.0"}}, "node_modules/eslint/node_modules/color-name": {"version": "1.1.4", "license": "MIT"}, "node_modules/eslint/node_modules/eslint-scope": {"version": "7.2.2", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"esrecurse": "^4.3.0", "estraverse": "^5.2.0"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/eslint/node_modules/globals": {"version": "13.24.0", "license": "MIT", "dependencies": {"type-fest": "^0.20.2"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/eslint/node_modules/has-flag": {"version": "4.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/eslint/node_modules/supports-color": {"version": "7.2.0", "license": "MIT", "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/eslint/node_modules/type-fest": {"version": "0.20.2", "license": "(MIT OR CC0-1.0)", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/espree": {"version": "9.6.1", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"acorn": "^8.9.0", "acorn-jsx": "^5.3.2", "eslint-visitor-keys": "^3.4.1"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/esquery": {"version": "1.6.0", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"estraverse": "^5.1.0"}, "engines": {"node": ">=0.10"}}, "node_modules/esrecurse": {"version": "4.3.0", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"estraverse": "^5.2.0"}, "engines": {"node": ">=4.0"}}, "node_modules/estraverse": {"version": "5.3.0", "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=4.0"}}, "node_modules/esutils": {"version": "2.0.3", "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/eventemitter2": {"version": "6.4.7", "license": "MIT"}, "node_modules/eventemitter3": {"version": "4.0.7", "license": "MIT"}, "node_modules/events": {"version": "3.3.0", "license": "MIT", "engines": {"node": ">=0.8.x"}}, "node_modules/execa": {"version": "4.1.0", "license": "MIT", "dependencies": {"cross-spawn": "^7.0.0", "get-stream": "^5.0.0", "human-signals": "^1.1.1", "is-stream": "^2.0.0", "merge-stream": "^2.0.0", "npm-run-path": "^4.0.0", "onetime": "^5.1.0", "signal-exit": "^3.0.2", "strip-final-newline": "^2.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sindresorhus/execa?sponsor=1"}}, "node_modules/executable": {"version": "4.1.1", "license": "MIT", "dependencies": {"pify": "^2.2.0"}, "engines": {"node": ">=4"}}, "node_modules/extend": {"version": "3.0.2", "license": "MIT"}, "node_modules/extract-zip": {"version": "2.0.1", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"debug": "^4.1.1", "get-stream": "^5.1.0", "yauzl": "^2.10.0"}, "bin": {"extract-zip": "cli.js"}, "engines": {"node": ">= 10.17.0"}, "optionalDependencies": {"@types/yauzl": "^2.9.1"}}, "node_modules/extsprintf": {"version": "1.3.0", "engines": ["node >=0.6.0"], "license": "MIT"}, "node_modules/fast-deep-equal": {"version": "3.1.3", "license": "MIT"}, "node_modules/fast-diff": {"version": "1.3.0", "dev": true, "license": "Apache-2.0"}, "node_modules/fast-equals": {"version": "5.0.1", "license": "MIT", "engines": {"node": ">=6.0.0"}}, "node_modules/fast-glob": {"version": "3.3.2", "license": "MIT", "dependencies": {"@nodelib/fs.stat": "^2.0.2", "@nodelib/fs.walk": "^1.2.3", "glob-parent": "^5.1.2", "merge2": "^1.3.0", "micromatch": "^4.0.4"}, "engines": {"node": ">=8.6.0"}}, "node_modules/fast-glob/node_modules/glob-parent": {"version": "5.1.2", "license": "ISC", "dependencies": {"is-glob": "^4.0.1"}, "engines": {"node": ">= 6"}}, "node_modules/fast-json-stable-stringify": {"version": "2.1.0", "license": "MIT"}, "node_modules/fast-levenshtein": {"version": "2.0.6", "license": "MIT"}, "node_modules/fastq": {"version": "1.17.1", "license": "ISC", "dependencies": {"reusify": "^1.0.4"}}, "node_modules/fd-slicer": {"version": "1.1.0", "license": "MIT", "dependencies": {"pend": "~1.2.0"}}, "node_modules/fflate": {"version": "0.8.2", "resolved": "https://registry.npmjs.org/fflate/-/fflate-0.8.2.tgz", "integrity": "sha512-cPJU47OaAoCbg0pBvzsgpTPhmhqI5eJjh/JIu8tPj5q+T7iLvW/JAYUqmE7KOB4R1ZyEhzBaIQpQpardBF5z8A=="}, "node_modules/figures": {"version": "3.2.0", "license": "MIT", "dependencies": {"escape-string-regexp": "^1.0.5"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/figures/node_modules/escape-string-regexp": {"version": "1.0.5", "license": "MIT", "engines": {"node": ">=0.8.0"}}, "node_modules/file-entry-cache": {"version": "6.0.1", "license": "MIT", "dependencies": {"flat-cache": "^3.0.4"}, "engines": {"node": "^10.12.0 || >=12.0.0"}}, "node_modules/file-selector": {"version": "2.1.2", "resolved": "https://registry.npmjs.org/file-selector/-/file-selector-2.1.2.tgz", "integrity": "sha512-QgXo+mXTe8ljeqUFaX3QVHc5osSItJ/Km+xpocx0aSqWGMSCf6qYs/VnzZgS864Pjn5iceMRFigeAV7AfTlaig==", "dependencies": {"tslib": "^2.7.0"}, "engines": {"node": ">= 12"}}, "node_modules/fill-range": {"version": "7.1.1", "license": "MIT", "dependencies": {"to-regex-range": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/find-root": {"version": "1.1.0", "license": "MIT"}, "node_modules/find-up": {"version": "5.0.0", "license": "MIT", "dependencies": {"locate-path": "^6.0.0", "path-exists": "^4.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/flat-cache": {"version": "3.2.0", "license": "MIT", "dependencies": {"flatted": "^3.2.9", "keyv": "^4.5.3", "rimraf": "^3.0.2"}, "engines": {"node": "^10.12.0 || >=12.0.0"}}, "node_modules/flatted": {"version": "3.3.1", "license": "ISC"}, "node_modules/focus-lock": {"version": "1.3.5", "resolved": "https://registry.npmjs.org/focus-lock/-/focus-lock-1.3.5.tgz", "integrity": "sha512-QFaHbhv9WPUeLYBDe/PAuLKJ4Dd9OPvKs9xZBr3yLXnUrDNaVXKu2baDBXe3naPY30hgHYSsf2JW4jzas2mDEQ==", "license": "MIT", "dependencies": {"tslib": "^2.0.3"}, "engines": {"node": ">=10"}}, "node_modules/follow-redirects": {"version": "1.15.6", "funding": [{"type": "individual", "url": "https://github.com/sponsors/Ruben<PERSON>"}], "license": "MIT", "engines": {"node": ">=4.0"}, "peerDependenciesMeta": {"debug": {"optional": true}}}, "node_modules/fontkit": {"version": "2.0.4", "license": "MIT", "dependencies": {"@swc/helpers": "^0.5.12", "brotli": "^1.3.2", "clone": "^2.1.2", "dfa": "^1.2.0", "fast-deep-equal": "^3.1.3", "restructure": "^3.0.0", "tiny-inflate": "^1.0.3", "unicode-properties": "^1.4.0", "unicode-trie": "^2.0.0"}}, "node_modules/for-each": {"version": "0.3.3", "license": "MIT", "dependencies": {"is-callable": "^1.1.3"}}, "node_modules/foreground-child": {"version": "3.3.0", "license": "ISC", "dependencies": {"cross-spawn": "^7.0.0", "signal-exit": "^4.0.1"}, "engines": {"node": ">=14"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/foreground-child/node_modules/signal-exit": {"version": "4.1.0", "license": "ISC", "engines": {"node": ">=14"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/forever-agent": {"version": "0.6.1", "license": "Apache-2.0", "engines": {"node": "*"}}, "node_modules/form-data": {"version": "4.0.0", "license": "MIT", "dependencies": {"asynckit": "^0.4.0", "combined-stream": "^1.0.8", "mime-types": "^2.1.12"}, "engines": {"node": ">= 6"}}, "node_modules/formik": {"version": "2.4.6", "funding": [{"type": "individual", "url": "https://opencollective.com/formik"}], "license": "Apache-2.0", "dependencies": {"@types/hoist-non-react-statics": "^3.3.1", "deepmerge": "^2.1.1", "hoist-non-react-statics": "^3.3.0", "lodash": "^4.17.21", "lodash-es": "^4.17.21", "react-fast-compare": "^2.0.1", "tiny-warning": "^1.0.2", "tslib": "^2.0.0"}, "peerDependencies": {"react": ">=16.8.0"}}, "node_modules/formik/node_modules/react-fast-compare": {"version": "2.0.4", "license": "MIT"}, "node_modules/frac": {"version": "1.1.2", "license": "Apache-2.0", "engines": {"node": ">=0.8"}}, "node_modules/fraction.js": {"version": "4.3.7", "license": "MIT", "engines": {"node": "*"}, "funding": {"type": "patreon", "url": "https://github.com/sponsors/rawify"}}, "node_modules/framer-motion": {"version": "10.12.16", "license": "MIT", "dependencies": {"tslib": "^2.4.0"}, "optionalDependencies": {"@emotion/is-prop-valid": "^0.8.2"}, "peerDependencies": {"react": "^18.0.0", "react-dom": "^18.0.0"}, "peerDependenciesMeta": {"react": {"optional": true}, "react-dom": {"optional": true}}}, "node_modules/framer-motion/node_modules/@emotion/is-prop-valid": {"version": "0.8.8", "license": "MIT", "optional": true, "dependencies": {"@emotion/memoize": "0.7.4"}}, "node_modules/framer-motion/node_modules/@emotion/memoize": {"version": "0.7.4", "license": "MIT", "optional": true}, "node_modules/framesync": {"version": "6.1.2", "resolved": "https://registry.npmjs.org/framesync/-/framesync-6.1.2.tgz", "integrity": "sha512-jBTqhX6KaQVDyus8muwZbBeGGP0XgujBRbQ7gM7BRdS3CadCZIHiawyzYLnafYcvZIh5j8WE7cxZKFn7dXhu9g==", "license": "MIT", "dependencies": {"tslib": "2.4.0"}}, "node_modules/framesync/node_modules/tslib": {"version": "2.4.0", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.4.0.tgz", "integrity": "sha512-d6xOpEDfsi2CZVlPQzGeux8XMwLT9hssAsaPYExaQMuYskwb+x1x7J371tWlbBdWHroy99KnVB6qIkUbs5X3UQ==", "license": "0BSD"}, "node_modules/fs-extra": {"version": "9.1.0", "license": "MIT", "dependencies": {"at-least-node": "^1.0.0", "graceful-fs": "^4.2.0", "jsonfile": "^6.0.1", "universalify": "^2.0.0"}, "engines": {"node": ">=10"}}, "node_modules/fs.realpath": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/fs.realpath/-/fs.realpath-1.0.0.tgz", "integrity": "sha512-OO0pH2lK6a0hZnAdau5ItzHPI6pUlvI7jMVnxUQRtw4owF2wk8lOSabtGDCTP4Ggrg2MbGnWO9X8K1t4+fGMDw==", "license": "ISC"}, "node_modules/function-bind": {"version": "1.1.2", "license": "MIT", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/function.prototype.name": {"version": "1.1.6", "license": "MIT", "dependencies": {"call-bind": "^1.0.2", "define-properties": "^1.2.0", "es-abstract": "^1.22.1", "functions-have-names": "^1.2.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/functions-have-names": {"version": "1.2.3", "license": "MIT", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/get-intrinsic": {"version": "1.2.4", "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "function-bind": "^1.1.2", "has-proto": "^1.0.1", "has-symbols": "^1.0.3", "hasown": "^2.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/get-nonce": {"version": "1.0.1", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/get-stream": {"version": "5.2.0", "license": "MIT", "dependencies": {"pump": "^3.0.0"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/get-symbol-description": {"version": "1.0.2", "license": "MIT", "dependencies": {"call-bind": "^1.0.5", "es-errors": "^1.3.0", "get-intrinsic": "^1.2.4"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/get-tsconfig": {"version": "4.7.6", "license": "MIT", "dependencies": {"resolve-pkg-maps": "^1.0.0"}, "funding": {"url": "https://github.com/privatenumber/get-tsconfig?sponsor=1"}}, "node_modules/get-user-locale": {"version": "2.3.2", "resolved": "https://registry.npmjs.org/get-user-locale/-/get-user-locale-2.3.2.tgz", "integrity": "sha512-O2GWvQkhnbDoWFUJfaBlDIKUEdND8ATpBXD6KXcbhxlfktyD/d8w6mkzM/IlQEqGZAMz/PW6j6Hv53BiigKLUQ==", "dependencies": {"mem": "^8.0.0"}, "funding": {"url": "https://github.com/wojtekmaj/get-user-locale?sponsor=1"}}, "node_modules/getos": {"version": "3.2.1", "license": "MIT", "dependencies": {"async": "^3.2.0"}}, "node_modules/getpass": {"version": "0.1.7", "license": "MIT", "dependencies": {"assert-plus": "^1.0.0"}}, "node_modules/glob-parent": {"version": "6.0.2", "license": "ISC", "dependencies": {"is-glob": "^4.0.3"}, "engines": {"node": ">=10.13.0"}}, "node_modules/global-dirs": {"version": "3.0.1", "license": "MIT", "dependencies": {"ini": "2.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/globalize": {"version": "0.1.1", "resolved": "https://registry.npmjs.org/globalize/-/globalize-0.1.1.tgz", "integrity": "sha512-5e01v8eLGfuQSOvx2MsDMOWS0GFtCx1wPzQSmcHw4hkxFzrQDBO3Xwg/m8Hr/7qXMrHeOIE29qWVzyv06u1TZA=="}, "node_modules/globals": {"version": "11.12.0", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/globalthis": {"version": "1.0.4", "license": "MIT", "dependencies": {"define-properties": "^1.2.1", "gopd": "^1.0.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/globby": {"version": "11.1.0", "license": "MIT", "dependencies": {"array-union": "^2.1.0", "dir-glob": "^3.0.1", "fast-glob": "^3.2.9", "ignore": "^5.2.0", "merge2": "^1.4.1", "slash": "^3.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/gopd": {"version": "1.0.1", "license": "MIT", "dependencies": {"get-intrinsic": "^1.1.3"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/graceful-fs": {"version": "4.2.11", "license": "ISC"}, "node_modules/grapheme-splitter": {"version": "1.0.4", "dev": true, "license": "MIT"}, "node_modules/graphemer": {"version": "1.4.0", "license": "MIT"}, "node_modules/has-bigints": {"version": "1.0.2", "license": "MIT", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/has-flag": {"version": "3.0.0", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/has-property-descriptors": {"version": "1.0.2", "license": "MIT", "dependencies": {"es-define-property": "^1.0.0"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/has-proto": {"version": "1.0.3", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/has-symbols": {"version": "1.0.3", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/has-tostringtag": {"version": "1.0.2", "license": "MIT", "dependencies": {"has-symbols": "^1.0.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/hasown": {"version": "2.0.2", "license": "MIT", "dependencies": {"function-bind": "^1.1.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/hoist-non-react-statics": {"version": "3.3.2", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"react-is": "^16.7.0"}}, "node_modules/hsl-to-hex": {"version": "1.0.0", "license": "MIT", "dependencies": {"hsl-to-rgb-for-reals": "^1.1.0"}}, "node_modules/hsl-to-rgb-for-reals": {"version": "1.1.1", "license": "ISC"}, "node_modules/html2canvas": {"version": "1.4.1", "resolved": "https://registry.npmjs.org/html2canvas/-/html2canvas-1.4.1.tgz", "integrity": "sha512-fPU6BHNpsyIhr8yyMpTLLxAbkaK8ArIBcmZIRiBLiDhjeqvXolaEmDGmELFuX9I4xDcaKKcJl+TKZLqruBbmWA==", "optional": true, "dependencies": {"css-line-break": "^2.1.0", "text-segmentation": "^1.0.3"}, "engines": {"node": ">=8.0.0"}}, "node_modules/http-signature": {"version": "1.3.6", "license": "MIT", "dependencies": {"assert-plus": "^1.0.0", "jsprim": "^2.0.2", "sshpk": "^1.14.1"}, "engines": {"node": ">=0.10"}}, "node_modules/human-signals": {"version": "1.1.1", "license": "Apache-2.0", "engines": {"node": ">=8.12.0"}}, "node_modules/husky": {"version": "8.0.3", "dev": true, "license": "MIT", "bin": {"husky": "lib/bin.js"}, "engines": {"node": ">=14"}, "funding": {"url": "https://github.com/sponsors/typicode"}}, "node_modules/hyphen": {"version": "1.10.4", "license": "ISC"}, "node_modules/iconv-lite": {"version": "0.6.3", "resolved": "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.6.3.tgz", "integrity": "sha512-4fCk79wshMdzMp2rH06qWrJE4iolqLhCUH+OiuIgU++RB0+94NlDL81atO7GX55uUKueo0txHNtvEyI6D7WdMw==", "devOptional": true, "license": "MIT", "dependencies": {"safer-buffer": ">= 2.1.2 < 3.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/ieee754": {"version": "1.2.1", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/ignore": {"version": "5.3.2", "license": "MIT", "engines": {"node": ">= 4"}}, "node_modules/immediate": {"version": "3.0.6", "resolved": "https://registry.npmjs.org/immediate/-/immediate-3.0.6.tgz", "integrity": "sha512-XXOFtyqDjNDAQxVfYxuF7g9Il/IbWmmlQg2MYKOH8ExIT1qg6xc4zyS3HaEEATgs1btfzxq15ciUiY7gjSXRGQ==", "license": "MIT"}, "node_modules/immer": {"version": "9.0.21", "license": "MIT", "funding": {"type": "opencollective", "url": "https://opencollective.com/immer"}}, "node_modules/import-fresh": {"version": "3.3.0", "license": "MIT", "dependencies": {"parent-module": "^1.0.0", "resolve-from": "^4.0.0"}, "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/imurmurhash": {"version": "0.1.4", "license": "MIT", "engines": {"node": ">=0.8.19"}}, "node_modules/indent-string": {"version": "4.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/inflight": {"version": "1.0.6", "resolved": "https://registry.npmjs.org/inflight/-/inflight-1.0.6.tgz", "integrity": "sha512-k92I/b08q4wvFscXCLvqfsHCrjrF7yiXsQuIVvVE7N82W3+aqpzuUdBbfhWcy/FZR3/4IgflMgKLOsvPDrGCJA==", "deprecated": "This module is not supported, and leaks memory. Do not use it. Check out lru-cache if you want a good and tested way to coalesce async requests by a key value, which is much more comprehensive and powerful.", "license": "ISC", "dependencies": {"once": "^1.3.0", "wrappy": "1"}}, "node_modules/inherits": {"version": "2.0.4", "license": "ISC"}, "node_modules/ini": {"version": "2.0.0", "license": "ISC", "engines": {"node": ">=10"}}, "node_modules/internal-slot": {"version": "1.0.7", "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "hasown": "^2.0.0", "side-channel": "^1.0.4"}, "engines": {"node": ">= 0.4"}}, "node_modules/international-types": {"version": "0.8.1", "resolved": "https://registry.npmjs.org/international-types/-/international-types-0.8.1.tgz", "integrity": "sha512-tajBCAHo4I0LIFlmQ9ZWfjMWVyRffzuvfbXCd6ssFt5u1Zw15DN0UBpVTItXdNa1ls+cpQt3Yw8+TxsfGF8JcA=="}, "node_modules/internmap": {"version": "2.0.3", "license": "ISC", "engines": {"node": ">=12"}}, "node_modules/invariant": {"version": "2.2.4", "license": "MIT", "dependencies": {"loose-envify": "^1.0.0"}}, "node_modules/is-arguments": {"version": "1.1.1", "license": "MIT", "dependencies": {"call-bind": "^1.0.2", "has-tostringtag": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-array-buffer": {"version": "3.0.4", "license": "MIT", "dependencies": {"call-bind": "^1.0.2", "get-intrinsic": "^1.2.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-arrayish": {"version": "0.2.1", "license": "MIT"}, "node_modules/is-async-function": {"version": "2.0.0", "license": "MIT", "dependencies": {"has-tostringtag": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-bigint": {"version": "1.0.4", "license": "MIT", "dependencies": {"has-bigints": "^1.0.1"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-binary-path": {"version": "2.1.0", "license": "MIT", "dependencies": {"binary-extensions": "^2.0.0"}, "engines": {"node": ">=8"}}, "node_modules/is-boolean-object": {"version": "1.1.2", "license": "MIT", "dependencies": {"call-bind": "^1.0.2", "has-tostringtag": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-bun-module": {"version": "1.1.0", "license": "MIT", "dependencies": {"semver": "^7.6.3"}}, "node_modules/is-callable": {"version": "1.2.7", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-ci": {"version": "3.0.1", "license": "MIT", "dependencies": {"ci-info": "^3.2.0"}, "bin": {"is-ci": "bin.js"}}, "node_modules/is-core-module": {"version": "2.15.1", "license": "MIT", "dependencies": {"hasown": "^2.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-data-view": {"version": "1.0.1", "license": "MIT", "dependencies": {"is-typed-array": "^1.1.13"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-date-object": {"version": "1.0.5", "license": "MIT", "dependencies": {"has-tostringtag": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-extglob": {"version": "2.1.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/is-finalizationregistry": {"version": "1.0.2", "license": "MIT", "dependencies": {"call-bind": "^1.0.2"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-fullwidth-code-point": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/is-generator-function": {"version": "1.0.10", "license": "MIT", "dependencies": {"has-tostringtag": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-glob": {"version": "4.0.3", "license": "MIT", "dependencies": {"is-extglob": "^2.1.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-installed-globally": {"version": "0.4.0", "license": "MIT", "dependencies": {"global-dirs": "^3.0.0", "is-path-inside": "^3.0.2"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/is-map": {"version": "2.0.3", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-negative-zero": {"version": "2.0.3", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-number": {"version": "7.0.0", "license": "MIT", "engines": {"node": ">=0.12.0"}}, "node_modules/is-number-object": {"version": "1.0.7", "license": "MIT", "dependencies": {"has-tostringtag": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-path-inside": {"version": "3.0.3", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/is-regex": {"version": "1.1.4", "license": "MIT", "dependencies": {"call-bind": "^1.0.2", "has-tostringtag": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-set": {"version": "2.0.3", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-shared-array-buffer": {"version": "1.0.3", "license": "MIT", "dependencies": {"call-bind": "^1.0.7"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-stream": {"version": "2.0.1", "license": "MIT", "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/is-string": {"version": "1.0.7", "license": "MIT", "dependencies": {"has-tostringtag": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-symbol": {"version": "1.0.4", "license": "MIT", "dependencies": {"has-symbols": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-typed-array": {"version": "1.1.13", "license": "MIT", "dependencies": {"which-typed-array": "^1.1.14"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-typedarray": {"version": "1.0.0", "license": "MIT"}, "node_modules/is-unicode-supported": {"version": "0.1.0", "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/is-url": {"version": "1.2.4", "license": "MIT"}, "node_modules/is-weakmap": {"version": "2.0.2", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-weakref": {"version": "1.0.2", "license": "MIT", "dependencies": {"call-bind": "^1.0.2"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-weakset": {"version": "2.0.3", "license": "MIT", "dependencies": {"call-bind": "^1.0.7", "get-intrinsic": "^1.2.4"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/isarray": {"version": "2.0.5", "license": "MIT"}, "node_modules/isexe": {"version": "2.0.0", "license": "ISC"}, "node_modules/isstream": {"version": "0.1.2", "license": "MIT"}, "node_modules/iterator.prototype": {"version": "1.1.2", "license": "MIT", "dependencies": {"define-properties": "^1.2.1", "get-intrinsic": "^1.2.1", "has-symbols": "^1.0.3", "reflect.getprototypeof": "^1.0.4", "set-function-name": "^2.0.1"}}, "node_modules/jackspeak": {"version": "2.3.6", "license": "BlueOak-1.0.0", "dependencies": {"@isaacs/cliui": "^8.0.2"}, "engines": {"node": ">=14"}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "optionalDependencies": {"@pkgjs/parseargs": "^0.11.0"}}, "node_modules/jay-peg": {"version": "1.0.2", "license": "MIT", "dependencies": {"restructure": "^3.0.0"}}, "node_modules/jiti": {"version": "1.21.6", "license": "MIT", "bin": {"jiti": "bin/jiti.js"}}, "node_modules/jose": {"version": "4.15.9", "license": "MIT", "funding": {"url": "https://github.com/sponsors/panva"}}, "node_modules/js-tokens": {"version": "4.0.0", "license": "MIT"}, "node_modules/js-yaml": {"version": "4.1.0", "license": "MIT", "dependencies": {"argparse": "^2.0.1"}, "bin": {"js-yaml": "bin/js-yaml.js"}}, "node_modules/jsbn": {"version": "0.1.1", "license": "MIT"}, "node_modules/jsesc": {"version": "2.5.2", "license": "MIT", "bin": {"jsesc": "bin/jsesc"}, "engines": {"node": ">=4"}}, "node_modules/json-buffer": {"version": "3.0.1", "license": "MIT"}, "node_modules/json-parse-even-better-errors": {"version": "2.3.1", "license": "MIT"}, "node_modules/json-schema": {"version": "0.4.0", "license": "(AFL-2.1 OR BSD-3-Clause)"}, "node_modules/json-schema-traverse": {"version": "0.4.1", "license": "MIT"}, "node_modules/json-stable-stringify-without-jsonify": {"version": "1.0.1", "license": "MIT"}, "node_modules/json-stringify-safe": {"version": "5.0.1", "license": "ISC"}, "node_modules/json5": {"version": "1.0.2", "license": "MIT", "dependencies": {"minimist": "^1.2.0"}, "bin": {"json5": "lib/cli.js"}}, "node_modules/jsonfile": {"version": "6.1.0", "license": "MIT", "dependencies": {"universalify": "^2.0.0"}, "optionalDependencies": {"graceful-fs": "^4.1.6"}}, "node_modules/jspdf": {"version": "3.0.1", "resolved": "https://registry.npmjs.org/jspdf/-/jspdf-3.0.1.tgz", "integrity": "sha512-qaGIxqxetdoNnFQQXxTKUD9/Z7AloLaw94fFsOiJMxbfYdBbrBuhWmbzI8TVjrw7s3jBY1PFHofBKMV/wZPapg==", "dependencies": {"@babel/runtime": "^7.26.7", "atob": "^2.1.2", "btoa": "^1.2.1", "fflate": "^0.8.1"}, "optionalDependencies": {"canvg": "^3.0.11", "core-js": "^3.6.0", "dompurify": "^3.2.4", "html2canvas": "^1.0.0-rc.5"}}, "node_modules/jsprim": {"version": "2.0.2", "engines": ["node >=0.6.0"], "license": "MIT", "dependencies": {"assert-plus": "1.0.0", "extsprintf": "1.3.0", "json-schema": "0.4.0", "verror": "1.10.0"}}, "node_modules/jsx-ast-utils": {"version": "3.3.5", "license": "MIT", "dependencies": {"array-includes": "^3.1.6", "array.prototype.flat": "^1.3.1", "object.assign": "^4.1.4", "object.values": "^1.1.6"}, "engines": {"node": ">=4.0"}}, "node_modules/jszip": {"version": "3.10.1", "resolved": "https://registry.npmjs.org/jszip/-/jszip-3.10.1.tgz", "integrity": "sha512-xXDvecyTpGLrqFrvkrUSoxxfJI5AH7U8zxxtVclpsUtMCq4JQ290LY8AW5c7Ggnr/Y/oK+bQMbqK2qmtk3pN4g==", "license": "(MIT OR GPL-3.0-or-later)", "dependencies": {"lie": "~3.3.0", "pako": "~1.0.2", "readable-stream": "~2.3.6", "setimmediate": "^1.0.5"}}, "node_modules/keyv": {"version": "4.5.4", "license": "MIT", "dependencies": {"json-buffer": "3.0.1"}}, "node_modules/language-subtag-registry": {"version": "0.3.23", "license": "CC0-1.0"}, "node_modules/language-tags": {"version": "1.0.9", "license": "MIT", "dependencies": {"language-subtag-registry": "^0.3.20"}, "engines": {"node": ">=0.10"}}, "node_modules/lazy-ass": {"version": "1.6.0", "license": "MIT", "engines": {"node": "> 0.8"}}, "node_modules/levn": {"version": "0.4.1", "license": "MIT", "dependencies": {"prelude-ls": "^1.2.1", "type-check": "~0.4.0"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/lie": {"version": "3.3.0", "resolved": "https://registry.npmjs.org/lie/-/lie-3.3.0.tgz", "integrity": "sha512-UaiMJzeWRlEujzAuw5LokY1L5ecNQYZKfmyZ9L7wDHb/p5etKaxXhohBcrw0EYby+G/NA52vRSN4N39dxHAIwQ==", "license": "MIT", "dependencies": {"immediate": "~3.0.5"}}, "node_modules/lilconfig": {"version": "2.1.0", "license": "MIT", "engines": {"node": ">=10"}}, "node_modules/lines-and-columns": {"version": "1.2.4", "license": "MIT"}, "node_modules/lint-staged": {"version": "13.2.2", "dev": true, "license": "MIT", "dependencies": {"chalk": "5.2.0", "cli-truncate": "^3.1.0", "commander": "^10.0.0", "debug": "^4.3.4", "execa": "^7.0.0", "lilconfig": "2.1.0", "listr2": "^5.0.7", "micromatch": "^4.0.5", "normalize-path": "^3.0.0", "object-inspect": "^1.12.3", "pidtree": "^0.6.0", "string-argv": "^0.3.1", "yaml": "^2.2.2"}, "bin": {"lint-staged": "bin/lint-staged.js"}, "engines": {"node": "^14.13.1 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/lint-staged"}}, "node_modules/lint-staged/node_modules/ansi-styles": {"version": "4.3.0", "dev": true, "license": "MIT", "dependencies": {"color-convert": "^2.0.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/lint-staged/node_modules/chalk": {"version": "5.2.0", "dev": true, "license": "MIT", "engines": {"node": "^12.17.0 || ^14.13 || >=16.0.0"}, "funding": {"url": "https://github.com/chalk/chalk?sponsor=1"}}, "node_modules/lint-staged/node_modules/color-convert": {"version": "2.0.1", "dev": true, "license": "MIT", "dependencies": {"color-name": "~1.1.4"}, "engines": {"node": ">=7.0.0"}}, "node_modules/lint-staged/node_modules/color-name": {"version": "1.1.4", "dev": true, "license": "MIT"}, "node_modules/lint-staged/node_modules/commander": {"version": "10.0.1", "dev": true, "license": "MIT", "engines": {"node": ">=14"}}, "node_modules/lint-staged/node_modules/execa": {"version": "7.2.0", "dev": true, "license": "MIT", "dependencies": {"cross-spawn": "^7.0.3", "get-stream": "^6.0.1", "human-signals": "^4.3.0", "is-stream": "^3.0.0", "merge-stream": "^2.0.0", "npm-run-path": "^5.1.0", "onetime": "^6.0.0", "signal-exit": "^3.0.7", "strip-final-newline": "^3.0.0"}, "engines": {"node": "^14.18.0 || ^16.14.0 || >=18.0.0"}, "funding": {"url": "https://github.com/sindresorhus/execa?sponsor=1"}}, "node_modules/lint-staged/node_modules/get-stream": {"version": "6.0.1", "dev": true, "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/lint-staged/node_modules/human-signals": {"version": "4.3.1", "dev": true, "license": "Apache-2.0", "engines": {"node": ">=14.18.0"}}, "node_modules/lint-staged/node_modules/is-fullwidth-code-point": {"version": "3.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/lint-staged/node_modules/is-stream": {"version": "3.0.0", "dev": true, "license": "MIT", "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/lint-staged/node_modules/listr2": {"version": "5.0.8", "dev": true, "license": "MIT", "dependencies": {"cli-truncate": "^2.1.0", "colorette": "^2.0.19", "log-update": "^4.0.0", "p-map": "^4.0.0", "rfdc": "^1.3.0", "rxjs": "^7.8.0", "through": "^2.3.8", "wrap-ansi": "^7.0.0"}, "engines": {"node": "^14.13.1 || >=16.0.0"}, "peerDependencies": {"enquirer": ">= 2.3.0 < 3"}, "peerDependenciesMeta": {"enquirer": {"optional": true}}}, "node_modules/lint-staged/node_modules/listr2/node_modules/cli-truncate": {"version": "2.1.0", "dev": true, "license": "MIT", "dependencies": {"slice-ansi": "^3.0.0", "string-width": "^4.2.0"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/lint-staged/node_modules/mimic-fn": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/lint-staged/node_modules/npm-run-path": {"version": "5.3.0", "dev": true, "license": "MIT", "dependencies": {"path-key": "^4.0.0"}, "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/lint-staged/node_modules/onetime": {"version": "6.0.0", "dev": true, "license": "MIT", "dependencies": {"mimic-fn": "^4.0.0"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/lint-staged/node_modules/path-key": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/lint-staged/node_modules/slice-ansi": {"version": "3.0.0", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^4.0.0", "astral-regex": "^2.0.0", "is-fullwidth-code-point": "^3.0.0"}, "engines": {"node": ">=8"}}, "node_modules/lint-staged/node_modules/strip-final-newline": {"version": "3.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/lint-staged/node_modules/yaml": {"version": "2.5.0", "dev": true, "license": "ISC", "bin": {"yaml": "bin.mjs"}, "engines": {"node": ">= 14"}}, "node_modules/listr2": {"version": "3.14.0", "license": "MIT", "dependencies": {"cli-truncate": "^2.1.0", "colorette": "^2.0.16", "log-update": "^4.0.0", "p-map": "^4.0.0", "rfdc": "^1.3.0", "rxjs": "^7.5.1", "through": "^2.3.8", "wrap-ansi": "^7.0.0"}, "engines": {"node": ">=10.0.0"}, "peerDependencies": {"enquirer": ">= 2.3.0 < 3"}, "peerDependenciesMeta": {"enquirer": {"optional": true}}}, "node_modules/listr2/node_modules/ansi-styles": {"version": "4.3.0", "license": "MIT", "dependencies": {"color-convert": "^2.0.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/listr2/node_modules/cli-truncate": {"version": "2.1.0", "license": "MIT", "dependencies": {"slice-ansi": "^3.0.0", "string-width": "^4.2.0"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/listr2/node_modules/color-convert": {"version": "2.0.1", "license": "MIT", "dependencies": {"color-name": "~1.1.4"}, "engines": {"node": ">=7.0.0"}}, "node_modules/listr2/node_modules/color-name": {"version": "1.1.4", "license": "MIT"}, "node_modules/listr2/node_modules/is-fullwidth-code-point": {"version": "3.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/listr2/node_modules/slice-ansi": {"version": "3.0.0", "license": "MIT", "dependencies": {"ansi-styles": "^4.0.0", "astral-regex": "^2.0.0", "is-fullwidth-code-point": "^3.0.0"}, "engines": {"node": ">=8"}}, "node_modules/locate-path": {"version": "6.0.0", "license": "MIT", "dependencies": {"p-locate": "^5.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/lodash": {"version": "4.17.21", "resolved": "https://registry.npmjs.org/lodash/-/lodash-4.17.21.tgz", "integrity": "sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg==", "license": "MIT"}, "node_modules/lodash-es": {"version": "4.17.21", "license": "MIT"}, "node_modules/lodash.merge": {"version": "4.6.2", "license": "MIT"}, "node_modules/lodash.mergewith": {"version": "4.6.2", "resolved": "https://registry.npmjs.org/lodash.mergewith/-/lodash.mergewith-4.6.2.tgz", "integrity": "sha512-GK3g5RPZWTRSeLSpgP8Xhra+pnjBC56q9FZYe1d5RN3TJ35dbkGy3YqBSMbyCrlbi+CM9Z3Jk5yTL7RCsqboyQ==", "license": "MIT"}, "node_modules/lodash.once": {"version": "4.1.1", "license": "MIT"}, "node_modules/log-symbols": {"version": "4.1.0", "license": "MIT", "dependencies": {"chalk": "^4.1.0", "is-unicode-supported": "^0.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/log-symbols/node_modules/ansi-styles": {"version": "4.3.0", "license": "MIT", "dependencies": {"color-convert": "^2.0.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/log-symbols/node_modules/chalk": {"version": "4.1.2", "license": "MIT", "dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/chalk?sponsor=1"}}, "node_modules/log-symbols/node_modules/color-convert": {"version": "2.0.1", "license": "MIT", "dependencies": {"color-name": "~1.1.4"}, "engines": {"node": ">=7.0.0"}}, "node_modules/log-symbols/node_modules/color-name": {"version": "1.1.4", "license": "MIT"}, "node_modules/log-symbols/node_modules/has-flag": {"version": "4.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/log-symbols/node_modules/supports-color": {"version": "7.2.0", "license": "MIT", "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/log-update": {"version": "4.0.0", "license": "MIT", "dependencies": {"ansi-escapes": "^4.3.0", "cli-cursor": "^3.1.0", "slice-ansi": "^4.0.0", "wrap-ansi": "^6.2.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/log-update/node_modules/ansi-styles": {"version": "4.3.0", "license": "MIT", "dependencies": {"color-convert": "^2.0.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/log-update/node_modules/color-convert": {"version": "2.0.1", "license": "MIT", "dependencies": {"color-name": "~1.1.4"}, "engines": {"node": ">=7.0.0"}}, "node_modules/log-update/node_modules/color-name": {"version": "1.1.4", "license": "MIT"}, "node_modules/log-update/node_modules/is-fullwidth-code-point": {"version": "3.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/log-update/node_modules/slice-ansi": {"version": "4.0.0", "license": "MIT", "dependencies": {"ansi-styles": "^4.0.0", "astral-regex": "^2.0.0", "is-fullwidth-code-point": "^3.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/slice-ansi?sponsor=1"}}, "node_modules/log-update/node_modules/wrap-ansi": {"version": "6.2.0", "license": "MIT", "dependencies": {"ansi-styles": "^4.0.0", "string-width": "^4.1.0", "strip-ansi": "^6.0.0"}, "engines": {"node": ">=8"}}, "node_modules/loose-envify": {"version": "1.4.0", "license": "MIT", "dependencies": {"js-tokens": "^3.0.0 || ^4.0.0"}, "bin": {"loose-envify": "cli.js"}}, "node_modules/lru-cache": {"version": "6.0.0", "license": "ISC", "dependencies": {"yallist": "^4.0.0"}, "engines": {"node": ">=10"}}, "node_modules/lucide-react": {"version": "0.378.0", "license": "ISC", "peerDependencies": {"react": "^16.5.1 || ^17.0.0 || ^18.0.0"}}, "node_modules/luxon": {"version": "3.5.0", "resolved": "https://registry.npmjs.org/luxon/-/luxon-3.5.0.tgz", "integrity": "sha512-rh+Zjr6DNfUYR3bPwJEnuwDdqMbxZW7LOQfUN4B54+Cl+0o5zaU9RJ6bcidfDtC1cWCZXQ+nvX8bf6bAji37QQ==", "license": "MIT", "engines": {"node": ">=12"}}, "node_modules/make-event-props": {"version": "1.6.2", "resolved": "https://registry.npmjs.org/make-event-props/-/make-event-props-1.6.2.tgz", "integrity": "sha512-iDwf7mA03WPiR8QxvcVHmVWEPfMY1RZXerDVNCRYW7dUr2ppH3J58Rwb39/WG39yTZdRSxr3x+2v22tvI0VEvA==", "funding": {"url": "https://github.com/wojtekmaj/make-event-props?sponsor=1"}}, "node_modules/map-age-cleaner": {"version": "0.1.3", "resolved": "https://registry.npmjs.org/map-age-cleaner/-/map-age-cleaner-0.1.3.tgz", "integrity": "sha512-bJzx6nMoP6PDLPBFmg7+xRKeFZvFboMrGlxmNj9ClvX53KrmvM5bXFXEWjbz4cz1AFn+jWJ9z/DJSz7hrs0w3w==", "dependencies": {"p-defer": "^1.0.0"}, "engines": {"node": ">=6"}}, "node_modules/media-engine": {"version": "1.0.3", "license": "MIT"}, "node_modules/mem": {"version": "8.1.1", "resolved": "https://registry.npmjs.org/mem/-/mem-8.1.1.tgz", "integrity": "sha512-qFCFUDs7U3b8mBDPyz5EToEKoAkgCzqquIgi9nkkR9bixxOVOre+09lbuH7+9Kn2NFpm56M3GUWVbU2hQgdACA==", "dependencies": {"map-age-cleaner": "^0.1.3", "mimic-fn": "^3.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sindresorhus/mem?sponsor=1"}}, "node_modules/mem/node_modules/mimic-fn": {"version": "3.1.0", "resolved": "https://registry.npmjs.org/mimic-fn/-/mimic-fn-3.1.0.tgz", "integrity": "sha512-Ysbi9uYW9hFyfrThdDEQuykN4Ey6BuwPD2kpI5ES/nFTDn/98yxYNLZJcgUAKPT/mcrLLKaGzJR9YVxJrIdASQ==", "engines": {"node": ">=8"}}, "node_modules/memoize-one": {"version": "6.0.0", "license": "MIT"}, "node_modules/merge-stream": {"version": "2.0.0", "license": "MIT"}, "node_modules/merge2": {"version": "1.4.1", "license": "MIT", "engines": {"node": ">= 8"}}, "node_modules/micromatch": {"version": "4.0.8", "license": "MIT", "dependencies": {"braces": "^3.0.3", "picomatch": "^2.3.1"}, "engines": {"node": ">=8.6"}}, "node_modules/mime-db": {"version": "1.52.0", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/mime-types": {"version": "2.1.35", "license": "MIT", "dependencies": {"mime-db": "1.52.0"}, "engines": {"node": ">= 0.6"}}, "node_modules/mimic-fn": {"version": "2.1.0", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/minimatch": {"version": "3.1.2", "license": "ISC", "dependencies": {"brace-expansion": "^1.1.7"}, "engines": {"node": "*"}}, "node_modules/minimist": {"version": "1.2.8", "license": "MIT", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/minipass": {"version": "7.1.2", "license": "ISC", "engines": {"node": ">=16 || 14 >=14.17"}}, "node_modules/mitt": {"version": "3.0.1", "resolved": "https://registry.npmjs.org/mitt/-/mitt-3.0.1.tgz", "integrity": "sha512-vKivATfr97l2/QBCYAkXYDbrIWPM2IIKEl7YPhjCvKlG3kE2gm+uBo6nEXK3M5/Ffh/FLpKExzOQ3JJoJGFKBw==", "license": "MIT"}, "node_modules/moment": {"version": "2.29.4", "license": "MIT", "engines": {"node": "*"}}, "node_modules/moment-timezone": {"version": "0.5.46", "resolved": "https://registry.npmjs.org/moment-timezone/-/moment-timezone-0.5.46.tgz", "integrity": "sha512-ZXm9b36esbe7OmdABqIWJuBBiLLwAjrN7CE+7sYdCCx82Nabt1wHDj8TVseS59QIlfFPbOoiBPm6ca9BioG4hw==", "license": "MIT", "dependencies": {"moment": "^2.29.4"}, "engines": {"node": "*"}}, "node_modules/ms": {"version": "2.1.2", "license": "MIT"}, "node_modules/mz": {"version": "2.7.0", "license": "MIT", "dependencies": {"any-promise": "^1.0.0", "object-assign": "^4.0.1", "thenify-all": "^1.0.0"}}, "node_modules/nanoclone": {"version": "0.2.1", "license": "MIT"}, "node_modules/nanoid": {"version": "3.3.7", "funding": [{"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "bin": {"nanoid": "bin/nanoid.cjs"}, "engines": {"node": "^10 || ^12 || ^13.7 || ^14 || >=15.0.1"}}, "node_modules/natural-compare": {"version": "1.4.0", "license": "MIT"}, "node_modules/natural-compare-lite": {"version": "1.4.0", "dev": true, "license": "MIT"}, "node_modules/next": {"version": "14.2.8", "resolved": "https://registry.npmjs.org/next/-/next-14.2.8.tgz", "integrity": "sha512-EyEyJZ89r8C5FPlS/401AiF3O8jeMtHIE+bLom9MwcdWJJFBgRl+MR/2VgO0v5bI6tQORNY0a0DR5sjpFNrjbg==", "license": "MIT", "dependencies": {"@next/env": "14.2.8", "@swc/helpers": "0.5.5", "busboy": "1.6.0", "caniuse-lite": "^1.0.30001579", "graceful-fs": "^4.2.11", "postcss": "8.4.31", "styled-jsx": "5.1.1"}, "bin": {"next": "dist/bin/next"}, "engines": {"node": ">=18.17.0"}, "optionalDependencies": {"@next/swc-darwin-arm64": "14.2.8", "@next/swc-darwin-x64": "14.2.8", "@next/swc-linux-arm64-gnu": "14.2.8", "@next/swc-linux-arm64-musl": "14.2.8", "@next/swc-linux-x64-gnu": "14.2.8", "@next/swc-linux-x64-musl": "14.2.8", "@next/swc-win32-arm64-msvc": "14.2.8", "@next/swc-win32-ia32-msvc": "14.2.8", "@next/swc-win32-x64-msvc": "14.2.8"}, "peerDependencies": {"@opentelemetry/api": "^1.1.0", "@playwright/test": "^1.41.2", "react": "^18.2.0", "react-dom": "^18.2.0", "sass": "^1.3.0"}, "peerDependenciesMeta": {"@opentelemetry/api": {"optional": true}, "@playwright/test": {"optional": true}, "sass": {"optional": true}}}, "node_modules/next-auth": {"version": "4.24.7", "license": "ISC", "dependencies": {"@babel/runtime": "^7.20.13", "@panva/hkdf": "^1.0.2", "cookie": "^0.5.0", "jose": "^4.15.5", "oauth": "^0.9.15", "openid-client": "^5.4.0", "preact": "^10.6.3", "preact-render-to-string": "^5.1.19", "uuid": "^8.3.2"}, "peerDependencies": {"next": "^12.2.5 || ^13 || ^14", "nodemailer": "^6.6.5", "react": "^17.0.2 || ^18", "react-dom": "^17.0.2 || ^18"}, "peerDependenciesMeta": {"nodemailer": {"optional": true}}}, "node_modules/next-auth/node_modules/cookie": {"version": "0.5.0", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/next-auth/node_modules/uuid": {"version": "8.3.2", "resolved": "https://registry.npmjs.org/uuid/-/uuid-8.3.2.tgz", "integrity": "sha512-+NYs2QeMWy+GWFOEm9xnn6HCDp0l7QBD7ml8zLUmJ+93Q5NF0NocErnwkTkXVFNiX3/fpC6afS8Dhb/gz7R7eg==", "license": "MIT", "bin": {"uuid": "dist/bin/uuid"}}, "node_modules/next-international": {"version": "1.2.4", "resolved": "https://registry.npmjs.org/next-international/-/next-international-1.2.4.tgz", "integrity": "sha512-JQvp+h2iSgA/t8hu5S/Lwow1ZErJutQRdpnplxjv4VTlCiND8T95fYih8BjkHcVhQbtM+Wu9Mb1CM32wD9hlWQ==", "dependencies": {"client-only": "^0.0.1", "international-types": "^0.8.1", "server-only": "^0.0.1"}}, "node_modules/next-themes": {"version": "0.3.0", "license": "MIT", "peerDependencies": {"react": "^16.8 || ^17 || ^18", "react-dom": "^16.8 || ^17 || ^18"}}, "node_modules/next/node_modules/@swc/helpers": {"version": "0.5.5", "license": "Apache-2.0", "dependencies": {"@swc/counter": "^0.1.3", "tslib": "^2.4.0"}}, "node_modules/next/node_modules/postcss": {"version": "8.4.31", "funding": [{"type": "opencollective", "url": "https://opencollective.com/postcss/"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/postcss"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "dependencies": {"nanoid": "^3.3.6", "picocolors": "^1.0.0", "source-map-js": "^1.0.2"}, "engines": {"node": "^10 || ^12 || >=14"}}, "node_modules/node-fetch": {"version": "2.7.0", "license": "MIT", "dependencies": {"whatwg-url": "^5.0.0"}, "engines": {"node": "4.x || >=6.0.0"}, "peerDependencies": {"encoding": "^0.1.0"}, "peerDependenciesMeta": {"encoding": {"optional": true}}}, "node_modules/node-releases": {"version": "2.0.18", "license": "MIT"}, "node_modules/nodemailer": {"version": "6.9.15", "resolved": "https://registry.npmjs.org/nodemailer/-/nodemailer-6.9.15.tgz", "integrity": "sha512-AHf04ySLC6CIfuRtRiEYtGEXgRfa6INgWGluDhnxTZhHSKvrBu7lc1VVchQ0d8nPc4cFaZoPq8vkyNoZr0TpGQ==", "license": "MIT-0", "engines": {"node": ">=6.0.0"}}, "node_modules/normalize-path": {"version": "3.0.0", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/normalize-range": {"version": "0.1.2", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/normalize-svg-path": {"version": "1.1.0", "license": "MIT", "dependencies": {"svg-arc-to-cubic-bezier": "^3.0.0"}}, "node_modules/npm-run-path": {"version": "4.0.1", "license": "MIT", "dependencies": {"path-key": "^3.0.0"}, "engines": {"node": ">=8"}}, "node_modules/numero-a-letras": {"version": "1.0.6", "license": "MIT", "dependencies": {"eslint-cli": "^1.1.1"}, "engines": {"node": ">=8.0.0", "npm": ">=5.7.1"}}, "node_modules/nuqs": {"version": "2.2.3", "resolved": "https://registry.npmjs.org/nuqs/-/nuqs-2.2.3.tgz", "integrity": "sha512-nMCcUW06KSqEXA0xp+LiRqDpIE59BVYbjZLe0HUisJAlswfihHYSsAjYTzV0lcE1thfh8uh+LqUHGdQ8qq8rfA==", "license": "MIT", "dependencies": {"mitt": "^3.0.1"}, "peerDependencies": {"@remix-run/react": ">=2", "next": ">=14.2.0", "react": ">=18.2.0 || ^19.0.0-0", "react-router-dom": ">=6"}, "peerDependenciesMeta": {"@remix-run/react": {"optional": true}, "next": {"optional": true}, "react-router-dom": {"optional": true}}}, "node_modules/oauth": {"version": "0.9.15", "license": "MIT"}, "node_modules/object-assign": {"version": "4.1.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/object-hash": {"version": "2.2.0", "license": "MIT", "engines": {"node": ">= 6"}}, "node_modules/object-inspect": {"version": "1.13.2", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/object-is": {"version": "1.1.6", "license": "MIT", "dependencies": {"call-bind": "^1.0.7", "define-properties": "^1.2.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/object-keys": {"version": "1.1.1", "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/object.assign": {"version": "4.1.5", "license": "MIT", "dependencies": {"call-bind": "^1.0.5", "define-properties": "^1.2.1", "has-symbols": "^1.0.3", "object-keys": "^1.1.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/object.entries": {"version": "1.1.8", "license": "MIT", "dependencies": {"call-bind": "^1.0.7", "define-properties": "^1.2.1", "es-object-atoms": "^1.0.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/object.fromentries": {"version": "2.0.8", "license": "MIT", "dependencies": {"call-bind": "^1.0.7", "define-properties": "^1.2.1", "es-abstract": "^1.23.2", "es-object-atoms": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/object.groupby": {"version": "1.0.3", "license": "MIT", "dependencies": {"call-bind": "^1.0.7", "define-properties": "^1.2.1", "es-abstract": "^1.23.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/object.values": {"version": "1.2.0", "license": "MIT", "dependencies": {"call-bind": "^1.0.7", "define-properties": "^1.2.1", "es-object-atoms": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/oidc-token-hash": {"version": "5.0.3", "license": "MIT", "engines": {"node": "^10.13.0 || >=12.0.0"}}, "node_modules/once": {"version": "1.4.0", "license": "ISC", "dependencies": {"wrappy": "1"}}, "node_modules/onetime": {"version": "5.1.2", "license": "MIT", "dependencies": {"mimic-fn": "^2.1.0"}, "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/openid-client": {"version": "5.6.5", "license": "MIT", "dependencies": {"jose": "^4.15.5", "lru-cache": "^6.0.0", "object-hash": "^2.2.0", "oidc-token-hash": "^5.0.3"}, "funding": {"url": "https://github.com/sponsors/panva"}}, "node_modules/optionator": {"version": "0.9.4", "license": "MIT", "dependencies": {"deep-is": "^0.1.3", "fast-levenshtein": "^2.0.6", "levn": "^0.4.1", "prelude-ls": "^1.2.1", "type-check": "^0.4.0", "word-wrap": "^1.2.5"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/ospath": {"version": "1.2.2", "license": "MIT"}, "node_modules/p-defer": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/p-defer/-/p-defer-1.0.0.tgz", "integrity": "sha512-wB3wfAxZpk2AzOfUMJNL+d36xothRSyj8EXOa4f6GMqYDN9BJaaSISbsk+wS9abmnebVw95C2Kb5t85UmpCxuw==", "engines": {"node": ">=4"}}, "node_modules/p-limit": {"version": "3.1.0", "license": "MIT", "dependencies": {"yocto-queue": "^0.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/p-locate": {"version": "5.0.0", "license": "MIT", "dependencies": {"p-limit": "^3.0.2"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/p-map": {"version": "4.0.0", "license": "MIT", "dependencies": {"aggregate-error": "^3.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/package-json-from-dist": {"version": "1.0.0", "license": "BlueOak-1.0.0"}, "node_modules/pako": {"version": "1.0.11", "license": "(MIT AND Zlib)"}, "node_modules/parent-module": {"version": "1.0.1", "license": "MIT", "dependencies": {"callsites": "^3.0.0"}, "engines": {"node": ">=6"}}, "node_modules/parse-json": {"version": "5.2.0", "license": "MIT", "dependencies": {"@babel/code-frame": "^7.0.0", "error-ex": "^1.3.1", "json-parse-even-better-errors": "^2.3.0", "lines-and-columns": "^1.1.6"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/parse-svg-path": {"version": "0.1.2", "license": "MIT"}, "node_modules/path-exists": {"version": "4.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/path-is-absolute": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/path-is-absolute/-/path-is-absolute-1.0.1.tgz", "integrity": "sha512-AVbw3UJ2e9bq64vSaS9Am0fje1Pa8pbGqTTsmXfaIiMpnr5DlDhfJOuLj9Sf95ZPVDAUerDfEk88MPmPe7UCQg==", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/path-key": {"version": "3.1.1", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/path-parse": {"version": "1.0.7", "license": "MIT"}, "node_modules/path-scurry": {"version": "1.11.1", "license": "BlueOak-1.0.0", "dependencies": {"lru-cache": "^10.2.0", "minipass": "^5.0.0 || ^6.0.2 || ^7.0.0"}, "engines": {"node": ">=16 || 14 >=14.18"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/path-scurry/node_modules/lru-cache": {"version": "10.4.3", "license": "ISC"}, "node_modules/path-type": {"version": "4.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/pend": {"version": "1.2.0", "license": "MIT"}, "node_modules/performance-now": {"version": "2.1.0", "license": "MIT"}, "node_modules/picocolors": {"version": "1.0.1", "license": "ISC"}, "node_modules/picomatch": {"version": "2.3.1", "license": "MIT", "engines": {"node": ">=8.6"}, "funding": {"url": "https://github.com/sponsors/jonschlinkert"}}, "node_modules/pidtree": {"version": "0.6.0", "dev": true, "license": "MIT", "bin": {"pidtree": "bin/pidtree.js"}, "engines": {"node": ">=0.10"}}, "node_modules/pify": {"version": "2.3.0", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/pirates": {"version": "4.0.6", "license": "MIT", "engines": {"node": ">= 6"}}, "node_modules/possible-typed-array-names": {"version": "1.0.0", "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/postcss": {"version": "8.4.24", "funding": [{"type": "opencollective", "url": "https://opencollective.com/postcss/"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/postcss"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "dependencies": {"nanoid": "^3.3.6", "picocolors": "^1.0.0", "source-map-js": "^1.0.2"}, "engines": {"node": "^10 || ^12 || >=14"}}, "node_modules/postcss-import": {"version": "15.1.0", "license": "MIT", "dependencies": {"postcss-value-parser": "^4.0.0", "read-cache": "^1.0.0", "resolve": "^1.1.7"}, "engines": {"node": ">=14.0.0"}, "peerDependencies": {"postcss": "^8.0.0"}}, "node_modules/postcss-js": {"version": "4.0.1", "license": "MIT", "dependencies": {"camelcase-css": "^2.0.1"}, "engines": {"node": "^12 || ^14 || >= 16"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/postcss/"}, "peerDependencies": {"postcss": "^8.4.21"}}, "node_modules/postcss-load-config": {"version": "4.0.2", "funding": [{"type": "opencollective", "url": "https://opencollective.com/postcss/"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "dependencies": {"lilconfig": "^3.0.0", "yaml": "^2.3.4"}, "engines": {"node": ">= 14"}, "peerDependencies": {"postcss": ">=8.0.9", "ts-node": ">=9.0.0"}, "peerDependenciesMeta": {"postcss": {"optional": true}, "ts-node": {"optional": true}}}, "node_modules/postcss-load-config/node_modules/lilconfig": {"version": "3.1.2", "license": "MIT", "engines": {"node": ">=14"}, "funding": {"url": "https://github.com/sponsors/antonk52"}}, "node_modules/postcss-load-config/node_modules/yaml": {"version": "2.5.0", "license": "ISC", "bin": {"yaml": "bin.mjs"}, "engines": {"node": ">= 14"}}, "node_modules/postcss-nested": {"version": "6.2.0", "funding": [{"type": "opencollective", "url": "https://opencollective.com/postcss/"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "dependencies": {"postcss-selector-parser": "^6.1.1"}, "engines": {"node": ">=12.0"}, "peerDependencies": {"postcss": "^8.2.14"}}, "node_modules/postcss-selector-parser": {"version": "6.1.2", "license": "MIT", "dependencies": {"cssesc": "^3.0.0", "util-deprecate": "^1.0.2"}, "engines": {"node": ">=4"}}, "node_modules/postcss-value-parser": {"version": "4.2.0", "license": "MIT"}, "node_modules/preact": {"version": "10.23.2", "license": "MIT", "funding": {"type": "opencollective", "url": "https://opencollective.com/preact"}}, "node_modules/preact-render-to-string": {"version": "5.2.6", "license": "MIT", "dependencies": {"pretty-format": "^3.8.0"}, "peerDependencies": {"preact": ">=10"}}, "node_modules/prelude-ls": {"version": "1.2.1", "license": "MIT", "engines": {"node": ">= 0.8.0"}}, "node_modules/prettier": {"version": "2.8.8", "dev": true, "license": "MIT", "bin": {"prettier": "bin-prettier.js"}, "engines": {"node": ">=10.13.0"}, "funding": {"url": "https://github.com/prettier/prettier?sponsor=1"}}, "node_modules/prettier-linter-helpers": {"version": "1.0.0", "dev": true, "license": "MIT", "dependencies": {"fast-diff": "^1.1.2"}, "engines": {"node": ">=6.0.0"}}, "node_modules/pretty-bytes": {"version": "5.6.0", "license": "MIT", "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/pretty-format": {"version": "3.8.0", "license": "MIT"}, "node_modules/process-nextick-args": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/process-nextick-args/-/process-nextick-args-2.0.1.tgz", "integrity": "sha512-3ouUOpQhtgrbOa17J7+uxOTpITYWaGP7/AhoR3+A+/1e9skrzelGi/dXzEYyvbxubEF6Wn2ypscTKiKJFFn1ag==", "license": "MIT"}, "node_modules/prom-client": {"version": "15.1.3", "resolved": "https://registry.npmjs.org/prom-client/-/prom-client-15.1.3.tgz", "integrity": "sha512-6ZiOBfCywsD4k1BN9IX0uZhF+tJkV8q8llP64G5Hajs4JOeVLPCwpPVcpXy3BwYiUGgyJzsJJQeOIv7+hDSq8g==", "license": "Apache-2.0", "dependencies": {"@opentelemetry/api": "^1.4.0", "tdigest": "^0.1.1"}, "engines": {"node": "^16 || ^18 || >=20"}}, "node_modules/prop-types": {"version": "15.8.1", "license": "MIT", "dependencies": {"loose-envify": "^1.4.0", "object-assign": "^4.1.1", "react-is": "^16.13.1"}}, "node_modules/property-expr": {"version": "2.0.6", "license": "MIT"}, "node_modules/proxy-from-env": {"version": "1.1.0", "license": "MIT"}, "node_modules/psl": {"version": "1.9.0", "license": "MIT"}, "node_modules/pump": {"version": "3.0.0", "license": "MIT", "dependencies": {"end-of-stream": "^1.1.0", "once": "^1.3.1"}}, "node_modules/punycode": {"version": "2.3.1", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/qs": {"version": "6.10.4", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"side-channel": "^1.0.4"}, "engines": {"node": ">=0.6"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/querystringify": {"version": "2.2.0", "license": "MIT"}, "node_modules/queue": {"version": "6.0.2", "license": "MIT", "dependencies": {"inherits": "~2.0.3"}}, "node_modules/queue-microtask": {"version": "1.2.3", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT"}, "node_modules/raf": {"version": "3.4.1", "resolved": "https://registry.npmjs.org/raf/-/raf-3.4.1.tgz", "integrity": "sha512-Sq4CW4QhwOHE8ucn6J34MqtZCeWFP2aQSmrlroYgqAV1PjStIhJXxYuTgUIfkEk7zTLjmIjLmU5q+fbD1NnOJA==", "optional": true, "dependencies": {"performance-now": "^2.1.0"}}, "node_modules/react": {"version": "18.2.0", "license": "MIT", "dependencies": {"loose-envify": "^1.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/react-big-calendar": {"version": "1.17.1", "resolved": "https://registry.npmjs.org/react-big-calendar/-/react-big-calendar-1.17.1.tgz", "integrity": "sha512-LltUAMSGODWQBKx4013bRe6R0jaINV9hrs970+F860KedpozwRGGMT66esV9mA3mAhfSKoazF/QH1WCyLkXYZA==", "license": "MIT", "dependencies": {"@babel/runtime": "^7.20.7", "clsx": "^1.2.1", "date-arithmetic": "^4.1.0", "dayjs": "^1.11.7", "dom-helpers": "^5.2.1", "globalize": "^0.1.1", "invariant": "^2.2.4", "lodash": "^4.17.21", "lodash-es": "^4.17.21", "luxon": "^3.2.1", "memoize-one": "^6.0.0", "moment": "^2.29.4", "moment-timezone": "^0.5.40", "prop-types": "^15.8.1", "react-overlays": "^5.2.1", "uncontrollable": "^7.2.1"}, "peerDependencies": {"react": "^16.14.0 || ^17 || ^18", "react-dom": "^16.14.0 || ^17 || ^18"}}, "node_modules/react-big-calendar/node_modules/clsx": {"version": "1.2.1", "resolved": "https://registry.npmjs.org/clsx/-/clsx-1.2.1.tgz", "integrity": "sha512-EcR6r5a8bj6pu3ycsa/E/cKVGuTgZJZdsyUYHOksG/UHIiKfjxzRxYJpyVBwYaQeOvghal9fcc4PidlgzugAQg==", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/react-calendar": {"version": "5.0.0", "resolved": "https://registry.npmjs.org/react-calendar/-/react-calendar-5.0.0.tgz", "integrity": "sha512-bHcE5e5f+VUKLd4R19BGkcSQLpuwjKBVG0fKz74cwPW5xDfNsReHdDbfd4z3mdjuUuZzVtw4Q920mkwK5/ZOEg==", "dependencies": {"@wojtekmaj/date-utils": "^1.1.3", "clsx": "^2.0.0", "get-user-locale": "^2.2.1", "warning": "^4.0.0"}, "funding": {"url": "https://github.com/wojtekmaj/react-calendar?sponsor=1"}, "peerDependencies": {"@types/react": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0", "react": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0", "react-dom": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "node_modules/react-chartjs-2": {"version": "5.2.0", "license": "MIT", "peerDependencies": {"chart.js": "^4.1.1", "react": "^16.8.0 || ^17.0.0 || ^18.0.0"}}, "node_modules/react-clientside-effect": {"version": "1.2.6", "resolved": "https://registry.npmjs.org/react-clientside-effect/-/react-clientside-effect-1.2.6.tgz", "integrity": "sha512-XGGGRQAKY+q25Lz9a/4EPqom7WRjz3z9R2k4jhVKA/puQFH/5Nt27vFZYql4m4NVNdUvX8PS3O7r/Zzm7cjUlg==", "license": "MIT", "dependencies": {"@babel/runtime": "^7.12.13"}, "peerDependencies": {"react": "^15.3.0 || ^16.0.0 || ^17.0.0 || ^18.0.0"}}, "node_modules/react-date-picker": {"version": "11.0.0", "resolved": "https://registry.npmjs.org/react-date-picker/-/react-date-picker-11.0.0.tgz", "integrity": "sha512-l+siu5HSZ/ciGL1293KCAHl4o9aD5rw16V4tB0C43h7QbMv2dWGgj7Dxgt8iztLaPVtEfOt/+sxNiTYw4WVq6A==", "dependencies": {"@wojtekmaj/date-utils": "^1.1.3", "clsx": "^2.0.0", "get-user-locale": "^2.2.1", "make-event-props": "^1.6.0", "react-calendar": "^5.0.0", "react-fit": "^2.0.0", "update-input-width": "^1.4.0"}, "funding": {"url": "https://github.com/wojtekmaj/react-date-picker?sponsor=1"}, "peerDependencies": {"@types/react": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0", "react": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0", "react-dom": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "node_modules/react-day-picker": {"version": "8.10.1", "resolved": "https://registry.npmjs.org/react-day-picker/-/react-day-picker-8.10.1.tgz", "integrity": "sha512-TMx7fNbhLk15eqcMt+7Z7S2KF7mfTId/XJDjKE8f+IUcFn0l08/kI4FiYTL/0yuOLmEcbR4Fwe3GJf/NiiMnPA==", "license": "MIT", "funding": {"type": "individual", "url": "https://github.com/sponsors/gpbl"}, "peerDependencies": {"date-fns": "^2.28.0 || ^3.0.0", "react": "^16.8.0 || ^17.0.0 || ^18.0.0"}}, "node_modules/react-dom": {"version": "18.2.0", "license": "MIT", "dependencies": {"loose-envify": "^1.1.0", "scheduler": "^0.23.0"}, "peerDependencies": {"react": "^18.2.0"}}, "node_modules/react-dom/node_modules/scheduler": {"version": "0.23.2", "license": "MIT", "dependencies": {"loose-envify": "^1.1.0"}}, "node_modules/react-dropzone": {"version": "14.3.8", "resolved": "https://registry.npmjs.org/react-dropzone/-/react-dropzone-14.3.8.tgz", "integrity": "sha512-sBgODnq+lcA4P296DY4wacOZz3JFpD99fp+hb//iBO2HHnyeZU3FwWyXJ6salNpqQdsZrgMrotuko/BdJMV8Ug==", "dependencies": {"attr-accept": "^2.2.4", "file-selector": "^2.1.0", "prop-types": "^15.8.1"}, "engines": {"node": ">= 10.13"}, "peerDependencies": {"react": ">= 16.8 || 18.0.0"}}, "node_modules/react-fast-compare": {"version": "3.2.1", "resolved": "https://registry.npmjs.org/react-fast-compare/-/react-fast-compare-3.2.1.tgz", "integrity": "sha512-xTYf9zFim2pEif/Fw16dBiXpe0hoy5PxcD8+OwBnTtNLfIm3g6WxhKNurY+6OmdH1u6Ta/W/Vl6vjbYP1MFnDg==", "license": "MIT"}, "node_modules/react-fit": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/react-fit/-/react-fit-2.0.1.tgz", "integrity": "sha512-Eip6ALs/+6Jv82Si0I9UnfysdwVlAhkkZRycgmMdnj7jwUg69SVFp84ICxwB8zszkfvJJ2MGAAo9KAYM8ZUykQ==", "dependencies": {"detect-element-overflow": "^1.4.0", "warning": "^4.0.0"}, "funding": {"url": "https://github.com/wojtekmaj/react-fit?sponsor=1"}, "peerDependencies": {"@types/react": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0", "react": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0", "react-dom": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0"}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "node_modules/react-focus-lock": {"version": "2.13.2", "resolved": "https://registry.npmjs.org/react-focus-lock/-/react-focus-lock-2.13.2.tgz", "integrity": "sha512-T/7bsofxYqnod2xadvuwjGKHOoL5GH7/EIPI5UyEvaU/c2CcphvGI371opFtuY/SYdbMsNiuF4HsHQ50nA/TKQ==", "license": "MIT", "dependencies": {"@babel/runtime": "^7.0.0", "focus-lock": "^1.3.5", "prop-types": "^15.6.2", "react-clientside-effect": "^1.2.6", "use-callback-ref": "^1.3.2", "use-sidecar": "^1.1.2"}, "peerDependencies": {"@types/react": "^16.8.0 || ^17.0.0 || ^18.0.0", "react": "^16.8.0 || ^17.0.0 || ^18.0.0"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "node_modules/react-hook-form": {"version": "7.53.2", "resolved": "https://registry.npmjs.org/react-hook-form/-/react-hook-form-7.53.2.tgz", "integrity": "sha512-YVel6fW5sOeedd1524pltpHX+jgU2u3DSDtXEaBORNdqiNrsX/nUI/iGXONegttg0mJVnfrIkiV0cmTU6Oo2xw==", "license": "MIT", "engines": {"node": ">=18.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/react-hook-form"}, "peerDependencies": {"react": "^16.8.0 || ^17 || ^18 || ^19"}}, "node_modules/react-icons": {"version": "4.12.0", "resolved": "https://registry.npmjs.org/react-icons/-/react-icons-4.12.0.tgz", "integrity": "sha512-IBaDuHiShdZqmfc/TwHu6+d6k2ltNCf3AszxNmjJc1KUfXdEeRJOKyNvLmAHaarhzGmTSVygNdyu8/opXv2gaw==", "license": "MIT", "peerDependencies": {"react": "*"}}, "node_modules/react-is": {"version": "16.13.1", "license": "MIT"}, "node_modules/react-lifecycles-compat": {"version": "3.0.4", "resolved": "https://registry.npmjs.org/react-lifecycles-compat/-/react-lifecycles-compat-3.0.4.tgz", "integrity": "sha512-fBASbA6LnOU9dOU2eW7aQ8xmYBSXUIWr+UmF9b1efZBazGNO+rcXT/icdKnYm2pTwcRylVUYwW7H1PHfLekVzA==", "license": "MIT"}, "node_modules/react-overlays": {"version": "5.2.1", "resolved": "https://registry.npmjs.org/react-overlays/-/react-overlays-5.2.1.tgz", "integrity": "sha512-GLLSOLWr21CqtJn8geSwQfoJufdt3mfdsnIiQswouuQ2MMPns+ihZklxvsTDKD3cR2tF8ELbi5xUsvqVhR6WvA==", "license": "MIT", "dependencies": {"@babel/runtime": "^7.13.8", "@popperjs/core": "^2.11.6", "@restart/hooks": "^0.4.7", "@types/warning": "^3.0.0", "dom-helpers": "^5.2.0", "prop-types": "^15.7.2", "uncontrollable": "^7.2.1", "warning": "^4.0.3"}, "peerDependencies": {"react": ">=16.3.0", "react-dom": ">=16.3.0"}}, "node_modules/react-redux": {"version": "8.1.1", "license": "MIT", "dependencies": {"@babel/runtime": "^7.12.1", "@types/hoist-non-react-statics": "^3.3.1", "@types/use-sync-external-store": "^0.0.3", "hoist-non-react-statics": "^3.3.2", "react-is": "^18.0.0", "use-sync-external-store": "^1.0.0"}, "peerDependencies": {"@types/react": "^16.8 || ^17.0 || ^18.0", "@types/react-dom": "^16.8 || ^17.0 || ^18.0", "react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "react-native": ">=0.59", "redux": "^4 || ^5.0.0-beta.0"}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}, "react-dom": {"optional": true}, "react-native": {"optional": true}, "redux": {"optional": true}}}, "node_modules/react-redux/node_modules/react-is": {"version": "18.3.1", "license": "MIT"}, "node_modules/react-remove-scroll": {"version": "2.6.0", "resolved": "https://registry.npmjs.org/react-remove-scroll/-/react-remove-scroll-2.6.0.tgz", "integrity": "sha512-I2U4JVEsQenxDAKaVa3VZ/JeJZe0/2DxPWL8Tj8yLKctQJQiZM52pn/GWFpSp8dftjM3pSAHVJZscAnC/y+ySQ==", "license": "MIT", "dependencies": {"react-remove-scroll-bar": "^2.3.6", "react-style-singleton": "^2.2.1", "tslib": "^2.1.0", "use-callback-ref": "^1.3.0", "use-sidecar": "^1.1.2"}, "engines": {"node": ">=10"}, "peerDependencies": {"@types/react": "^16.8.0 || ^17.0.0 || ^18.0.0", "react": "^16.8.0 || ^17.0.0 || ^18.0.0"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "node_modules/react-remove-scroll-bar": {"version": "2.3.6", "license": "MIT", "dependencies": {"react-style-singleton": "^2.2.1", "tslib": "^2.0.0"}, "engines": {"node": ">=10"}, "peerDependencies": {"@types/react": "^16.8.0 || ^17.0.0 || ^18.0.0", "react": "^16.8.0 || ^17.0.0 || ^18.0.0"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "node_modules/react-select": {"version": "5.7.3", "license": "MIT", "dependencies": {"@babel/runtime": "^7.12.0", "@emotion/cache": "^11.4.0", "@emotion/react": "^11.8.1", "@floating-ui/dom": "^1.0.1", "@types/react-transition-group": "^4.4.0", "memoize-one": "^6.0.0", "prop-types": "^15.6.0", "react-transition-group": "^4.3.0", "use-isomorphic-layout-effect": "^1.1.2"}, "peerDependencies": {"react": "^16.8.0 || ^17.0.0 || ^18.0.0", "react-dom": "^16.8.0 || ^17.0.0 || ^18.0.0"}}, "node_modules/react-signature-canvas": {"version": "1.0.6", "resolved": "https://registry.npmjs.org/react-signature-canvas/-/react-signature-canvas-1.0.6.tgz", "integrity": "sha512-NoMHomYu9HxFeLjUGbIeV9abPdWSROfFxFNDekGdwmmaIx+w5ziOEiU2C34X0Ao4GxFnwqyUy/BpYlA4lCD1CA==", "dependencies": {"signature_pad": "^2.3.2", "trim-canvas": "^0.1.0"}, "peerDependencies": {"prop-types": "^15.5.8", "react": "0.14 - 18", "react-dom": "0.14 - 18"}}, "node_modules/react-smooth": {"version": "4.0.1", "license": "MIT", "dependencies": {"fast-equals": "^5.0.1", "prop-types": "^15.8.1", "react-transition-group": "^4.4.5"}, "peerDependencies": {"react": "^16.8.0 || ^17.0.0 || ^18.0.0", "react-dom": "^16.8.0 || ^17.0.0 || ^18.0.0"}}, "node_modules/react-style-singleton": {"version": "2.2.1", "license": "MIT", "dependencies": {"get-nonce": "^1.0.0", "invariant": "^2.2.4", "tslib": "^2.0.0"}, "engines": {"node": ">=10"}, "peerDependencies": {"@types/react": "^16.8.0 || ^17.0.0 || ^18.0.0", "react": "^16.8.0 || ^17.0.0 || ^18.0.0"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "node_modules/react-transition-group": {"version": "4.4.5", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"@babel/runtime": "^7.5.5", "dom-helpers": "^5.0.1", "loose-envify": "^1.4.0", "prop-types": "^15.6.2"}, "peerDependencies": {"react": ">=16.6.0", "react-dom": ">=16.6.0"}}, "node_modules/react-virtuoso": {"version": "4.10.1", "license": "MIT", "engines": {"node": ">=10"}, "peerDependencies": {"react": ">=16 || >=17 || >= 18", "react-dom": ">=16 || >=17 || >= 18"}}, "node_modules/react-window": {"version": "1.8.10", "license": "MIT", "dependencies": {"@babel/runtime": "^7.0.0", "memoize-one": ">=3.1.1 <6"}, "engines": {"node": ">8.0.0"}, "peerDependencies": {"react": "^15.0.0 || ^16.0.0 || ^17.0.0 || ^18.0.0", "react-dom": "^15.0.0 || ^16.0.0 || ^17.0.0 || ^18.0.0"}}, "node_modules/react-window/node_modules/memoize-one": {"version": "5.2.1", "license": "MIT"}, "node_modules/react-zoom-pan-pinch": {"version": "3.1.0", "license": "MIT", "engines": {"node": ">=8", "npm": ">=5"}, "peerDependencies": {"react": "*", "react-dom": "*"}}, "node_modules/read-cache": {"version": "1.0.0", "license": "MIT", "dependencies": {"pify": "^2.3.0"}}, "node_modules/readable-stream": {"version": "2.3.8", "resolved": "https://registry.npmjs.org/readable-stream/-/readable-stream-2.3.8.tgz", "integrity": "sha512-8p0AUk4XODgIewSi0l8Epjs+EVnWiK7NoDIEGU0HhE7+ZyY8D1IMY7odu5lRrFXGg71L15KG8QrPmum45RTtdA==", "license": "MIT", "dependencies": {"core-util-is": "~1.0.0", "inherits": "~2.0.3", "isarray": "~1.0.0", "process-nextick-args": "~2.0.0", "safe-buffer": "~5.1.1", "string_decoder": "~1.1.1", "util-deprecate": "~1.0.1"}}, "node_modules/readable-stream/node_modules/isarray": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/isarray/-/isarray-1.0.0.tgz", "integrity": "sha512-VLghIWNM6ELQzo7zwmcg0NmTVyWKYjvIeM83yjp0wRDTmUnrM678fQbcKBo6n2CJEF0szoG//ytg+TKla89ALQ==", "license": "MIT"}, "node_modules/readable-stream/node_modules/safe-buffer": {"version": "5.1.2", "resolved": "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.1.2.tgz", "integrity": "sha512-Gd2UZBJDkXlY7GbJxfsE8/nvKkUEU1G38c1siN6QP6a9PT9MmHB8GnpscSmMJSoF8LOIrt8ud/wPtojys4G6+g==", "license": "MIT"}, "node_modules/readable-stream/node_modules/string_decoder": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/string_decoder/-/string_decoder-1.1.1.tgz", "integrity": "sha512-n/ShnvDi6FHbbVfviro+WojiFzv+s8MPMHBczVePfUpDJLwoLT0ht1l4YwBCbi8pJAveEEdnkHyPyTP/mzRfwg==", "license": "MIT", "dependencies": {"safe-buffer": "~5.1.0"}}, "node_modules/readdirp": {"version": "3.6.0", "license": "MIT", "dependencies": {"picomatch": "^2.2.1"}, "engines": {"node": ">=8.10.0"}}, "node_modules/recharts": {"version": "2.13.0-alpha.4", "license": "MIT", "dependencies": {"clsx": "^2.0.0", "eventemitter3": "^4.0.1", "lodash": "^4.17.21", "react-is": "^18.3.1", "react-smooth": "^4.0.0", "recharts-scale": "^0.4.4", "tiny-invariant": "^1.3.1", "victory-vendor": "^36.6.8"}, "engines": {"node": ">=14"}, "peerDependencies": {"react": "^16.0.0 || ^17.0.0 || ^18.0.0", "react-dom": "^16.0.0 || ^17.0.0 || ^18.0.0"}}, "node_modules/recharts-scale": {"version": "0.4.5", "license": "MIT", "dependencies": {"decimal.js-light": "^2.4.1"}}, "node_modules/recharts/node_modules/react-is": {"version": "18.3.1", "license": "MIT"}, "node_modules/redux": {"version": "4.2.1", "license": "MIT", "dependencies": {"@babel/runtime": "^7.9.2"}}, "node_modules/redux-thunk": {"version": "2.4.2", "license": "MIT", "peerDependencies": {"redux": "^4"}}, "node_modules/reflect.getprototypeof": {"version": "1.0.6", "license": "MIT", "dependencies": {"call-bind": "^1.0.7", "define-properties": "^1.2.1", "es-abstract": "^1.23.1", "es-errors": "^1.3.0", "get-intrinsic": "^1.2.4", "globalthis": "^1.0.3", "which-builtin-type": "^1.1.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/regenerator-runtime": {"version": "0.13.11", "resolved": "https://registry.npmjs.org/regenerator-runtime/-/regenerator-runtime-0.13.11.tgz", "integrity": "sha512-kY1AZVr2Ra+t+piVaJ4gxaFaReZVH40AKNo7UCX6W+dEwBo/2oZJzqfuN1qLq1oL45o56cPaTXELwrTh8Fpggg==", "optional": true}, "node_modules/regexp.prototype.flags": {"version": "1.5.2", "license": "MIT", "dependencies": {"call-bind": "^1.0.6", "define-properties": "^1.2.1", "es-errors": "^1.3.0", "set-function-name": "^2.0.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/request-progress": {"version": "3.0.0", "license": "MIT", "dependencies": {"throttleit": "^1.0.0"}}, "node_modules/require-from-string": {"version": "2.0.2", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/requires-port": {"version": "1.0.0", "license": "MIT"}, "node_modules/reselect": {"version": "4.1.8", "license": "MIT"}, "node_modules/resolve": {"version": "1.22.8", "license": "MIT", "dependencies": {"is-core-module": "^2.13.0", "path-parse": "^1.0.7", "supports-preserve-symlinks-flag": "^1.0.0"}, "bin": {"resolve": "bin/resolve"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/resolve-from": {"version": "4.0.0", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/resolve-pkg-maps": {"version": "1.0.0", "license": "MIT", "funding": {"url": "https://github.com/privatenumber/resolve-pkg-maps?sponsor=1"}}, "node_modules/restore-cursor": {"version": "3.1.0", "license": "MIT", "dependencies": {"onetime": "^5.1.0", "signal-exit": "^3.0.2"}, "engines": {"node": ">=8"}}, "node_modules/restructure": {"version": "3.0.2", "license": "MIT"}, "node_modules/reusify": {"version": "1.0.4", "license": "MIT", "engines": {"iojs": ">=1.0.0", "node": ">=0.10.0"}}, "node_modules/rfdc": {"version": "1.4.1", "license": "MIT"}, "node_modules/rgbcolor": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/rgbcolor/-/rgbcolor-1.0.1.tgz", "integrity": "sha512-9aZLIrhRaD97sgVhtJOW6ckOEh6/GnvQtdVNfdZ6s67+3/XwLS9lBcQYzEEhYVeUowN7pRzMLsyGhK2i/xvWbw==", "optional": true, "engines": {"node": ">= 0.8.15"}}, "node_modules/rimraf": {"version": "3.0.2", "license": "ISC", "dependencies": {"glob": "^7.1.3"}, "bin": {"rimraf": "bin.js"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/rimraf/node_modules/glob": {"version": "7.2.3", "resolved": "https://registry.npmjs.org/glob/-/glob-7.2.3.tgz", "integrity": "sha512-nFR0zLpU2YCaRxwoCJvL6UvCH2JFyFVIvwTLsIf21AuHlMskA1hhTdk+LlYJtOlYt9v6dvszD2BGRqBL+iQK9Q==", "deprecated": "Glob versions prior to v9 are no longer supported", "license": "ISC", "dependencies": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.1.1", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}, "engines": {"node": "*"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/run-parallel": {"version": "1.2.0", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT", "dependencies": {"queue-microtask": "^1.2.2"}}, "node_modules/rxjs": {"version": "7.8.1", "license": "Apache-2.0", "dependencies": {"tslib": "^2.1.0"}}, "node_modules/safe-array-concat": {"version": "1.1.2", "license": "MIT", "dependencies": {"call-bind": "^1.0.7", "get-intrinsic": "^1.2.4", "has-symbols": "^1.0.3", "isarray": "^2.0.5"}, "engines": {"node": ">=0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/safe-buffer": {"version": "5.2.1", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT"}, "node_modules/safe-regex-test": {"version": "1.0.3", "license": "MIT", "dependencies": {"call-bind": "^1.0.6", "es-errors": "^1.3.0", "is-regex": "^1.1.4"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/safer-buffer": {"version": "2.1.2", "license": "MIT"}, "node_modules/scheduler": {"version": "0.17.0", "license": "MIT", "dependencies": {"loose-envify": "^1.1.0", "object-assign": "^4.1.1"}}, "node_modules/semver": {"version": "7.6.3", "license": "ISC", "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/server-only": {"version": "0.0.1", "resolved": "https://registry.npmjs.org/server-only/-/server-only-0.0.1.tgz", "integrity": "sha512-qepMx2JxAa5jjfzxG79yPPq+8BuFToHd1hm7kI+Z4zAq1ftQiP7HcxMhDDItrbtwVeLg/cY2JnKnrcFkmiswNA=="}, "node_modules/set-function-length": {"version": "1.2.2", "license": "MIT", "dependencies": {"define-data-property": "^1.1.4", "es-errors": "^1.3.0", "function-bind": "^1.1.2", "get-intrinsic": "^1.2.4", "gopd": "^1.0.1", "has-property-descriptors": "^1.0.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/set-function-name": {"version": "2.0.2", "license": "MIT", "dependencies": {"define-data-property": "^1.1.4", "es-errors": "^1.3.0", "functions-have-names": "^1.2.3", "has-property-descriptors": "^1.0.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/setimmediate": {"version": "1.0.5", "resolved": "https://registry.npmjs.org/setimmediate/-/setimmediate-1.0.5.tgz", "integrity": "sha512-MATJdZp8sLqDl/68LfQmbP8zKPLQNV6BIZoIgrscFDQ+RsvK/BxeDQOgyxKKoh0y/8h3BqVFnCqQ/gd+reiIXA==", "license": "MIT"}, "node_modules/shebang-command": {"version": "2.0.0", "license": "MIT", "dependencies": {"shebang-regex": "^3.0.0"}, "engines": {"node": ">=8"}}, "node_modules/shebang-regex": {"version": "3.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/side-channel": {"version": "1.0.6", "license": "MIT", "dependencies": {"call-bind": "^1.0.7", "es-errors": "^1.3.0", "get-intrinsic": "^1.2.4", "object-inspect": "^1.13.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/signal-exit": {"version": "3.0.7", "license": "ISC"}, "node_modules/signature_pad": {"version": "2.3.2", "resolved": "https://registry.npmjs.org/signature_pad/-/signature_pad-2.3.2.tgz", "integrity": "sha512-peYXLxOsIY6MES2TrRLDiNg2T++8gGbpP2yaC+6Ohtxr+a2dzoaqWosWDY9sWqTAAk6E/TyQO+LJw9zQwyu5kA=="}, "node_modules/simple-swizzle": {"version": "0.2.2", "license": "MIT", "dependencies": {"is-arrayish": "^0.3.1"}}, "node_modules/simple-swizzle/node_modules/is-arrayish": {"version": "0.3.2", "license": "MIT"}, "node_modules/slash": {"version": "3.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/slice-ansi": {"version": "5.0.0", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^6.0.0", "is-fullwidth-code-point": "^4.0.0"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/slice-ansi?sponsor=1"}}, "node_modules/slice-ansi/node_modules/ansi-styles": {"version": "6.2.1", "dev": true, "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/sonner": {"version": "1.5.0", "license": "MIT", "peerDependencies": {"react": "^18.0.0", "react-dom": "^18.0.0"}}, "node_modules/source-map": {"version": "0.5.7", "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/source-map-js": {"version": "1.2.0", "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/ssf": {"version": "0.11.2", "license": "Apache-2.0", "dependencies": {"frac": "~1.1.2"}, "engines": {"node": ">=0.8"}}, "node_modules/sshpk": {"version": "1.18.0", "license": "MIT", "dependencies": {"asn1": "~0.2.3", "assert-plus": "^1.0.0", "bcrypt-pbkdf": "^1.0.0", "dashdash": "^1.12.0", "ecc-jsbn": "~0.1.1", "getpass": "^0.1.1", "jsbn": "~0.1.0", "safer-buffer": "^2.0.2", "tweetnacl": "~0.14.0"}, "bin": {"sshpk-conv": "bin/sshpk-conv", "sshpk-sign": "bin/sshpk-sign", "sshpk-verify": "bin/sshpk-verify"}, "engines": {"node": ">=0.10.0"}}, "node_modules/stackblur-canvas": {"version": "2.7.0", "resolved": "https://registry.npmjs.org/stackblur-canvas/-/stackblur-canvas-2.7.0.tgz", "integrity": "sha512-yf7OENo23AGJhBriGx0QivY5JP6Y1HbrrDI6WLt6C5auYZXlQrheoY8hD4ibekFKz1HOfE48Ww8kMWMnJD/zcQ==", "optional": true, "engines": {"node": ">=0.1.14"}}, "node_modules/stop-iteration-iterator": {"version": "1.0.0", "license": "MIT", "dependencies": {"internal-slot": "^1.0.4"}, "engines": {"node": ">= 0.4"}}, "node_modules/streamsearch": {"version": "1.1.0", "engines": {"node": ">=10.0.0"}}, "node_modules/string_decoder": {"version": "1.3.0", "license": "MIT", "dependencies": {"safe-buffer": "~5.2.0"}}, "node_modules/string-argv": {"version": "0.3.2", "dev": true, "license": "MIT", "engines": {"node": ">=0.6.19"}}, "node_modules/string-width": {"version": "4.2.3", "license": "MIT", "dependencies": {"emoji-regex": "^8.0.0", "is-fullwidth-code-point": "^3.0.0", "strip-ansi": "^6.0.1"}, "engines": {"node": ">=8"}}, "node_modules/string-width-cjs": {"name": "string-width", "version": "4.2.3", "license": "MIT", "dependencies": {"emoji-regex": "^8.0.0", "is-fullwidth-code-point": "^3.0.0", "strip-ansi": "^6.0.1"}, "engines": {"node": ">=8"}}, "node_modules/string-width-cjs/node_modules/emoji-regex": {"version": "8.0.0", "license": "MIT"}, "node_modules/string-width-cjs/node_modules/is-fullwidth-code-point": {"version": "3.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/string-width/node_modules/emoji-regex": {"version": "8.0.0", "license": "MIT"}, "node_modules/string-width/node_modules/is-fullwidth-code-point": {"version": "3.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/string.prototype.includes": {"version": "2.0.0", "license": "MIT", "dependencies": {"define-properties": "^1.1.3", "es-abstract": "^1.17.5"}}, "node_modules/string.prototype.matchall": {"version": "4.0.11", "license": "MIT", "dependencies": {"call-bind": "^1.0.7", "define-properties": "^1.2.1", "es-abstract": "^1.23.2", "es-errors": "^1.3.0", "es-object-atoms": "^1.0.0", "get-intrinsic": "^1.2.4", "gopd": "^1.0.1", "has-symbols": "^1.0.3", "internal-slot": "^1.0.7", "regexp.prototype.flags": "^1.5.2", "set-function-name": "^2.0.2", "side-channel": "^1.0.6"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/string.prototype.repeat": {"version": "1.0.0", "license": "MIT", "dependencies": {"define-properties": "^1.1.3", "es-abstract": "^1.17.5"}}, "node_modules/string.prototype.trim": {"version": "1.2.9", "license": "MIT", "dependencies": {"call-bind": "^1.0.7", "define-properties": "^1.2.1", "es-abstract": "^1.23.0", "es-object-atoms": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/string.prototype.trimend": {"version": "1.0.8", "license": "MIT", "dependencies": {"call-bind": "^1.0.7", "define-properties": "^1.2.1", "es-object-atoms": "^1.0.0"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/string.prototype.trimstart": {"version": "1.0.8", "license": "MIT", "dependencies": {"call-bind": "^1.0.7", "define-properties": "^1.2.1", "es-object-atoms": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/strip-ansi": {"version": "6.0.1", "license": "MIT", "dependencies": {"ansi-regex": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/strip-ansi-cjs": {"name": "strip-ansi", "version": "6.0.1", "license": "MIT", "dependencies": {"ansi-regex": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/strip-bom": {"version": "3.0.0", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/strip-final-newline": {"version": "2.0.0", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/strip-json-comments": {"version": "3.1.1", "license": "MIT", "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/styled-jsx": {"version": "5.1.1", "license": "MIT", "dependencies": {"client-only": "0.0.1"}, "engines": {"node": ">= 12.0.0"}, "peerDependencies": {"react": ">= 16.8.0 || 17.x.x || ^18.0.0-0"}, "peerDependenciesMeta": {"@babel/core": {"optional": true}, "babel-plugin-macros": {"optional": true}}}, "node_modules/stylis": {"version": "4.2.0", "license": "MIT"}, "node_modules/sucrase": {"version": "3.35.0", "license": "MIT", "dependencies": {"@jridgewell/gen-mapping": "^0.3.2", "commander": "^4.0.0", "glob": "^10.3.10", "lines-and-columns": "^1.1.6", "mz": "^2.7.0", "pirates": "^4.0.1", "ts-interface-checker": "^0.1.9"}, "bin": {"sucrase": "bin/sucrase", "sucrase-node": "bin/sucrase-node"}, "engines": {"node": ">=16 || 14 >=14.17"}}, "node_modules/sucrase/node_modules/brace-expansion": {"version": "2.0.1", "license": "MIT", "dependencies": {"balanced-match": "^1.0.0"}}, "node_modules/sucrase/node_modules/commander": {"version": "4.1.1", "license": "MIT", "engines": {"node": ">= 6"}}, "node_modules/sucrase/node_modules/glob": {"version": "10.4.5", "license": "ISC", "dependencies": {"foreground-child": "^3.1.0", "jackspeak": "^3.1.2", "minimatch": "^9.0.4", "minipass": "^7.1.2", "package-json-from-dist": "^1.0.0", "path-scurry": "^1.11.1"}, "bin": {"glob": "dist/esm/bin.mjs"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/sucrase/node_modules/jackspeak": {"version": "3.4.3", "license": "BlueOak-1.0.0", "dependencies": {"@isaacs/cliui": "^8.0.2"}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "optionalDependencies": {"@pkgjs/parseargs": "^0.11.0"}}, "node_modules/sucrase/node_modules/minimatch": {"version": "9.0.5", "license": "ISC", "dependencies": {"brace-expansion": "^2.0.1"}, "engines": {"node": ">=16 || 14 >=14.17"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/supports-color": {"version": "5.5.0", "license": "MIT", "dependencies": {"has-flag": "^3.0.0"}, "engines": {"node": ">=4"}}, "node_modules/supports-preserve-symlinks-flag": {"version": "1.0.0", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/svg-arc-to-cubic-bezier": {"version": "3.2.0", "license": "ISC"}, "node_modules/svg-pathdata": {"version": "6.0.3", "resolved": "https://registry.npmjs.org/svg-pathdata/-/svg-pathdata-6.0.3.tgz", "integrity": "sha512-qsjeeq5YjBZ5eMdFuUa4ZosMLxgr5RZ+F+Y1OrDhuOCEInRMA3x74XdBtggJcj9kOeInz0WE+LgCPDkZFlBYJw==", "optional": true, "engines": {"node": ">=12.0.0"}}, "node_modules/sweetalert2": {"version": "11.10.0", "license": "MIT", "funding": {"type": "individual", "url": "https://github.com/sponsors/limonte"}}, "node_modules/sweetalert2-react-content": {"version": "5.1.0", "resolved": "https://registry.npmjs.org/sweetalert2-react-content/-/sweetalert2-react-content-5.1.0.tgz", "integrity": "sha512-SBh41SdyHDY9NzwrIG6LACbClCMxIlEFP86tGVr/B4zGD4H2gGXB8o7UAJRc/RtBd/iQL9hIvuCAkrk0AgKEMA==", "license": "MIT", "peerDependencies": {"react": "^18.0.0 || ^19.0.0", "react-dom": "^18.0.0 || ^19.0.0", "sweetalert2": "^11.0.0"}}, "node_modules/tailwind-merge": {"version": "2.5.2", "license": "MIT", "funding": {"type": "github", "url": "https://github.com/sponsors/dcastil"}}, "node_modules/tailwindcss": {"version": "3.3.2", "license": "MIT", "dependencies": {"@alloc/quick-lru": "^5.2.0", "arg": "^5.0.2", "chokidar": "^3.5.3", "didyoumean": "^1.2.2", "dlv": "^1.1.3", "fast-glob": "^3.2.12", "glob-parent": "^6.0.2", "is-glob": "^4.0.3", "jiti": "^1.18.2", "lilconfig": "^2.1.0", "micromatch": "^4.0.5", "normalize-path": "^3.0.0", "object-hash": "^3.0.0", "picocolors": "^1.0.0", "postcss": "^8.4.23", "postcss-import": "^15.1.0", "postcss-js": "^4.0.1", "postcss-load-config": "^4.0.1", "postcss-nested": "^6.0.1", "postcss-selector-parser": "^6.0.11", "postcss-value-parser": "^4.2.0", "resolve": "^1.22.2", "sucrase": "^3.32.0"}, "bin": {"tailwind": "lib/cli.js", "tailwindcss": "lib/cli.js"}, "engines": {"node": ">=14.0.0"}}, "node_modules/tailwindcss-animate": {"version": "1.0.7", "license": "MIT", "peerDependencies": {"tailwindcss": ">=3.0.0 || insiders"}}, "node_modules/tailwindcss/node_modules/object-hash": {"version": "3.0.0", "license": "MIT", "engines": {"node": ">= 6"}}, "node_modules/tapable": {"version": "2.2.1", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/tdigest": {"version": "0.1.2", "resolved": "https://registry.npmjs.org/tdigest/-/tdigest-0.1.2.tgz", "integrity": "sha512-+G0LLgjjo9BZX2MfdvPfH+MKLCrxlXSYec5DaPYP1fe6Iyhf0/fSmJ0bFiZ1F8BT6cGXl2LpltQptzjXKWEkKA==", "license": "MIT", "dependencies": {"bintrees": "1.0.2"}}, "node_modules/text-segmentation": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/text-segmentation/-/text-segmentation-1.0.3.tgz", "integrity": "sha512-iOiPUo/BGnZ6+54OsWxZidGCsdU8YbE4PSpdPinp7DeMtUJNJBoJ/ouUSTJjHkh1KntHaltHl/gDs2FC4i5+Nw==", "optional": true, "dependencies": {"utrie": "^1.0.2"}}, "node_modules/text-table": {"version": "0.2.0", "license": "MIT"}, "node_modules/thenify": {"version": "3.3.1", "license": "MIT", "dependencies": {"any-promise": "^1.0.0"}}, "node_modules/thenify-all": {"version": "1.6.0", "license": "MIT", "dependencies": {"thenify": ">= 3.1.0 < 4"}, "engines": {"node": ">=0.8"}}, "node_modules/throttleit": {"version": "1.0.1", "license": "MIT", "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/through": {"version": "2.3.8", "license": "MIT"}, "node_modules/tiny-inflate": {"version": "1.0.3", "license": "MIT"}, "node_modules/tiny-invariant": {"version": "1.3.3", "license": "MIT"}, "node_modules/tiny-warning": {"version": "1.0.3", "license": "MIT"}, "node_modules/tmp": {"version": "0.2.3", "license": "MIT", "engines": {"node": ">=14.14"}}, "node_modules/to-fast-properties": {"version": "2.0.0", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/to-regex-range": {"version": "5.0.1", "license": "MIT", "dependencies": {"is-number": "^7.0.0"}, "engines": {"node": ">=8.0"}}, "node_modules/toggle-selection": {"version": "1.0.6", "resolved": "https://registry.npmjs.org/toggle-selection/-/toggle-selection-1.0.6.tgz", "integrity": "sha512-BiZS+C1OS8g/q2RRbJmy59xpyghNBqrr6k5L/uKBGRsTfxmu3ffiRnd8mlGPUVayg8pvfi5urfnu8TU7DVOkLQ==", "license": "MIT"}, "node_modules/toposort": {"version": "2.0.2", "license": "MIT"}, "node_modules/tough-cookie": {"version": "4.1.4", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"psl": "^1.1.33", "punycode": "^2.1.1", "universalify": "^0.2.0", "url-parse": "^1.5.3"}, "engines": {"node": ">=6"}}, "node_modules/tough-cookie/node_modules/universalify": {"version": "0.2.0", "license": "MIT", "engines": {"node": ">= 4.0.0"}}, "node_modules/tr46": {"version": "0.0.3", "license": "MIT"}, "node_modules/trim-canvas": {"version": "0.1.2", "resolved": "https://registry.npmjs.org/trim-canvas/-/trim-canvas-0.1.2.tgz", "integrity": "sha512-nd4Ga3iLFV94mdhW9JFMLpQbHUyCQuhFOD71PEAt1NjtMD5wbZctzhX8c3agHNybMR5zXD1XTGoIEWk995E6pQ=="}, "node_modules/ts-interface-checker": {"version": "0.1.13", "license": "Apache-2.0"}, "node_modules/tsconfig-paths": {"version": "3.15.0", "license": "MIT", "dependencies": {"@types/json5": "^0.0.29", "json5": "^1.0.2", "minimist": "^1.2.6", "strip-bom": "^3.0.0"}}, "node_modules/tslib": {"version": "2.7.0", "license": "0BSD"}, "node_modules/tsutils": {"version": "3.21.0", "license": "MIT", "dependencies": {"tslib": "^1.8.1"}, "engines": {"node": ">= 6"}, "peerDependencies": {"typescript": ">=2.8.0 || >= 3.2.0-dev || >= 3.3.0-dev || >= 3.4.0-dev || >= 3.5.0-dev || >= 3.6.0-dev || >= 3.6.0-beta || >= 3.7.0-dev || >= 3.7.0-beta"}}, "node_modules/tsutils/node_modules/tslib": {"version": "1.14.1", "license": "0BSD"}, "node_modules/tunnel-agent": {"version": "0.6.0", "license": "Apache-2.0", "dependencies": {"safe-buffer": "^5.0.1"}, "engines": {"node": "*"}}, "node_modules/tweetnacl": {"version": "0.14.5", "license": "Unlicense"}, "node_modules/type-check": {"version": "0.4.0", "license": "MIT", "dependencies": {"prelude-ls": "^1.2.1"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/type-fest": {"version": "0.21.3", "license": "(MIT OR CC0-1.0)", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/typed-array-buffer": {"version": "1.0.2", "license": "MIT", "dependencies": {"call-bind": "^1.0.7", "es-errors": "^1.3.0", "is-typed-array": "^1.1.13"}, "engines": {"node": ">= 0.4"}}, "node_modules/typed-array-byte-length": {"version": "1.0.1", "license": "MIT", "dependencies": {"call-bind": "^1.0.7", "for-each": "^0.3.3", "gopd": "^1.0.1", "has-proto": "^1.0.3", "is-typed-array": "^1.1.13"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/typed-array-byte-offset": {"version": "1.0.2", "license": "MIT", "dependencies": {"available-typed-arrays": "^1.0.7", "call-bind": "^1.0.7", "for-each": "^0.3.3", "gopd": "^1.0.1", "has-proto": "^1.0.3", "is-typed-array": "^1.1.13"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/typed-array-length": {"version": "1.0.6", "license": "MIT", "dependencies": {"call-bind": "^1.0.7", "for-each": "^0.3.3", "gopd": "^1.0.1", "has-proto": "^1.0.3", "is-typed-array": "^1.1.13", "possible-typed-array-names": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/typescript": {"version": "5.5.4", "license": "Apache-2.0", "bin": {"tsc": "bin/tsc", "tsserver": "bin/tsserver"}, "engines": {"node": ">=14.17"}}, "node_modules/unbox-primitive": {"version": "1.0.2", "license": "MIT", "dependencies": {"call-bind": "^1.0.2", "has-bigints": "^1.0.2", "has-symbols": "^1.0.3", "which-boxed-primitive": "^1.0.2"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/uncontrollable": {"version": "7.2.1", "resolved": "https://registry.npmjs.org/uncontrollable/-/uncontrollable-7.2.1.tgz", "integrity": "sha512-svtcfoTADIB0nT9nltgjujTi7BzVmwjZClOmskKu/E8FW9BXzg9os8OLr4f8Dlnk0rYWJIWr4wv9eKUXiQvQwQ==", "license": "MIT", "dependencies": {"@babel/runtime": "^7.6.3", "@types/react": ">=16.9.11", "invariant": "^2.2.4", "react-lifecycles-compat": "^3.0.4"}, "peerDependencies": {"react": ">=15.0.0"}}, "node_modules/unicode-properties": {"version": "1.4.1", "license": "MIT", "dependencies": {"base64-js": "^1.3.0", "unicode-trie": "^2.0.0"}}, "node_modules/unicode-trie": {"version": "2.0.0", "license": "MIT", "dependencies": {"pako": "^0.2.5", "tiny-inflate": "^1.0.0"}}, "node_modules/unicode-trie/node_modules/pako": {"version": "0.2.9", "license": "MIT"}, "node_modules/universalify": {"version": "2.0.1", "license": "MIT", "engines": {"node": ">= 10.0.0"}}, "node_modules/untildify": {"version": "4.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/update-browserslist-db": {"version": "1.1.0", "funding": [{"type": "opencollective", "url": "https://opencollective.com/browserslist"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/browserslist"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "dependencies": {"escalade": "^3.1.2", "picocolors": "^1.0.1"}, "bin": {"update-browserslist-db": "cli.js"}, "peerDependencies": {"browserslist": ">= 4.21.0"}}, "node_modules/update-input-width": {"version": "1.4.2", "resolved": "https://registry.npmjs.org/update-input-width/-/update-input-width-1.4.2.tgz", "integrity": "sha512-/p0XLhrQQQ4bMWD7bL9duYObwYCO1qGr8R19xcMmoMSmXuQ7/1//veUnCObQ7/iW6E2pGS6rFkS4TfH4ur7e/g==", "funding": {"url": "https://github.com/wojtekmaj/update-input-width?sponsor=1"}}, "node_modules/uri-js": {"version": "4.4.1", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"punycode": "^2.1.0"}}, "node_modules/url-parse": {"version": "1.5.10", "license": "MIT", "dependencies": {"querystringify": "^2.1.1", "requires-port": "^1.0.0"}}, "node_modules/use-callback-ref": {"version": "1.3.2", "license": "MIT", "dependencies": {"tslib": "^2.0.0"}, "engines": {"node": ">=10"}, "peerDependencies": {"@types/react": "^16.8.0 || ^17.0.0 || ^18.0.0", "react": "^16.8.0 || ^17.0.0 || ^18.0.0"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "node_modules/use-isomorphic-layout-effect": {"version": "1.1.2", "license": "MIT", "peerDependencies": {"react": "^16.8.0 || ^17.0.0 || ^18.0.0"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "node_modules/use-sidecar": {"version": "1.1.2", "license": "MIT", "dependencies": {"detect-node-es": "^1.1.0", "tslib": "^2.0.0"}, "engines": {"node": ">=10"}, "peerDependencies": {"@types/react": "^16.9.0 || ^17.0.0 || ^18.0.0", "react": "^16.8.0 || ^17.0.0 || ^18.0.0"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "node_modules/use-sync-external-store": {"version": "1.2.2", "license": "MIT", "peerDependencies": {"react": "^16.8.0 || ^17.0.0 || ^18.0.0"}}, "node_modules/util-deprecate": {"version": "1.0.2", "license": "MIT"}, "node_modules/utrie": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/utrie/-/utrie-1.0.2.tgz", "integrity": "sha512-1MLa5ouZiOmQzUbjbu9VmjLzn1QLXBhwpUa7kdLUQK+KQ5KA9I1vk5U4YHe/X2Ch7PYnJfWuWT+VbuxbGwljhw==", "optional": true, "dependencies": {"base64-arraybuffer": "^1.0.2"}}, "node_modules/uuid": {"version": "10.0.0", "resolved": "https://registry.npmjs.org/uuid/-/uuid-10.0.0.tgz", "integrity": "sha512-8XkAphELsDnEGrDxUOHB3RGvXz6TeuYSGEZBOjtTtPm2lwhGBjLgOzLHB63IUWfBpNucQjND6d3AOudO+H3RWQ==", "funding": ["https://github.com/sponsors/broofa", "https://github.com/sponsors/ctavan"], "license": "MIT", "bin": {"uuid": "dist/bin/uuid"}}, "node_modules/verror": {"version": "1.10.0", "engines": ["node >=0.6.0"], "license": "MIT", "dependencies": {"assert-plus": "^1.0.0", "core-util-is": "1.0.2", "extsprintf": "^1.2.0"}}, "node_modules/victory-vendor": {"version": "36.9.2", "license": "MIT AND ISC", "dependencies": {"@types/d3-array": "^3.0.3", "@types/d3-ease": "^3.0.0", "@types/d3-interpolate": "^3.0.1", "@types/d3-scale": "^4.0.2", "@types/d3-shape": "^3.1.0", "@types/d3-time": "^3.0.0", "@types/d3-timer": "^3.0.0", "d3-array": "^3.1.6", "d3-ease": "^3.0.1", "d3-interpolate": "^3.0.1", "d3-scale": "^4.0.2", "d3-shape": "^3.1.0", "d3-time": "^3.0.0", "d3-timer": "^3.0.1"}}, "node_modules/vite-compatible-readable-stream": {"version": "3.6.1", "license": "MIT", "dependencies": {"inherits": "^2.0.3", "string_decoder": "^1.1.1", "util-deprecate": "^1.0.1"}, "engines": {"node": ">= 6"}}, "node_modules/warning": {"version": "4.0.3", "resolved": "https://registry.npmjs.org/warning/-/warning-4.0.3.tgz", "integrity": "sha512-rpJyN222KWIvHJ/F53XSZv0Zl/accqHR8et1kpaMTD/fLCRxtV8iX8czMzY7sVZupTI3zcUTg8eycS2kNF9l6w==", "dependencies": {"loose-envify": "^1.0.0"}}, "node_modules/webidl-conversions": {"version": "3.0.1", "license": "BSD-2-<PERSON><PERSON>"}, "node_modules/whatwg-url": {"version": "5.0.0", "license": "MIT", "dependencies": {"tr46": "~0.0.3", "webidl-conversions": "^3.0.0"}}, "node_modules/which": {"version": "2.0.2", "license": "ISC", "dependencies": {"isexe": "^2.0.0"}, "bin": {"node-which": "bin/node-which"}, "engines": {"node": ">= 8"}}, "node_modules/which-boxed-primitive": {"version": "1.0.2", "license": "MIT", "dependencies": {"is-bigint": "^1.0.1", "is-boolean-object": "^1.1.0", "is-number-object": "^1.0.4", "is-string": "^1.0.5", "is-symbol": "^1.0.3"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/which-builtin-type": {"version": "1.1.4", "license": "MIT", "dependencies": {"function.prototype.name": "^1.1.6", "has-tostringtag": "^1.0.2", "is-async-function": "^2.0.0", "is-date-object": "^1.0.5", "is-finalizationregistry": "^1.0.2", "is-generator-function": "^1.0.10", "is-regex": "^1.1.4", "is-weakref": "^1.0.2", "isarray": "^2.0.5", "which-boxed-primitive": "^1.0.2", "which-collection": "^1.0.2", "which-typed-array": "^1.1.15"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/which-collection": {"version": "1.0.2", "license": "MIT", "dependencies": {"is-map": "^2.0.3", "is-set": "^2.0.3", "is-weakmap": "^2.0.2", "is-weakset": "^2.0.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/which-typed-array": {"version": "1.1.15", "license": "MIT", "dependencies": {"available-typed-arrays": "^1.0.7", "call-bind": "^1.0.7", "for-each": "^0.3.3", "gopd": "^1.0.1", "has-tostringtag": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/wmf": {"version": "1.0.2", "license": "Apache-2.0", "engines": {"node": ">=0.8"}}, "node_modules/word": {"version": "0.3.0", "license": "Apache-2.0", "engines": {"node": ">=0.8"}}, "node_modules/word-wrap": {"version": "1.2.5", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/wrap-ansi": {"version": "7.0.0", "license": "MIT", "dependencies": {"ansi-styles": "^4.0.0", "string-width": "^4.1.0", "strip-ansi": "^6.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/wrap-ansi?sponsor=1"}}, "node_modules/wrap-ansi-cjs": {"name": "wrap-ansi", "version": "7.0.0", "license": "MIT", "dependencies": {"ansi-styles": "^4.0.0", "string-width": "^4.1.0", "strip-ansi": "^6.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/wrap-ansi?sponsor=1"}}, "node_modules/wrap-ansi-cjs/node_modules/ansi-styles": {"version": "4.3.0", "license": "MIT", "dependencies": {"color-convert": "^2.0.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/wrap-ansi-cjs/node_modules/color-convert": {"version": "2.0.1", "license": "MIT", "dependencies": {"color-name": "~1.1.4"}, "engines": {"node": ">=7.0.0"}}, "node_modules/wrap-ansi-cjs/node_modules/color-name": {"version": "1.1.4", "license": "MIT"}, "node_modules/wrap-ansi/node_modules/ansi-styles": {"version": "4.3.0", "license": "MIT", "dependencies": {"color-convert": "^2.0.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/wrap-ansi/node_modules/color-convert": {"version": "2.0.1", "license": "MIT", "dependencies": {"color-name": "~1.1.4"}, "engines": {"node": ">=7.0.0"}}, "node_modules/wrap-ansi/node_modules/color-name": {"version": "1.1.4", "license": "MIT"}, "node_modules/wrappy": {"version": "1.0.2", "license": "ISC"}, "node_modules/xlsx": {"version": "0.18.5", "license": "Apache-2.0", "dependencies": {"adler-32": "~1.3.0", "cfb": "~1.2.1", "codepage": "~1.15.0", "crc-32": "~1.2.1", "ssf": "~0.11.2", "wmf": "~1.0.1", "word": "~0.3.0"}, "bin": {"xlsx": "bin/xlsx.njs"}, "engines": {"node": ">=0.8"}}, "node_modules/yallist": {"version": "4.0.0", "license": "ISC"}, "node_modules/yaml": {"version": "1.10.2", "license": "ISC", "engines": {"node": ">= 6"}}, "node_modules/yauzl": {"version": "2.10.0", "license": "MIT", "dependencies": {"buffer-crc32": "~0.2.3", "fd-slicer": "~1.1.0"}}, "node_modules/yocto-queue": {"version": "0.1.0", "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/yoga-layout": {"version": "2.0.1", "license": "MIT"}, "node_modules/yup": {"version": "0.32.11", "resolved": "https://registry.npmjs.org/yup/-/yup-0.32.11.tgz", "integrity": "sha512-Z2Fe1bn+eLstG8DRR6FTavGD+MeAwyfmouhHsIUgaADz8jvFKbO/fXc2trJKZg+5EBjh4gGm3iU/t3onKlXHIg==", "dependencies": {"@babel/runtime": "^7.15.4", "@types/lodash": "^4.14.175", "lodash": "^4.17.21", "lodash-es": "^4.17.21", "nanoclone": "^0.2.1", "property-expr": "^2.0.4", "toposort": "^2.0.2"}, "engines": {"node": ">=10"}}, "node_modules/zod": {"version": "3.23.8", "resolved": "https://registry.npmjs.org/zod/-/zod-3.23.8.tgz", "integrity": "sha512-XBx9AXhXktjUqnepgTiE5flcKIYWi/rme0Eaj+5Y0lftuGBq+jyRu/md4WnuxqgP1ubdpNCsYEYPxrzVHD8d6g==", "license": "MIT", "funding": {"url": "https://github.com/sponsors/colinhacks"}}, "node_modules/zustand": {"version": "4.4.6", "license": "MIT", "dependencies": {"use-sync-external-store": "1.2.0"}, "engines": {"node": ">=12.7.0"}, "peerDependencies": {"@types/react": ">=16.8", "immer": ">=9.0", "react": ">=16.8"}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "immer": {"optional": true}, "react": {"optional": true}}}, "node_modules/zustand/node_modules/use-sync-external-store": {"version": "1.2.0", "license": "MIT", "peerDependencies": {"react": "^16.8.0 || ^17.0.0 || ^18.0.0"}}}}