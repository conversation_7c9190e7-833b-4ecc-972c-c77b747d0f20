'use client';

import { useState, useCallback } from 'react';
import {
  Box,
  VStack,
  Text,
  Image,
  Button,
  IconButton,
  useToast,
  Progress,
  Alert,
  AlertIcon,
  HStack,
  Badge,
} from '@chakra-ui/react';
import { FiUpload, FiFile, FiX, FiCheck } from 'react-icons/fi';
import { useDropzone } from 'react-dropzone';

interface FileUploadZoneProps {
  label: string;
  description?: string;
  acceptedFileTypes?: string[];
  maxFileSize?: number; // in MB
  onFileUpload: (file: File) => Promise<string>; // Returns URL
  uploadedUrl?: string;
  required?: boolean;
  disabled?: boolean;
  placeholder?: string;
}

export function FileUploadZone({
  label,
  description,
  acceptedFileTypes = ['image/*', '.pdf'],
  maxFileSize = 10,
  onFileUpload,
  uploadedUrl,
  required = false,
  disabled = false,
  placeholder
}: FileUploadZoneProps) {
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [error, setError] = useState<string | null>(null);
  const toast = useToast();

  const onDrop = useCallback(async (acceptedFiles: File[]) => {
    if (acceptedFiles.length === 0) return;
    
    const file = acceptedFiles[0];
    setError(null);
    
    // Validate file size
    if (file.size > maxFileSize * 1024 * 1024) {
      setError(`El archivo es muy grande. Máximo ${maxFileSize}MB`);
      return;
    }

    setIsUploading(true);
    setUploadProgress(0);

    try {
      // Simulate upload progress
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return 90;
          }
          return prev + 10;
        });
      }, 100);

      const url = await onFileUpload(file);
      
      clearInterval(progressInterval);
      setUploadProgress(100);
      
      toast({
        title: 'Archivo subido exitosamente',
        status: 'success',
        duration: 3000,
        isClosable: true,
      });
    } catch (error: any) {
      setError(error.message || 'Error al subir el archivo');
      toast({
        title: 'Error al subir archivo',
        description: error.message,
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setIsUploading(false);
      setUploadProgress(0);
    }
  }, [onFileUpload, maxFileSize, toast]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: acceptedFileTypes.reduce((acc, type) => {
      acc[type] = [];
      return acc;
    }, {} as Record<string, string[]>),
    multiple: false,
    disabled: disabled || isUploading
  });

  const handleRemove = () => {
    // TODO: Implement file removal
    toast({
      title: 'Archivo eliminado',
      status: 'info',
      duration: 2000,
      isClosable: true,
    });
  };

  const isImage = (url: string) => {
    return /\.(jpg|jpeg|png|gif|webp)$/i.test(url);
  };

  const getFileTypeIcon = (url: string) => {
    if (isImage(url)) return null; // Will show image preview
    return <FiFile size="48" />;
  };

  return (
    <VStack spacing={4} align="stretch">
      <Box>
        <HStack>
          <Text fontWeight="semibold" fontSize="sm">
            {label}
          </Text>
          {required && <Badge colorScheme="red" size="sm">Requerido</Badge>}
        </HStack>
        {description && (
          <Text fontSize="xs" color="gray.500" mt={1}>
            {description}
          </Text>
        )}
      </Box>

      {uploadedUrl ? (
        // File uploaded - show preview
        <Box
          border="2px solid"
          borderColor="green.200"
          borderRadius="lg"
          p={4}
          bg="green.50"
          position="relative"
        >
          <HStack justify="space-between" align="start">
            <HStack spacing={3} flex={1}>
              <Box
                w="60px"
                h="60px"
                borderRadius="md"
                overflow="hidden"
                bg="white"
                display="flex"
                alignItems="center"
                justifyContent="center"
                border="1px solid"
                borderColor="gray.200"
              >
                {isImage(uploadedUrl) ? (
                  <Image
                    src={uploadedUrl}
                    alt={label}
                    objectFit="cover"
                    w="full"
                    h="full"
                  />
                ) : (
                  getFileTypeIcon(uploadedUrl)
                )}
              </Box>
              <VStack align="start" spacing={1} flex={1}>
                <HStack>
                  <FiCheck color="green" />
                  <Text fontSize="sm" fontWeight="medium" color="green.700">
                    Archivo subido exitosamente
                  </Text>
                </HStack>
                <Text fontSize="xs" color="gray.600">
                  Haz clic para ver o cambiar archivo
                </Text>
              </VStack>
            </HStack>
            <IconButton
              aria-label="Eliminar archivo"
              icon={<FiX />}
              size="sm"
              variant="ghost"
              colorScheme="red"
              onClick={handleRemove}
            />
          </HStack>
        </Box>
      ) : (
        // Upload zone
        <Box
          {...getRootProps()}
          border="2px dashed"
          borderColor={isDragActive ? "orange.300" : error ? "red.300" : "gray.300"}
          borderRadius="lg"
          p={8}
          textAlign="center"
          cursor={disabled ? "not-allowed" : "pointer"}
          bg={isDragActive ? "orange.50" : error ? "red.50" : "gray.50"}
          transition="all 0.2s"
          _hover={!disabled ? {
            borderColor: "orange.400",
            bg: "orange.50"
          } : {}}
        >
          <input {...getInputProps()} />
          
          <VStack spacing={4}>
            <Box color={isDragActive ? "orange.500" : error ? "red.500" : "gray.400"}>
              <FiUpload size="32" />
            </Box>
            
            {isUploading ? (
              <VStack spacing={2} w="full">
                <Text fontSize="sm" fontWeight="medium">
                  Subiendo archivo...
                </Text>
                <Progress
                  value={uploadProgress}
                  colorScheme="orange"
                  size="sm"
                  w="200px"
                  hasStripe
                  isAnimated
                />
                <Text fontSize="xs" color="gray.500">
                  {uploadProgress}%
                </Text>
              </VStack>
            ) : isDragActive ? (
              <Text fontSize="sm" fontWeight="medium" color="orange.600">
                Suelta el archivo aquí
              </Text>
            ) : (
              <VStack spacing={2}>
                <Text fontSize="sm" fontWeight="medium">
                  {placeholder || 'Arrastra y suelta un archivo aquí'}
                </Text>
                <Text fontSize="xs" color="gray.500">
                  o haz clic para seleccionar
                </Text>
                <Text fontSize="xs" color="gray.400">
                  Formatos: {acceptedFileTypes.join(', ')} • Máximo {maxFileSize}MB
                </Text>
              </VStack>
            )}
          </VStack>
        </Box>
      )}

      {error && (
        <Alert status="error" size="sm">
          <AlertIcon />
          <Text fontSize="sm">{error}</Text>
        </Alert>
      )}
    </VStack>
  );
}