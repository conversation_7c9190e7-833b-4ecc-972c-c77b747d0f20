'use client';

import { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import {
  Box,
  Button,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Badge,
  Input,
  Select,
  HStack,
  VStack,
  Text,
  useDisclosure,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  ModalCloseButton,
  SimpleGrid,
  Card,
  CardHeader,
  CardBody,
  Heading,
  Alert,
  AlertIcon,
  Image,
  Spinner,
  IconButton,
  Tooltip,
  Flex,
  Spacer,
  useToast,
} from '@chakra-ui/react';
import { FiEye, FiDownload, FiFilter, FiRefreshCw } from 'react-icons/fi';
import { getVerificationHistory, getAllVerifications } from '@/actions/verification';
import { getVerificationStatus, formatVerificationStatus } from '@/utils/verification';
import { VerificationRecord } from '@/types';

interface AdminVerificationDashboardProps {
  vehicleId?: string;
}

export function AdminVerificationDashboard({ vehicleId }: AdminVerificationDashboardProps) {
  const [searchPlate, setSearchPlate] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [selectedVerification, setSelectedVerification] = useState<VerificationRecord | null>(null);
  const [selectedDocument, setSelectedDocument] = useState<{ url: string; filename: string } | null>(null);
  const { isOpen, onOpen, onClose } = useDisclosure();
  const {
    isOpen: isDocumentOpen,
    onOpen: onDocumentOpen,
    onClose: onDocumentClose
  } = useDisclosure();
  const toast = useToast();

  const { data: verificationHistory, isLoading, error, refetch } = useQuery({
    queryKey: ['verification-history', vehicleId, searchPlate],
    queryFn: () => getVerificationHistory(vehicleId, searchPlate),
    enabled: !!(vehicleId || searchPlate),
  });

  const handleSearch = () => {
    if (searchPlate.trim()) {
      refetch();
    }
  };

  const handleViewDetails = (verification: VerificationRecord) => {
    setSelectedVerification(verification);
    onOpen();
  };

  const handleViewDocument = (url: string, filename: string) => {
    setSelectedDocument({ url, filename });
    onDocumentOpen();
  };

  const handleDownloadDocument = (url: string, filename: string) => {
    // Create download link
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    link.click();

    toast({
      title: 'Descarga iniciada',
      description: `Descargando ${filename}`,
      status: 'info',
      duration: 3000,
      isClosable: true,
    });
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      completed: { label: 'Completada', color: 'green' },
      pending: { label: 'Pendiente', color: 'yellow' },
      expired: { label: 'Vencida', color: 'red' },
      cancelled: { label: 'Cancelada', color: 'gray' },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending;
    return (
      <Badge colorScheme={config.color}>
        {config.label}
      </Badge>
    );
  };

  const getHologramBadge = (hologramType: string) => {
    const hologramConfig = {
      '00': { label: 'Exento', color: 'blue' },
      '0': { label: 'Cumple', color: 'green' },
      '1': { label: 'Primera', color: 'yellow' },
      '2': { label: 'Segunda', color: 'red' },
    };

    const config = hologramConfig[hologramType as keyof typeof hologramConfig];
    return config ? (
      <Badge colorScheme={config.color}>
        {hologramType} - {config.label}
      </Badge>
    ) : (
      <Badge colorScheme="gray">-</Badge>
    );
  };

  const getCurrentVerificationStatus = () => {
    if (!verificationHistory || verificationHistory.records.length === 0) return null;

    const latest = verificationHistory.records[0];
    const nextDate = verificationHistory.nextVerificationDue ? new Date(verificationHistory.nextVerificationDue) : null;
    const status = getVerificationStatus(
      new Date(latest.verificationDate),
      nextDate
    );

    return formatVerificationStatus(status);
  };

  const filteredVerifications = verificationHistory?.records.filter(verification => {
    if (statusFilter && verification.status !== statusFilter) return false;
    return true;
  }) || [];

  return (
    <Box>
      <VStack spacing={6} align="stretch">
        {/* Header */}
        <Flex>
          <Heading size="lg">Dashboard de Verificaciones</Heading>
          <Spacer />
          <Button
            leftIcon={<FiRefreshCw />}
            onClick={() => refetch()}
            variant="outline"
            size="sm"
          >
            Actualizar
          </Button>
        </Flex>

        {/* Search and Filters */}
        <Card>
          <CardHeader>
            <Heading size="md">Buscar Verificaciones</Heading>
          </CardHeader>
          <CardBody>
            <HStack spacing={4}>
              <Input
                placeholder="Número de placa"
                value={searchPlate}
                onChange={(e) => setSearchPlate(e.target.value.toUpperCase())}
                maxW="200px"
              />
              <Button colorScheme="blue" onClick={handleSearch}>
                Buscar
              </Button>
              <Select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                maxW="200px"
                placeholder="Filtrar por estado"
              >
                <option value="completed">Completadas</option>
                <option value="pending">Pendientes</option>
                <option value="expired">Vencidas</option>
                <option value="cancelled">Canceladas</option>
              </Select>
            </HStack>
          </CardBody>
        </Card>

        {/* Current Status */}
        {verificationHistory && (
          <Card>
            <CardHeader>
              <Heading size="md">Estado Actual - {verificationHistory.vehiclePlate}</Heading>
            </CardHeader>
            <CardBody>
              <SimpleGrid columns={3} spacing={4}>
                <Box>
                  <Text fontSize="sm" color="gray.600">Estado Actual</Text>
                  <Badge colorScheme={getCurrentVerificationStatus()?.color || 'gray'} size="lg">
                    {getCurrentVerificationStatus()?.label || 'Sin datos'}
                  </Badge>
                </Box>
                <Box>
                  <Text fontSize="sm" color="gray.600">Próxima Verificación</Text>
                  <Text fontWeight="bold">
                    {verificationHistory.nextVerificationDue
                      ? new Date(verificationHistory.nextVerificationDue).toLocaleDateString('es-MX')
                      : 'No definida'
                    }
                  </Text>
                </Box>
                <Box>
                  <Text fontSize="sm" color="gray.600">Total de Verificaciones</Text>
                  <Text fontWeight="bold">{verificationHistory.records.length}</Text>
                </Box>
              </SimpleGrid>
            </CardBody>
          </Card>
        )}

        {/* Verification History Table */}
        <Card>
          <CardHeader>
            <Heading size="md">Historial de Verificaciones</Heading>
          </CardHeader>
          <CardBody>
            {isLoading ? (
              <Box display="flex" justifyContent="center" p={8}>
                <Spinner size="lg" />
              </Box>
            ) : error ? (
              <Alert status="error">
                <AlertIcon />
                Error al cargar el historial de verificaciones
              </Alert>
            ) : filteredVerifications.length === 0 ? (
              <Alert status="info">
                <AlertIcon />
                {searchPlate ? 'No se encontraron verificaciones' : 'Busca por número de placa para ver el historial'}
              </Alert>
            ) : (
              <Table variant="simple">
                <Thead>
                  <Tr>
                    <Th>Fecha</Th>
                    <Th>Placa</Th>
                    <Th>Verificentro</Th>
                    <Th>Holograma</Th>
                    <Th>Estado</Th>
                    <Th>Próxima Verificación</Th>
                    <Th>Acciones</Th>
                  </Tr>
                </Thead>
                <Tbody>
                  {filteredVerifications.map((verification) => (
                    <Tr key={verification._id}>
                      <Td>{new Date(verification.verificationDate).toLocaleDateString('es-MX')}</Td>
                      <Td fontWeight="bold">{verification.vehiclePlate}</Td>
                      <Td>{verification.verificationCenterId}</Td>
                      <Td>{getHologramBadge(verification.hologramType || '')}</Td>
                      <Td>{getStatusBadge(verification.status || '')}</Td>
                      <Td>
                        {verificationHistory?.nextVerificationDue
                          ? new Date(verificationHistory.nextVerificationDue).toLocaleDateString('es-MX')
                          : '-'
                        }
                      </Td>
                      <Td>
                        <Tooltip label="Ver detalles">
                          <IconButton
                            aria-label="Ver detalles"
                            icon={<FiEye />}
                            size="sm"
                            onClick={() => handleViewDetails(verification)}
                          />
                        </Tooltip>
                      </Td>
                    </Tr>
                  ))}
                </Tbody>
              </Table>
            )}
          </CardBody>
        </Card>
      </VStack>

      {/* Verification Details Modal */}
      <Modal isOpen={isOpen} onClose={onClose} size="2xl">
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>Detalles de la Verificación</ModalHeader>
          <ModalCloseButton />
          <ModalBody>
            {selectedVerification && (
              <VStack spacing={6} align="stretch">
                <SimpleGrid columns={2} spacing={4}>
                  <Box>
                    <Text fontSize="sm" color="gray.600">Placa</Text>
                    <Text fontWeight="bold">{selectedVerification.vehiclePlate}</Text>
                  </Box>
                  <Box>
                    <Text fontSize="sm" color="gray.600">Fecha de Verificación</Text>
                    <Text fontWeight="bold">
                      {new Date(selectedVerification.verificationDate).toLocaleDateString('es-MX')}
                    </Text>
                  </Box>
                  <Box>
                    <Text fontSize="sm" color="gray.600">Verificentro</Text>
                    <Text fontWeight="bold">{selectedVerification.verificationCenterId}</Text>
                  </Box>
                  <Box>
                    <Text fontSize="sm" color="gray.600">Holograma</Text>
                    {getHologramBadge(selectedVerification.hologramType || '')}
                  </Box>
                  <Box>
                    <Text fontSize="sm" color="gray.600">Estado</Text>
                    {getStatusBadge(selectedVerification.status || '')}
                  </Box>
                  <Box>
                    <Text fontSize="sm" color="gray.600">Próxima Verificación</Text>
                    <Text fontWeight="bold">
                      {verificationHistory?.nextVerificationDue
                        ? new Date(verificationHistory.nextVerificationDue).toLocaleDateString('es-MX')
                        : '-'
                      }
                    </Text>
                  </Box>
                </SimpleGrid>

                <Box>
                  <Text fontSize="md" fontWeight="bold" mb={4}>Documentos</Text>
                  <SimpleGrid columns={2} spacing={4}>
                    {selectedVerification.vehiclePhoto && (
                      <Card>
                        <CardBody>
                          <VStack>
                            <Text fontSize="sm" fontWeight="bold">Foto del Vehículo</Text>
                            <HStack>
                              <Button
                                size="sm"
                                leftIcon={<FiEye />}
                                onClick={() => handleViewDocument(
                                  selectedVerification.vehiclePhoto!,
                                  'Foto del Vehículo'
                                )}
                              >
                                Ver
                              </Button>
                              <Button
                                size="sm"
                                leftIcon={<FiDownload />}
                                onClick={() => handleDownloadDocument(
                                  selectedVerification.vehiclePhoto!,
                                  'Foto del Vehículo'
                                )}
                              >
                                Descargar
                              </Button>
                            </HStack>
                          </VStack>
                        </CardBody>
                      </Card>
                    )}

                    {selectedVerification.circulationCard && (
                      <Card>
                        <CardBody>
                          <VStack>
                            <Text fontSize="sm" fontWeight="bold">Tarjeta de Circulación</Text>
                            <HStack>
                              <Button
                                size="sm"
                                leftIcon={<FiEye />}
                                onClick={() => handleViewDocument(
                                  selectedVerification.circulationCard!,
                                  'Tarjeta de Circulación'
                                )}
                              >
                                Ver
                              </Button>
                              <Button
                                size="sm"
                                leftIcon={<FiDownload />}
                                onClick={() => handleDownloadDocument(
                                  selectedVerification.circulationCard!,
                                  'Tarjeta de Circulación'
                                )}
                              >
                                Descargar
                              </Button>
                            </HStack>
                          </VStack>
                        </CardBody>
                      </Card>
                    )}

                    {selectedVerification.verificationCertificate && (
                      <Card>
                        <CardBody>
                          <VStack>
                            <Text fontSize="sm" fontWeight="bold">Certificado de Verificación</Text>
                            <HStack>
                              <Button
                                size="sm"
                                leftIcon={<FiEye />}
                                onClick={() => handleViewDocument(
                                  selectedVerification.verificationCertificate!,
                                  'Certificado de Verificación'
                                )}
                              >
                                Ver
                              </Button>
                              <Button
                                size="sm"
                                leftIcon={<FiDownload />}
                                onClick={() => handleDownloadDocument(
                                  selectedVerification.verificationCertificate!,
                                  'Certificado de Verificación'
                                )}
                              >
                                Descargar
                              </Button>
                            </HStack>
                          </VStack>
                        </CardBody>
                      </Card>
                    )}

                    {selectedVerification.hologramPhoto && (
                      <Card>
                        <CardBody>
                          <VStack>
                            <Text fontSize="sm" fontWeight="bold">Foto del Holograma</Text>
                            <HStack>
                              <Button
                                size="sm"
                                leftIcon={<FiEye />}
                                onClick={() => handleViewDocument(
                                  selectedVerification.hologramPhoto!,
                                  'Foto del Holograma'
                                )}
                              >
                                Ver
                              </Button>
                              <Button
                                size="sm"
                                leftIcon={<FiDownload />}
                                onClick={() => handleDownloadDocument(
                                  selectedVerification.hologramPhoto!,
                                  'Foto del Holograma'
                                )}
                              >
                                Descargar
                              </Button>
                            </HStack>
                          </VStack>
                        </CardBody>
                      </Card>
                    )}
                  </SimpleGrid>
                </Box>

                {selectedVerification.customerNotes && (
                  <Box>
                    <Text fontSize="md" fontWeight="bold" mb={2}>Notas del Cliente</Text>
                    <Text>{selectedVerification.customerNotes}</Text>
                  </Box>
                )}
              </VStack>
            )}
          </ModalBody>
          <ModalFooter>
            <Button onClick={onClose}>Cerrar</Button>
          </ModalFooter>
        </ModalContent>
      </Modal>

      {/* Document Viewer Modal */}
      <Modal isOpen={isDocumentOpen} onClose={onDocumentClose} size="4xl">
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>{selectedDocument?.filename}</ModalHeader>
          <ModalCloseButton />
          <ModalBody>
            {selectedDocument && (
              <Box display="flex" justifyContent="center">
                <Image
                  src={selectedDocument.url}
                  alt={selectedDocument.filename}
                  maxH="70vh"
                  objectFit="contain"
                />
              </Box>
            )}
          </ModalBody>
          <ModalFooter>
            <Button onClick={onDocumentClose}>Cerrar</Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </Box>
  );
}