'use client';

import { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import {
  Box,
  Button,
  VStack,
  HStack,
  FormControl,
  FormLabel,
  Input,
  Select,
  Alert,
  AlertIcon,
  Text,
  useToast,
  Spinner,
  Card,
  CardHeader,
  CardBody,
  Heading,
  Badge,
  Divider,
  Image,
  useDisclosure,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  ModalCloseButton,
} from '@chakra-ui/react';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import { 
  getVerificationCenters, 
  searchVehicleByPlate, 
  createVerificationRecord 
} from '@/actions/verification';
import { validatePlateNumber } from '@/utils/verification';
import { FileUploader } from '@/components/verification/FileUploader';
import { fileToBase64 } from '@/utils/fileToBase64';

const validationSchema = Yup.object({
  plateNumber: Yup.string()
    .required('Número de placa es requerido')
    .test('plate-format', 'Formato de placa inválido', (value) => {
      return value ? validatePlateNumber(value) : false;
    }),
  verificationCenterId: Yup.string().required('Verificentro es requerido'),
  vehiclePhoto: Yup.mixed().required('Foto del vehículo es requerida'),
  circulationCard: Yup.mixed().required('Tarjeta de circulación es requerida'),
});

interface VehicleInfo {
  _id: string;
  brand: string;
  model: string;
  color: string;
  vin: string;
  carPlates: { plates: string };
  contractNumber: string;
}

export function VendorVerificationFlow() {
  const [step, setStep] = useState<'search' | 'register' | 'success'>('search');
  const [vehicleInfo, setVehicleInfo] = useState<VehicleInfo | null>(null);
  const [plateSearch, setPlateSearch] = useState('');
  const [isSearching, setIsSearching] = useState(false);
  const [searchError, setSearchError] = useState<string | null>(null);
  const [createdVerification, setCreatedVerification] = useState<any>(null);
  const { isOpen, onOpen, onClose } = useDisclosure();
  const toast = useToast();
  const queryClient = useQueryClient();

  const { data: centers, isLoading: centersLoading } = useQuery({
    queryKey: ['verification-centers'],
    queryFn: () => getVerificationCenters(),
  });

  const createMutation = useMutation({
    mutationFn: createVerificationRecord,
    onSuccess: (data) => {
      setCreatedVerification(data);
      setStep('success');
      toast({
        title: 'Verificación registrada',
        description: 'La verificación se ha registrado exitosamente',
        status: 'success',
        duration: 5000,
        isClosable: true,
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.message || 'Error al registrar la verificación',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    },
  });

  const formik = useFormik({
    initialValues: {
      plateNumber: '',
      verificationCenterId: '',
      vehiclePhoto: null as File | null,
      circulationCard: null as File | null,
    },
    validationSchema,
    onSubmit: async (values) => {
      if (!vehicleInfo) return;

      try {
        const vehiclePhotoBase64 = values.vehiclePhoto ? await fileToBase64(values.vehiclePhoto) : undefined;
        const circulationCardBase64 = values.circulationCard ? await fileToBase64(values.circulationCard) : undefined;

        createMutation.mutate({
          vehiclePlate: values.plateNumber,
          verificationCenterId: values.verificationCenterId,
          verificationDate: new Date().toISOString(),
          vehiclePhoto: vehiclePhotoBase64,
          circulationCard: circulationCardBase64,
        });
      } catch (error) {
        console.error('Error converting files to base64:', error);
        // TODO: Show error message to user
      }
    },
  });

  const handlePlateSearch = async () => {
    if (!plateSearch.trim()) {
      setSearchError('Ingresa un número de placa');
      return;
    }

    if (!validatePlateNumber(plateSearch)) {
      setSearchError('Formato de placa inválido');
      return;
    }

    setIsSearching(true);
    setSearchError(null);

    try {
      const vehicle = await searchVehicleByPlate(plateSearch);
      
      if (vehicle) {
        setVehicleInfo(vehicle);
        formik.setFieldValue('plateNumber', plateSearch);
        setStep('register');
      } else {
        setSearchError('Vehículo no encontrado en el sistema');
      }
    } catch (error: any) {
      setSearchError(error.message || 'Error al buscar el vehículo');
    } finally {
      setIsSearching(false);
    }
  };

  const handleStartOver = () => {
    setStep('search');
    setVehicleInfo(null);
    setPlateSearch('');
    setSearchError(null);
    setCreatedVerification(null);
    formik.resetForm();
  };

  const handleViewDetails = () => {
    onOpen();
  };

  const copyLinkToClipboard = () => {
    if (createdVerification?.uniqueLink) {
      navigator.clipboard.writeText(createdVerification.uniqueLink);
      toast({
        title: 'Link copiado',
        description: 'El link se ha copiado al portapapeles',
        status: 'success',
        duration: 3000,
        isClosable: true,
      });
    }
  };

  if (step === 'search') {
    return (
      <Card maxW="md" mx="auto">
        <CardHeader>
          <Heading size="md">Buscar Vehículo</Heading>
        </CardHeader>
        <CardBody>
          <VStack spacing={4}>
            <FormControl>
              <FormLabel>Número de Placa</FormLabel>
              <Input
                value={plateSearch}
                onChange={(e) => setPlateSearch(e.target.value.toUpperCase())}
                placeholder="ABC123"
                maxLength={8}
              />
            </FormControl>

            {searchError && (
              <Alert status="error">
                <AlertIcon />
                {searchError}
              </Alert>
            )}

            <Button
              colorScheme="blue"
              onClick={handlePlateSearch}
              isLoading={isSearching}
              loadingText="Buscando..."
              width="full"
            >
              Buscar Vehículo
            </Button>
          </VStack>
        </CardBody>
      </Card>
    );
  }

  if (step === 'register') {
    return (
      <Card maxW="2xl" mx="auto">
        <CardHeader>
          <HStack justifyContent="space-between">
            <Heading size="md">Registrar Verificación</Heading>
            <Button variant="ghost" onClick={handleStartOver}>
              Buscar Otro
            </Button>
          </HStack>
        </CardHeader>
        <CardBody>
          <VStack spacing={6}>
            {/* Vehicle Information */}
            <Box width="full" p={4} bg="gray.50" borderRadius="md">
              <Text fontWeight="bold" mb={2}>Información del Vehículo</Text>
              <VStack align="start" spacing={1}>
                <Text><strong>Placa:</strong> {vehicleInfo?.carPlates.plates}</Text>
                <Text><strong>Marca:</strong> {vehicleInfo?.brand}</Text>
                <Text><strong>Modelo:</strong> {vehicleInfo?.model}</Text>
                <Text><strong>Color:</strong> {vehicleInfo?.color}</Text>
                <Text><strong>VIN:</strong> {vehicleInfo?.vin}</Text>
                <Text><strong>Contrato:</strong> {vehicleInfo?.contractNumber}</Text>
              </VStack>
            </Box>

            <Divider />

            <form onSubmit={formik.handleSubmit} style={{ width: '100%' }}>
              <VStack spacing={4}>
                <FormControl isRequired isInvalid={!!formik.errors.verificationCenterId && formik.touched.verificationCenterId}>
                  <FormLabel>Verificentro</FormLabel>
                  <Select
                    name="verificationCenterId"
                    value={formik.values.verificationCenterId}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    placeholder="Seleccionar verificentro"
                  >
                    {centers?.map((center) => (
                      <option key={center._id} value={center._id}>
                        {center.code} - {center.name}
                      </option>
                    ))}
                  </Select>
                  {formik.errors.verificationCenterId && formik.touched.verificationCenterId && (
                    <Text color="red.500" fontSize="sm">{formik.errors.verificationCenterId}</Text>
                  )}
                </FormControl>

                <FormControl isRequired isInvalid={!!formik.errors.vehiclePhoto && formik.touched.vehiclePhoto}>
                  <FormLabel>Foto del Vehículo Durante la Verificación</FormLabel>
                  <FileUploader
                    accept="image/*"
                    onFileSelect={(file: File) => formik.setFieldValue('vehiclePhoto', file)}
                    selectedFile={formik.values.vehiclePhoto}
                  />
                  {formik.errors.vehiclePhoto && formik.touched.vehiclePhoto && (
                    <Text color="red.500" fontSize="sm">{formik.errors.vehiclePhoto}</Text>
                  )}
                </FormControl>

                <FormControl isRequired isInvalid={!!formik.errors.circulationCard && formik.touched.circulationCard}>
                  <FormLabel>Tarjeta de Circulación (TDC)</FormLabel>
                  <FileUploader
                    accept="image/*,application/pdf"
                    onFileSelect={(file: File) => formik.setFieldValue('circulationCard', file)}
                    selectedFile={formik.values.circulationCard}
                  />
                  {formik.errors.circulationCard && formik.touched.circulationCard && (
                    <Text color="red.500" fontSize="sm">{formik.errors.circulationCard}</Text>
                  )}
                </FormControl>

                <HStack width="full" justifyContent="space-between">
                  <Button variant="outline" onClick={handleStartOver}>
                    Cancelar
                  </Button>
                  <Button
                    colorScheme="blue"
                    type="submit"
                    isLoading={createMutation.isPending}
                    loadingText="Registrando..."
                  >
                    Registrar Verificación
                  </Button>
                </HStack>
              </VStack>
            </form>
          </VStack>
        </CardBody>
      </Card>
    );
  }

  if (step === 'success') {
    return (
      <Card maxW="2xl" mx="auto">
        <CardHeader>
          <Heading size="md" color="green.600">
            ✅ Verificación Registrada Exitosamente
          </Heading>
        </CardHeader>
        <CardBody>
          <VStack spacing={6}>
            <Alert status="success">
              <AlertIcon />
              La verificación se ha registrado correctamente. Se ha enviado un enlace al cliente para que complete la información.
            </Alert>

            <Box width="full" p={4} bg="blue.50" borderRadius="md">
              <Text fontWeight="bold" mb={2}>Enlace para el Cliente:</Text>
              <HStack>
                <Input
                  value={createdVerification?.uniqueLink || ''}
                  readOnly
                  bg="white"
                  fontSize="sm"
                />
                <Button size="sm" onClick={copyLinkToClipboard}>
                  Copiar
                </Button>
              </HStack>
              <Text fontSize="sm" color="gray.600" mt={2}>
                El cliente deberá usar este enlace para subir el certificado de verificación y foto del holograma
              </Text>
            </Box>

            <Divider />

            <HStack width="full" justifyContent="space-between">
              <Button variant="outline" onClick={handleViewDetails}>
                Ver Detalles
              </Button>
              <Button colorScheme="blue" onClick={handleStartOver}>
                Registrar Otra Verificación
              </Button>
            </HStack>
          </VStack>
        </CardBody>
      </Card>
    );
  }

  return (
    <>
      <Modal isOpen={isOpen} onClose={onClose} size="lg">
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>Detalles de la Verificación</ModalHeader>
          <ModalCloseButton />
          <ModalBody>
            {createdVerification && (
              <VStack align="start" spacing={3}>
                <Text><strong>ID:</strong> {createdVerification._id}</Text>
                <Text><strong>Placa:</strong> {createdVerification.plateNumber}</Text>
                <Text><strong>Fecha:</strong> {new Date(createdVerification.verificationDate).toLocaleDateString('es-MX')}</Text>
                <Text><strong>Estado:</strong> 
                  <Badge ml={2} colorScheme={createdVerification.status === 'pending' ? 'yellow' : 'green'}>
                    {createdVerification.status === 'pending' ? 'Pendiente' : 'Completada'}
                  </Badge>
                </Text>
                <Text><strong>Enlace único:</strong></Text>
                <Box p={2} bg="gray.100" borderRadius="md" width="full">
                  <Text fontSize="sm" wordBreak="break-all">
                    {createdVerification.uniqueLink}
                  </Text>
                </Box>
              </VStack>
            )}
          </ModalBody>
          <ModalFooter>
            <Button onClick={onClose}>Cerrar</Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </>
  );
}