'use client';

import React, { useState, useEffect } from 'react';
import getCurrentUser from '@/actions/getCurrentUser';

interface UserDiagnosticsProps {
  onClose: () => void;
}

const UserDiagnostics: React.FC<UserDiagnosticsProps> = ({ onClose }) => {
  const [diagnostics, setDiagnostics] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    runDiagnostics();
  }, []);

  const runDiagnostics = async () => {
    try {
      setLoading(true);
      
      // Intentar obtener usuario actual
      const user = await getCurrentUser();
      
      const result = {
        user: user ? {
          id: user._id,
          email: user.email,
          name: user.name,
          userType: user.userType,
          hasAccessToken: !!user.accessToken,
          accessTokenLength: user.accessToken?.length || 0,
          accessTokenPreview: user.accessToken ? `${user.accessToken.substring(0, 20)}...` : null,
        } : null,
        permissions: {
          canCreateVerificationCenters: user ? ['superAdmin', 'company-gestor'].includes(user.userType) : false,
          allowedUserTypes: ['superAdmin', 'company-gestor'],
          currentUserType: user?.userType || 'none',
        },
        apiEndpoint: {
          url: `${process.env.NEXT_PUBLIC_API_URL}/vendor-platform/emissions-verification/admin/verification-centers`,
          method: 'POST',
          headers: {
            'Authorization': user?.accessToken ? 'Bearer [TOKEN]' : 'MISSING',
            'Content-Type': 'application/json',
            'adpt': 'true',
          }
        },
        timestamp: new Date().toISOString(),
      };

      setDiagnostics(result);
    } catch (error) {
      setDiagnostics({
        error: error instanceof Error ? error.message : 'Unknown error occurred',
        timestamp: new Date().toISOString(),
      });
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white p-6 rounded-lg max-w-md w-full">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-semibold">Diagnóstico de Usuario</h2>
            <button onClick={onClose} className="text-gray-500 hover:text-gray-700">
              ✕
            </button>
          </div>
          <div className="text-center py-4">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p>Ejecutando diagnóstico...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white p-6 rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold">Diagnóstico de Usuario</h2>
          <button onClick={onClose} className="text-gray-500 hover:text-gray-700">
            ✕
          </button>
        </div>

        {diagnostics?.error ? (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            <strong>Error:</strong> {diagnostics.error}
          </div>
        ) : (
          <div className="space-y-4">
            {/* Información del Usuario */}
            <div className="bg-gray-50 p-4 rounded-lg">
              <h3 className="font-semibold mb-2 text-gray-800">👤 Usuario Actual</h3>
              {diagnostics?.user ? (
                <div className="space-y-2 text-sm">
                  <p><strong>ID:</strong> {diagnostics.user.id}</p>
                  <p><strong>Email:</strong> {diagnostics.user.email}</p>
                  <p><strong>Nombre:</strong> {diagnostics.user.name}</p>
                  <p><strong>Tipo de Usuario:</strong> 
                    <span className={`ml-2 px-2 py-1 rounded text-xs font-medium ${
                      diagnostics.permissions.canCreateVerificationCenters 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {diagnostics.user.userType}
                    </span>
                  </p>
                  <p><strong>Token de Acceso:</strong> 
                    <span className={diagnostics.user.hasAccessToken ? 'text-green-600' : 'text-red-600'}>
                      {diagnostics.user.hasAccessToken ? '✅ Presente' : '❌ Ausente'}
                    </span>
                  </p>
                  {diagnostics.user.hasAccessToken && (
                    <p className="text-xs text-gray-600">
                      <strong>Token:</strong> {diagnostics.user.accessTokenPreview} ({diagnostics.user.accessTokenLength} caracteres)
                    </p>
                  )}
                </div>
              ) : (
                <p className="text-red-600">❌ No se pudo obtener información del usuario</p>
              )}
            </div>

            {/* Permisos */}
            <div className="bg-gray-50 p-4 rounded-lg">
              <h3 className="font-semibold mb-2 text-gray-800">🔒 Permisos</h3>
              <div className="space-y-2 text-sm">
                <p><strong>¿Puede crear centros de verificación?</strong> 
                  <span className={`ml-2 px-2 py-1 rounded text-xs font-medium ${
                    diagnostics?.permissions.canCreateVerificationCenters 
                      ? 'bg-green-100 text-green-800' 
                      : 'bg-red-100 text-red-800'
                  }`}>
                    {diagnostics?.permissions.canCreateVerificationCenters ? '✅ SÍ' : '❌ NO'}
                  </span>
                </p>
                <p><strong>Tipos de usuario permitidos:</strong></p>
                <ul className="ml-4 space-y-1">
                  {diagnostics?.permissions.allowedUserTypes.map((type: string) => (
                    <li key={type} className={`text-xs ${
                      diagnostics?.permissions.currentUserType === type 
                        ? 'text-green-600 font-medium' 
                        : 'text-gray-600'
                    }`}>
                      • {type} {diagnostics?.permissions.currentUserType === type && '← Tu tipo actual'}
                    </li>
                  ))}
                </ul>
              </div>
            </div>

            {/* Configuración de API */}
            <div className="bg-gray-50 p-4 rounded-lg">
              <h3 className="font-semibold mb-2 text-gray-800">🌐 Configuración de API</h3>
              <div className="space-y-2 text-sm">
                <p><strong>URL del Endpoint:</strong></p>
                <p className="text-xs bg-gray-100 p-2 rounded font-mono break-all">
                  {diagnostics?.apiEndpoint.url}
                </p>
                <p><strong>Método:</strong> {diagnostics?.apiEndpoint.method}</p>
                <p><strong>Headers:</strong></p>
                <div className="text-xs bg-gray-100 p-2 rounded">
                  {Object.entries(diagnostics?.apiEndpoint.headers || {}).map(([key, value]) => (
                    <div key={key}>
                      <strong>{key}:</strong> {String(value)}
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Soluciones */}
            <div className="bg-blue-50 p-4 rounded-lg">
              <h3 className="font-semibold mb-2 text-blue-800">💡 Soluciones</h3>
              <div className="space-y-2 text-sm text-blue-700">
                {!diagnostics?.user ? (
                  <p>• Inicia sesión en la plataforma</p>
                ) : !diagnostics?.user.hasAccessToken ? (
                  <p>• Cierra sesión e inicia sesión nuevamente para renovar el token</p>
                ) : !diagnostics?.permissions.canCreateVerificationCenters ? (
                  <div>
                    <p>• Necesitas ser usuario tipo <strong>superAdmin</strong> o <strong>company-gestor</strong></p>
                    <p>• Contacta al administrador para cambiar tu tipo de usuario</p>
                    <p>• Tu tipo actual es: <strong>{diagnostics?.permissions.currentUserType}</strong></p>
                  </div>
                ) : (
                  <p>• ✅ Tu usuario tiene todos los permisos necesarios</p>
                )}
              </div>
            </div>
          </div>
        )}

        <div className="mt-6 flex justify-between">
          <button
            onClick={runDiagnostics}
            className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
          >
            🔄 Ejecutar Diagnóstico Nuevamente
          </button>
          <button
            onClick={onClose}
            className="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700"
          >
            Cerrar
          </button>
        </div>

        <div className="mt-4 text-xs text-gray-500">
          Diagnóstico ejecutado: {diagnostics?.timestamp}
        </div>
      </div>
    </div>
  );
};

export default UserDiagnostics;