'use client';

import { useState, useRef } from 'react';
import {
  Box,
  Button,
  Text,
  VStack,
  HStack,
  Image,
  IconButton,
  useColorModeValue,
  Alert,
  AlertIcon,
} from '@chakra-ui/react';
import { FiUpload, FiX, FiFile } from 'react-icons/fi';

interface FileUploaderProps {
  accept?: string;
  maxFileSize?: number; // in bytes
  onFileSelect: (file: File) => void;
  selectedFile?: File | null;
  error?: string;
}

export function FileUploader({
  accept = 'image/*',
  maxFileSize = 5 * 1024 * 1024, // 5MB
  onFileSelect,
  selectedFile,
  error,
}: FileUploaderProps) {
  const [dragActive, setDragActive] = useState(false);
  const [localError, setLocalError] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  const borderColor = useColorModeValue('gray.300', 'gray.600');
  const activeBorderColor = useColorModeValue('blue.500', 'blue.400');
  const bgColor = useColorModeValue('gray.50', 'gray.700');
  const activeBgColor = useColorModeValue('blue.50', 'blue.900');

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    const files = e.dataTransfer.files;
    if (files && files[0]) {
      handleFileSelection(files[0]);
    }
  };

  const handleFileSelection = (file: File) => {
    setLocalError(null);
    
    // Check file size
    if (file.size > maxFileSize) {
      setLocalError(`El archivo es demasiado grande. Tamaño máximo: ${(maxFileSize / 1024 / 1024).toFixed(1)}MB`);
      return;
    }

    // Check file type
    if (accept && !accept.includes(file.type) && !accept.includes('*')) {
      setLocalError('Tipo de archivo no permitido');
      return;
    }

    onFileSelect(file);
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files[0]) {
      handleFileSelection(files[0]);
    }
  };

  const handleRemoveFile = () => {
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
    setLocalError(null);
  };

  const isImageFile = (file: File) => {
    return file.type.startsWith('image/');
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <VStack spacing={4} align="stretch">
      <Box
        border="2px dashed"
        borderColor={dragActive ? activeBorderColor : borderColor}
        borderRadius="md"
        bg={dragActive ? activeBgColor : bgColor}
        p={6}
        cursor="pointer"
        transition="all 0.2s"
        onDragEnter={handleDrag}
        onDragLeave={handleDrag}
        onDragOver={handleDrag}
        onDrop={handleDrop}
        onClick={() => fileInputRef.current?.click()}
        _hover={{
          borderColor: activeBorderColor,
          bg: activeBgColor,
        }}
      >
        <VStack spacing={3}>
          <FiUpload size={32} />
          <Text fontSize="sm" textAlign="center">
            Arrastra y suelta un archivo aquí, o haz clic para seleccionar
          </Text>
          <Text fontSize="xs" color="gray.500" textAlign="center">
            Tamaño máximo: {(maxFileSize / 1024 / 1024).toFixed(1)}MB
          </Text>
        </VStack>
      </Box>

      <input
        ref={fileInputRef}
        type="file"
        accept={accept}
        onChange={handleFileChange}
        style={{ display: 'none' }}
      />

      {selectedFile && (
        <Box
          border="1px solid"
          borderColor={borderColor}
          borderRadius="md"
          p={4}
          bg={bgColor}
        >
          <HStack spacing={4}>
            <Box>
              {isImageFile(selectedFile) ? (
                <Image
                  src={URL.createObjectURL(selectedFile)}
                  alt="Preview"
                  boxSize="50px"
                  objectFit="cover"
                  borderRadius="md"
                />
              ) : (
                <Box
                  boxSize="50px"
                  bg="gray.200"
                  borderRadius="md"
                  display="flex"
                  alignItems="center"
                  justifyContent="center"
                >
                  <FiFile size={24} />
                </Box>
              )}
            </Box>
            
            <VStack align="start" spacing={1} flex={1}>
              <Text fontSize="sm" fontWeight="bold" noOfLines={1}>
                {selectedFile.name}
              </Text>
              <Text fontSize="xs" color="gray.500">
                {formatFileSize(selectedFile.size)}
              </Text>
            </VStack>

            <IconButton
              aria-label="Remover archivo"
              icon={<FiX />}
              size="sm"
              variant="ghost"
              onClick={handleRemoveFile}
            />
          </HStack>
        </Box>
      )}

      {(error || localError) && (
        <Alert status="error" size="sm">
          <AlertIcon />
          {error || localError}
        </Alert>
      )}
    </VStack>
  );
}