'use client';

import { useState, useEffect } from 'react';
import { useQuery, useMutation } from '@tanstack/react-query';
import {
  Box,
  Container,
  Card,
  CardHeader,
  CardBody,
  Heading,
  VStack,
  HStack,
  FormControl,
  FormLabel,
  Input,
  Select,
  Button,
  Alert,
  AlertIcon,
  Text,
  Badge,
  Divider,
  useToast,
  Spinner,
  Radio,
  RadioGroup,
  Stack,
  Image,
  SimpleGrid,
} from '@chakra-ui/react';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import { getCustomerVerificationData, updateCustomerVerification } from '@/actions/verification';
import { calculateNextVerificationDate } from '@/utils/verification';
import { fileToBase64 } from '@/utils/fileToBase64';
import { FileUploader } from '@/components/verification/FileUploader';

const validationSchema = Yup.object({
  hologramType: Yup.string().required('Tipo de holograma es requerido'),
  verificationCertificate: Yup.mixed().required('Certificado de verificación es requerido'),
  hologramPhoto: Yup.mixed().required('Foto del holograma es requerida'),
  nextVerificationDate: Yup.date().nullable(),
});

interface CustomerVerificationFormProps {
  token: string;
}

export function CustomerVerificationForm({ token }: CustomerVerificationFormProps) {
  const [step, setStep] = useState<'loading' | 'form' | 'success' | 'error'>('loading');
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const toast = useToast();

  const { data: verificationData, isLoading, error } = useQuery({
    queryKey: ['customer-verification', token],
    queryFn: () => getCustomerVerificationData(token),
    retry: false,
  });

  const updateMutation = useMutation({
    mutationFn: (data: any) => updateCustomerVerification(token, data),
    onSuccess: () => {
      setStep('success');
      toast({
        title: 'Verificación completada',
        description: 'Tu información se ha guardado correctamente',
        status: 'success',
        duration: 5000,
        isClosable: true,
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.message || 'Error al actualizar la verificación',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    },
  });

  const formik = useFormik({
    initialValues: {
      hologramType: '' as '00' | '0' | '1' | '2' | '',
      verificationCertificate: null as File | null,
      hologramPhoto: null as File | null,
      nextVerificationDate: '',
    },
    validationSchema,
    onSubmit: async (values) => {
      try {
        const nextDate = values.nextVerificationDate 
          ? new Date(values.nextVerificationDate).toISOString()
          : calculateNextVerificationDate(verificationData?.plateNumber || '', values.hologramType as '00' | '0' | '1' | '2')?.toISOString();

        const verificationCertificateBase64 = values.verificationCertificate ? await fileToBase64(values.verificationCertificate) : undefined;
        const hologramPhotoBase64 = values.hologramPhoto ? await fileToBase64(values.hologramPhoto) : undefined;

        updateMutation.mutate({
          verificationCertificate: verificationCertificateBase64,
          hologramPhoto: hologramPhotoBase64,
          hologramType: values.hologramType as '00' | '0' | '1' | '2',
          nextVerificationDate: nextDate,
        });
      } catch (error) {
        console.error('Error converting files to base64:', error);
        // TODO: Show error message to user
      }
    },
  });

  useEffect(() => {
    if (error) {
      setStep('error');
      setErrorMessage('Token inválido o expirado');
    } else if (verificationData) {
      setStep('form');
    }
  }, [error, verificationData]);

  const getHologramDescription = (type: string) => {
    switch (type) {
      case '00':
        return 'Exento por 2 años';
      case '0':
        return 'Cumple con la norma (cada 6 meses)';
      case '1':
        return 'Primera verificación reprobada (cada 6 meses)';
      case '2':
        return 'Segunda verificación reprobada (cada 6 meses)';
      default:
        return '';
    }
  };

  const getHologramColor = (type: string) => {
    switch (type) {
      case '00':
        return 'blue';
      case '0':
        return 'green';
      case '1':
        return 'yellow';
      case '2':
        return 'red';
      default:
        return 'gray';
    }
  };

  if (step === 'loading' || isLoading) {
    return (
      <Container maxW="md" py={20}>
        <Box display="flex" justifyContent="center">
          <Spinner size="xl" />
        </Box>
      </Container>
    );
  }

  if (step === 'error') {
    return (
      <Container maxW="md" py={20}>
        <Alert status="error">
          <AlertIcon />
          {errorMessage}
        </Alert>
      </Container>
    );
  }

  if (step === 'success') {
    return (
      <Container maxW="md" py={20}>
        <Card>
          <CardHeader>
            <Heading size="md" color="green.600">
              ✅ Verificación Completada
            </Heading>
          </CardHeader>
          <CardBody>
            <VStack spacing={4}>
              <Alert status="success">
                <AlertIcon />
                Tu información se ha guardado correctamente. El proceso de verificación ha sido completado.
              </Alert>
              
              <Box width="full" p={4} bg="blue.50" borderRadius="md">
                <Text fontWeight="bold" mb={2}>Información del vehículo:</Text>
                <Text><strong>Placa:</strong> {verificationData?.plateNumber}</Text>
                <Text><strong>Tipo de holograma:</strong> 
                  <Badge ml={2} colorScheme={getHologramColor(formik.values.hologramType)}>
                    {formik.values.hologramType} - {getHologramDescription(formik.values.hologramType)}
                  </Badge>
                </Text>
                {formik.values.nextVerificationDate && (
                  <Text><strong>Próxima verificación:</strong> {new Date(formik.values.nextVerificationDate).toLocaleDateString('es-MX')}</Text>
                )}
              </Box>

              <Text fontSize="sm" color="gray.600" textAlign="center">
                Recibirás recordatorios cuando se acerque la fecha de tu próxima verificación.
              </Text>
            </VStack>
          </CardBody>
        </Card>
      </Container>
    );
  }

  return (
    <Container maxW="2xl" py={8}>
      <Card>
        <CardHeader>
          <Heading size="lg" textAlign="center">
            Completar Verificación Vehicular
          </Heading>
        </CardHeader>
        <CardBody>
          <VStack spacing={6}>
            {/* Vehicle Information */}
            <Box width="full" p={4} bg="gray.50" borderRadius="md">
              <Text fontWeight="bold" mb={2}>Información del Vehículo</Text>
              <SimpleGrid columns={2} spacing={4}>
                <Text><strong>Placa:</strong> {verificationData?.plateNumber}</Text>
                <Text><strong>Fecha de verificación:</strong> {new Date(verificationData?.verificationDate).toLocaleDateString('es-MX')}</Text>
              </SimpleGrid>
            </Box>

            <Divider />

            <form onSubmit={formik.handleSubmit} style={{ width: '100%' }}>
              <VStack spacing={6}>
                <FormControl isRequired isInvalid={!!formik.errors.hologramType && formik.touched.hologramType}>
                  <FormLabel fontWeight="bold">Tipo de Holograma Obtenido</FormLabel>
                  <RadioGroup 
                    value={formik.values.hologramType} 
                    onChange={(value) => formik.setFieldValue('hologramType', value)}
                  >
                    <Stack spacing={3}>
                      <Radio value="00" colorScheme="blue">
                        <Box>
                          <Text fontWeight="medium">00 - Exento</Text>
                          <Text fontSize="sm" color="gray.600">Exento por 2 años</Text>
                        </Box>
                      </Radio>
                      <Radio value="0" colorScheme="green">
                        <Box>
                          <Text fontWeight="medium">0 - Cumple</Text>
                          <Text fontSize="sm" color="gray.600">Cumple con la norma (cada 6 meses)</Text>
                        </Box>
                      </Radio>
                      <Radio value="1" colorScheme="yellow">
                        <Box>
                          <Text fontWeight="medium">1 - Primera reprobada</Text>
                          <Text fontSize="sm" color="gray.600">Primera verificación reprobada (cada 6 meses)</Text>
                        </Box>
                      </Radio>
                      <Radio value="2" colorScheme="red">
                        <Box>
                          <Text fontWeight="medium">2 - Segunda reprobada</Text>
                          <Text fontSize="sm" color="gray.600">Segunda verificación reprobada (cada 6 meses)</Text>
                        </Box>
                      </Radio>
                    </Stack>
                  </RadioGroup>
                  {formik.errors.hologramType && formik.touched.hologramType && (
                    <Text color="red.500" fontSize="sm">{formik.errors.hologramType}</Text>
                  )}
                </FormControl>

                <FormControl isRequired isInvalid={!!formik.errors.verificationCertificate && formik.touched.verificationCertificate}>
                  <FormLabel fontWeight="bold">Certificado de Verificación</FormLabel>
                  <Text fontSize="sm" color="gray.600" mb={2}>
                    Sube una foto clara del certificado de verificación
                  </Text>
                  <FileUploader
                    accept="image/*,application/pdf"
                    onFileSelect={(file: File) => formik.setFieldValue('verificationCertificate', file)}
                    selectedFile={formik.values.verificationCertificate}
                  />
                  {formik.errors.verificationCertificate && formik.touched.verificationCertificate && (
                    <Text color="red.500" fontSize="sm">{formik.errors.verificationCertificate}</Text>
                  )}
                </FormControl>

                <FormControl isRequired isInvalid={!!formik.errors.hologramPhoto && formik.touched.hologramPhoto}>
                  <FormLabel fontWeight="bold">Foto del Holograma</FormLabel>
                  <Text fontSize="sm" color="gray.600" mb={2}>
                    Sube una foto clara y visible del holograma de verificación
                  </Text>
                  <FileUploader
                    accept="image/*"
                    onFileSelect={(file: File) => formik.setFieldValue('hologramPhoto', file)}
                    selectedFile={formik.values.hologramPhoto}
                  />
                  {formik.errors.hologramPhoto && formik.touched.hologramPhoto && (
                    <Text color="red.500" fontSize="sm">{formik.errors.hologramPhoto}</Text>
                  )}
                </FormControl>

                <FormControl>
                  <FormLabel fontWeight="bold">Fecha de Próxima Verificación (Opcional)</FormLabel>
                  <Text fontSize="sm" color="gray.600" mb={2}>
                    Si el certificado muestra la fecha de la próxima verificación, ingrésala aquí
                  </Text>
                  <Input
                    type="date"
                    name="nextVerificationDate"
                    value={formik.values.nextVerificationDate}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                  />
                </FormControl>

                <Button
                  type="submit"
                  colorScheme="blue"
                  size="lg"
                  width="full"
                  isLoading={updateMutation.isPending}
                  loadingText="Guardando..."
                >
                  Completar Verificación
                </Button>
              </VStack>
            </form>

            <Alert status="info" fontSize="sm">
              <AlertIcon />
              <Box>
                <Text fontWeight="bold">Importante:</Text>
                <Text>
                  Asegúrate de que las fotos sean claras y legibles. Esta información será revisada por el equipo de OneCarNow.
                </Text>
              </Box>
            </Alert>
          </VStack>
        </CardBody>
      </Card>
    </Container>
  );
}