/**
 * Determines the current verification status based on dates
 */
export function getVerificationStatus(verificationDate: Date, nextDue: Date | null): string {
  if (!nextDue) return 'unknown';
  
  const now = new Date();
  const oneWeekFromNow = new Date();
  oneWeekFromNow.setDate(now.getDate() + 7);
  
  if (now > nextDue) {
    return 'expired';
  } else if (now >= new Date(nextDue.getTime() - 7 * 24 * 60 * 60 * 1000)) {
    return 'expiring_soon';
  } else {
    return 'valid';
  }
}

/**
 * Formats verification status for UI display
 */
export function formatVerificationStatus(status: string): { label: string; color: string } {
  switch (status) {
    case 'valid':
      return { label: 'Vigente', color: 'green' };
    case 'expiring_soon':
      return { label: 'Próxima a vencer', color: 'yellow' };
    case 'expired':
      return { label: 'Vencida', color: 'red' };
    case 'unknown':
      return { label: 'Estado desconocido', color: 'gray' };
    default:
      return { label: 'Estado desconocido', color: 'gray' };
  }
}

/**
 * Calculates next verification date based on hologram type
 */
export function calculateNextVerificationDate(plateNumber: string, hologramType: '00' | '0' | '1' | '2'): Date | null {
  const now = new Date();
  const nextDate = new Date(now);
  
  switch (hologramType) {
    case '00': // Exento
      nextDate.setFullYear(now.getFullYear() + 2);
      break;
    case '0': // Cumple
    case '1': // Primera reprobada
    case '2': // Segunda reprobada
      nextDate.setMonth(now.getMonth() + 6);
      break;
    default:
      return null;
  }
  
  return nextDate;
}

/**
 * Validates Mexican license plate format
 */
export function validatePlateNumber(plateNumber: string): boolean {
  if (!plateNumber) return false;
  
  const cleanPlate = plateNumber.replace(/\s/g, '').toUpperCase();
  
  // Mexican plate formats:
  // Old format: 3 letters + 3 numbers (ABC123)
  // New format: 3 numbers + 3 letters (123ABC)
  // Also allow 2 letters + 5 numbers for some special cases
  const oldFormat = /^[A-Z]{3}[0-9]{3}$/;
  const newFormat = /^[0-9]{3}[A-Z]{3}$/;
  const specialFormat = /^[A-Z]{2}[0-9]{5}$/;
  
  return oldFormat.test(cleanPlate) || newFormat.test(cleanPlate) || specialFormat.test(cleanPlate);
}