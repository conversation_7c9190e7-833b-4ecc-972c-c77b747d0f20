/**
 * Converts a File object to a base64 data URL string
 * @param file - The File object to convert
 * @returns Promise that resolves to a base64 data URL string
 */
export function fileToBase64(file: File): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    
    reader.onload = () => {
      if (typeof reader.result === 'string') {
        resolve(reader.result);
      } else {
        reject(new Error('Failed to convert file to base64'));
      }
    };
    
    reader.onerror = () => {
      reject(new Error('Error reading file'));
    };
    
    reader.readAsDataURL(file);
  });
}

/**
 * Converts multiple File objects to base64 data URL strings
 * @param files - Array of File objects to convert
 * @returns Promise that resolves to an array of base64 data URL strings
 */
export async function filesToBase64(files: File[]): Promise<string[]> {
  const promises = files.map(file => fileToBase64(file));
  return Promise.all(promises);
}