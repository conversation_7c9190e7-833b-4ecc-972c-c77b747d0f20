'use client';

import { useState, Suspense } from 'react';
import { useSession } from 'next-auth/react';
import {
  Box,
  Container,
  Heading,
  VStack,
  Card,
  CardHeader,
  CardBody,
  HStack,
  Input,
  InputGroup,
  InputLeftElement,
  Select,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Badge,
  IconButton,
  useToast,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalFooter,
  ModalBody,
  ModalCloseButton,
  useDisclosure,
  FormControl,
  FormLabel,
  useColorModeValue,
  Alert,
  AlertIcon,
  Divider,
  Spinner,
  Text,
} from '@chakra-ui/react';
import { Button } from '@/components/ui/button';
import { FiSearch, FiPlus, FiEye, FiEdit, FiMapPin, FiLock } from 'react-icons/fi';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { getVerificationCenters, getCenterStats } from '@/actions/verification';

export function VerificationCentersPage() {
  const { data: session, status } = useSession();
  const [searchTerm, setSearchTerm] = useState('');
  const [stateFilter, setStateFilter] = useState('');
  const [selectedCenter, setSelectedCenter] = useState<any>(null);

  console.log('VerificationCentersPage renderizando...');

  // Log para debugging de sesión
  console.log('🔐 Estado de sesión:', {
    status,
    sessionExists: !!session,
    user: session?.user,
    userType: (session?.user as any)?.userType
  });

  // Control de acceso: Solo usuarios "eco" y "superAdmin" pueden acceder
  const user = session?.user as any;
  const hasAccess = user && (user.userType === 'eco' || user.userType === 'superAdmin');

  // Mostrar loading mientras se verifica la sesión
  if (status === 'loading') {
    return (
      <Container maxW="7xl" py={8}>
        <VStack spacing={6} align="center" justify="center" minH="400px">
          <Spinner size="xl" color="orange.500" />
          <Text>Verificando acceso...</Text>
        </VStack>
      </Container>
    );
  }

  // Mostrar mensaje de acceso denegado si el usuario no tiene permisos
  if (status === 'authenticated' && !hasAccess) {
    return (
      <Container maxW="7xl" py={8}>
        <VStack spacing={6} align="center" justify="center" minH="400px">
          <Card bg={useColorModeValue('white', 'gray.800')} p={8} textAlign="center">
            <CardBody>
              <VStack spacing={4}>
                <Box color="red.500" fontSize="4xl">
                  <FiLock />
                </Box>
                <Heading size="lg" color="red.500">
                  Acceso Denegado
                </Heading>
                <Text color="gray.600" maxW="md">
                  Solo los usuarios de tipo "eco" y "superAdmin" tienen acceso a la gestión de Verificentros.
                </Text>
                <Text fontSize="sm" color="gray.500">
                  Tu tipo de usuario actual: {user?.userType || 'No definido'}
                </Text>
              </VStack>
            </CardBody>
          </Card>
        </VStack>
      </Container>
    );
  }
  const [newCenter, setNewCenter] = useState({
    name: '',
    code: '',
    state: 'CDMX' as 'CDMX' | 'EDOMEX',
    organizationId: '507f1f77bcf86cd799439011', // Default organization ID
    location: {
      address: '',
      city: '',
      state: 'CDMX',
    },
    authorizedFor: ['gasoline', 'diesel'],
  });

  const bgColor = useColorModeValue('white', 'gray.800');
  const toast = useToast();
  const { isOpen, onOpen, onClose } = useDisclosure();
  const { isOpen: isCreateOpen, onOpen: onCreateOpen, onClose: onCreateClose } = useDisclosure();
  const queryClient = useQueryClient();

  // Fetch verification centers
  const { data: centers, isLoading: centersLoading, error: centersError } = useQuery({
    queryKey: ['verification-centers', stateFilter],
    queryFn: () => {
      console.log('🔄 React Query - Ejecutando getVerificationCenters...', { stateFilter });
      return getVerificationCenters(stateFilter as 'CDMX' | 'EDOMEX' | undefined);
    },
    enabled: status === 'authenticated', // Solo ejecutar cuando el usuario esté autenticado
    retry: 1,
  });

  // Log para debugging
  console.log('📊 Estado de centros:', {
    centers,
    centersLoading,
    centersError,
    stateFilter
  });

  // Fetch center stats when a center is selected
  const { data: centerStats } = useQuery({
    queryKey: ['center-stats', selectedCenter?._id],
    queryFn: () => getCenterStats(selectedCenter._id),
    enabled: !!selectedCenter?._id,
  });

  // Función para crear centro usando el endpoint de vendor (no requiere admin platform)
  const createCenterClient = async (data: any) => {
    const user = session?.user as any;
    if (!user) {
      throw new Error('Usuario no autenticado');
    }

    // Verificar permisos - Solo usuarios "superAdmin" pueden crear centros
    if (user.userType !== 'superAdmin') {
      throw new Error('Usuario no autorizado para crear centros de verificación');
    }

    console.log('📡 Enviando datos al backend (vendor endpoint):', data);

    const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL;
    const response = await fetch(`${API_BASE_URL}/vendor-platform/emissions-verification/vendor/verification-centers`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${user.accessToken}`,
        'Content-Type': 'application/json',
        // No incluir 'adpt': 'true' porque usamos el endpoint de vendor
      },
      body: JSON.stringify(data),
    });

    console.log('📡 Respuesta del backend (vendor):', {
      status: response.status,
      statusText: response.statusText,
      ok: response.ok
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ Error del backend (vendor):', errorText);

      let errorMessage = 'Error al crear el centro de verificación';
      try {
        const errorData = JSON.parse(errorText);
        errorMessage = errorData.message || errorMessage;
      } catch (e) {
        // Si no se puede parsear como JSON, usar el texto completo
        errorMessage = errorText || errorMessage;
      }

      throw new Error(errorMessage);
    }

    const result = await response.json();
    return result.data;
  };

  // Create center mutation
  const createCenterMutation = useMutation({
    mutationFn: createCenterClient,
    onSuccess: () => {
      toast({
        title: 'Centro creado exitosamente',
        status: 'success',
        duration: 3000,
        isClosable: true,
      });
      queryClient.invalidateQueries({ queryKey: ['verification-centers'] });
      onCreateClose();
      setNewCenter({
        name: '',
        code: '',
        state: 'CDMX',
        organizationId: '507f1f77bcf86cd799439011',
        location: {
          address: '',
          city: '',
          state: 'CDMX',
        },
        authorizedFor: ['gasoline', 'diesel'],
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Error al crear centro',
        description: error.message,
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    },
  });

  const filteredCenters = centers?.filter(center =>
    center.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    center.code.toLowerCase().includes(searchTerm.toLowerCase())
  ) || [];

  const handleCreateCenter = () => {
    // Validación más completa de campos requeridos
    if (!newCenter.name || !newCenter.code || !newCenter.location.address || !newCenter.location.city) {
      toast({
        title: 'Campos requeridos',
        description: 'Por favor completa todos los campos obligatorios: nombre, código, dirección y ciudad',
        status: 'warning',
        duration: 3000,
        isClosable: true,
      });
      return;
    }

    console.log('🚀 Creando centro:', newCenter);

    // Debug: Verificar sesión antes de crear
    const user = session?.user as any;
    console.log('👤 Sesión antes de crear:', {
      sessionStatus: status,
      sessionExists: !!session,
      userExists: !!user,
      userType: user?.userType,
      email: user?.email,
      hasToken: !!user?.accessToken
    });

    createCenterMutation.mutate(newCenter);
  };

  const getStatusColor = (isActive: boolean) => {
    return isActive ? 'green' : 'red';
  };

  const getStatusText = (isActive: boolean) => {
    return isActive ? 'Activo' : 'Inactivo';
  };

  return (
    <Container maxW="7xl" py={8}>
      <VStack spacing={6} align="stretch">
        {/* Header */}
        <Card bg={bgColor}>
          <CardHeader>
            <HStack justify="space-between">
              <Heading size="lg">Gestión de Centros de Verificación</Heading>
              <HStack>
                {/* Solo mostrar botón de crear centro para superAdmin */}
                {user?.userType === 'superAdmin' && (
                  <Button onClick={onCreateOpen}>
                    <FiPlus className="mr-2 h-4 w-4" />
                    Nuevo Centro
                  </Button>
                )}
              </HStack>
            </HStack>
          </CardHeader>
          <CardBody>
            <HStack spacing={4}>
              <InputGroup maxW="300px">
                <InputLeftElement pointerEvents="none">
                  <FiSearch color="gray.300" />
                </InputLeftElement>
                <Input
                  placeholder="Buscar centro..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </InputGroup>

              <Select maxW="200px" value={stateFilter} onChange={(e) => setStateFilter(e.target.value)}>
                <option value="">Todos los estados</option>
                <option value="CDMX">CDMX</option>
                <option value="EDOMEX">Estado de México</option>
              </Select>
            </HStack>
          </CardBody>
        </Card>

        {/* Centers Table */}
        <Card bg={bgColor}>
          <CardHeader>
            <Heading size="md">Centros Registrados ({filteredCenters.length})</Heading>
          </CardHeader>
          <CardBody>
            {centersLoading ? (
              <Alert status="info">
                <AlertIcon />
                Cargando centros de verificación...
              </Alert>
            ) : (
              <Table variant="simple">
                <Thead>
                  <Tr>
                    <Th>Código</Th>
                    <Th>Nombre</Th>
                    <Th>Estado</Th>
                    <Th>Ubicación</Th>
                    <Th>Estado</Th>
                    <Th>Acciones</Th>
                  </Tr>
                </Thead>
                <Tbody>
                  {filteredCenters.map((center: any) => (
                    <Tr key={center._id}>
                      <Td fontWeight="bold" color="orange.500">{center.code}</Td>
                      <Td>{center.name}</Td>
                      <Td>{center.state}</Td>
                      <Td>
                        <VStack align="start" spacing={0}>
                          <Box fontSize="sm">{center.location?.city}</Box>
                          <Box fontSize="xs" color="gray.500">{center.location?.address}</Box>
                        </VStack>
                      </Td>
                      <Td>
                        <Badge colorScheme={getStatusColor(center.isActive)}>
                          {getStatusText(center.isActive)}
                        </Badge>
                      </Td>
                      <Td>
                        <HStack>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              setSelectedCenter(center);
                              onOpen();
                            }}
                          >
                            <FiEye className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                          >
                            <FiEdit className="h-4 w-4" />
                          </Button>
                        </HStack>
                      </Td>
                    </Tr>
                  ))}
                </Tbody>
              </Table>
            )}

            {!centersLoading && filteredCenters.length === 0 && (
              <Alert status="info">
                <AlertIcon />
                {searchTerm || stateFilter ? 'No se encontraron centros con los filtros aplicados' : 'No hay centros registrados'}
              </Alert>
            )}
          </CardBody>
        </Card>
      </VStack>

      {/* Modal de detalles del centro */}
      <Modal isOpen={isOpen} onClose={onClose} size="xl">
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>
            <HStack>
              <FiMapPin />
              <Box>Detalles del Centro</Box>
            </HStack>
          </ModalHeader>
          <ModalCloseButton />
          <ModalBody>
            {selectedCenter && (
              <VStack spacing={4} align="start">
                <Box>
                  <Box fontWeight="bold" color="orange.500">Código:</Box>
                  <Box fontSize="lg">{selectedCenter.code}</Box>
                </Box>

                <Box>
                  <Box fontWeight="bold">Nombre:</Box>
                  <Box>{selectedCenter.name}</Box>
                </Box>

                <Box>
                  <Box fontWeight="bold">Estado:</Box>
                  <Box>{selectedCenter.state}</Box>
                </Box>

                <Box>
                  <Box fontWeight="bold">Ubicación:</Box>
                  <Box>{selectedCenter.location?.address}</Box>
                  <Box color="gray.500">{selectedCenter.location?.city}, {selectedCenter.location?.state}</Box>
                </Box>

                <Box>
                  <Box fontWeight="bold">Estado:</Box>
                  <Badge colorScheme={getStatusColor(selectedCenter.isActive)}>
                    {getStatusText(selectedCenter.isActive)}
                  </Badge>
                </Box>

                {centerStats && (
                  <>
                    <Divider />
                    <Box>
                      <Box fontWeight="bold" mb={2}>Estadísticas:</Box>
                      <VStack align="start" spacing={2}>
                        <HStack>
                          <Box>Total verificaciones:</Box>
                          <Box fontWeight="bold">{centerStats.total}</Box>
                        </HStack>
                        <HStack>
                          <Box>Pendientes cliente:</Box>
                          <Box fontWeight="bold" color="yellow.500">{centerStats.pendingCustomer}</Box>
                        </HStack>
                        <HStack>
                          <Box>Completadas:</Box>
                          <Box fontWeight="bold" color="green.500">{centerStats.completed}</Box>
                        </HStack>
                        <HStack>
                          <Box>Este mes:</Box>
                          <Box fontWeight="bold">{centerStats.thisMonth}</Box>
                        </HStack>
                        <HStack>
                          <Box>Tasa de finalización:</Box>
                          <Box fontWeight="bold">{centerStats.completionRate}%</Box>
                        </HStack>
                      </VStack>
                    </Box>
                  </>
                )}
              </VStack>
            )}
          </ModalBody>
          <ModalFooter>
            <Button onClick={onClose} variant="outline">Cerrar</Button>
          </ModalFooter>
        </ModalContent>
      </Modal>

      {/* Modal para crear nuevo centro */}
      <Modal isOpen={isCreateOpen} onClose={onCreateClose} size="lg">
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>Crear Nuevo Centro de Verificación</ModalHeader>
          <ModalCloseButton />
          <ModalBody>
            <VStack spacing={4}>
              <FormControl isRequired>
                <FormLabel>Nombre del Centro</FormLabel>
                <Input
                  value={newCenter.name}
                  onChange={(e) => setNewCenter({ ...newCenter, name: e.target.value })}
                  placeholder="Verificentro Central"
                />
              </FormControl>

              <FormControl isRequired>
                <FormLabel>Código del Centro</FormLabel>
                <Input
                  value={newCenter.code}
                  onChange={(e) => setNewCenter({ ...newCenter, code: e.target.value.toUpperCase() })}
                  placeholder="MH01"
                />
              </FormControl>

              <FormControl isRequired>
                <FormLabel>Estado</FormLabel>
                <Select
                  value={newCenter.state}
                  onChange={(e) => setNewCenter({
                    ...newCenter,
                    state: e.target.value as 'CDMX' | 'EDOMEX',
                    location: { ...newCenter.location, state: e.target.value }
                  })}
                  placeholder="Selecciona un estado"
                >
                  <option value="CDMX">Ciudad de México</option>
                  <option value="EDOMEX">Estado de México</option>
                </Select>
              </FormControl>

              <FormControl>
                <FormLabel>ID de Organización</FormLabel>
                <Input
                  value={newCenter.organizationId}
                  isReadOnly
                  bg="gray.100"
                  placeholder="ID de la organización propietaria"
                />
              </FormControl>

              <FormControl isRequired>
                <FormLabel>Dirección</FormLabel>
                <Input
                  value={newCenter.location.address}
                  onChange={(e) => setNewCenter({
                    ...newCenter,
                    location: { ...newCenter.location, address: e.target.value }
                  })}
                  placeholder="Av. Insurgentes Sur 123, Col. Roma Norte"
                />
              </FormControl>

              <FormControl isRequired>
                <FormLabel>Ciudad</FormLabel>
                <Input
                  value={newCenter.location.city}
                  onChange={(e) => setNewCenter({
                    ...newCenter,
                    location: { ...newCenter.location, city: e.target.value }
                  })}
                  placeholder={newCenter.state === 'CDMX' ? 'Ciudad de México' : 'Nezahualcóyotl'}
                />
              </FormControl>
            </VStack>
          </ModalBody>
          <ModalFooter>
            <Button variant="outline" onClick={onCreateClose}>
              Cancelar
            </Button>
            <Button
              onClick={handleCreateCenter}
              disabled={createCenterMutation.isPending}
            >
              Crear Centro
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </Container>
  );
}