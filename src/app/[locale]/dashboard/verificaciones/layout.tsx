'use client';

import React from 'react';
import { useSession } from 'next-auth/react';
import { Box, Container, VStack, Card, CardBody, Heading, Text, Spinner } from '@chakra-ui/react';
import { FiLock } from 'react-icons/fi';

export default function VerificationsLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const { data: session, status } = useSession();

  // Control de acceso: Solo usuarios "eco" y "superAdmin" pueden acceder
  const user = session?.user as any;
  const hasAccess = user && (user.userType === 'eco' || user.userType === 'superAdmin');

  // Mostrar loading mientras se verifica la sesión
  if (status === 'loading') {
    return (
      <div className="min-h-screen bg-gray-50">
        <Container maxW="7xl" py={8}>
          <VStack spacing={6} align="center" justify="center" minH="400px">
            <Spinner size="xl" color="orange.500" />
            <Text>Verificando acceso...</Text>
          </VStack>
        </Container>
      </div>
    );
  }

  // Mostrar mensaje de acceso denegado si el usuario no tiene permisos
  if (status === 'authenticated' && !hasAccess) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Container maxW="7xl" py={8}>
          <VStack spacing={6} align="center" justify="center" minH="400px">
            <Card bg="white" p={8} textAlign="center">
              <CardBody>
                <VStack spacing={4}>
                  <Box color="red.500" fontSize="4xl">
                    <FiLock />
                  </Box>
                  <Heading size="lg" color="red.500">
                    Acceso Denegado
                  </Heading>
                  <Text color="gray.600" maxW="md">
                    Solo los usuarios de tipo "eco" y "superAdmin" tienen acceso a la sección de Verificaciones.
                  </Text>
                  <Text fontSize="sm" color="gray.500">
                    Tu tipo de usuario actual: {user?.userType || 'No definido'}
                  </Text>
                </VStack>
              </CardBody>
            </Card>
          </VStack>
        </Container>
      </div>
    );
  }

  // Si el usuario tiene acceso, mostrar el contenido
  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto py-6">
        {children}
      </div>
    </div>
  );
}