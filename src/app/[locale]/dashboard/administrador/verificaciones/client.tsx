'use client';

import { useState } from 'react';
import { 
  <PERSON>, 
  Container, 
  Tabs, 
  <PERSON>b<PERSON>ist, 
  TabPanels, 
  Tab, 
  TabPanel, 
  Heading,
  VStack,
  Card,
  CardHeader,
  CardBody,
  SimpleGrid,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  StatArrow,
  useColorModeValue,
} from '@chakra-ui/react';
import { AdminVerificationDashboard } from '@/components/verification/AdminVerificationDashboard';
import { VerificationCenterManager } from '@/components/verification/VerificationCenterManager';
import { useQuery } from '@tanstack/react-query';

export function VerificationManagementPage() {
  const [activeTab, setActiveTab] = useState(0);
  const bgColor = useColorModeValue('white', 'gray.800');

  // Mock data for statistics - in real implementation, this would come from API
  const mockStats = {
    totalVerifications: 1247,
    pendingVerifications: 23,
    completedThisMonth: 156,
    averageCompletionTime: 2.3,
    expiringThisMonth: 45,
  };

  return (
    <Container maxW="7xl" py={8}>
      <VStack spacing={6} align="stretch">
        {/* Header */}
        <Card bg={bgColor}>
          <CardHeader>
            <Heading size="lg">Administración de Verificaciones</Heading>
          </CardHeader>
          <CardBody>
            <SimpleGrid columns={[1, 2, 5]} spacing={4}>
              <Stat>
                <StatLabel>Total Verificaciones</StatLabel>
                <StatNumber>{mockStats.totalVerifications.toLocaleString()}</StatNumber>
                <StatHelpText>
                  <StatArrow type="increase" />
                  12.3%
                </StatHelpText>
              </Stat>
              
              <Stat>
                <StatLabel>Pendientes</StatLabel>
                <StatNumber color="yellow.500">{mockStats.pendingVerifications}</StatNumber>
                <StatHelpText>Esperando cliente</StatHelpText>
              </Stat>
              
              <Stat>
                <StatLabel>Completadas este mes</StatLabel>
                <StatNumber color="green.500">{mockStats.completedThisMonth}</StatNumber>
                <StatHelpText>
                  <StatArrow type="increase" />
                  8.2%
                </StatHelpText>
              </Stat>
              
              <Stat>
                <StatLabel>Tiempo promedio</StatLabel>
                <StatNumber>{mockStats.averageCompletionTime} días</StatNumber>
                <StatHelpText>Para completar</StatHelpText>
              </Stat>
              
              <Stat>
                <StatLabel>Vencen este mes</StatLabel>
                <StatNumber color="red.500">{mockStats.expiringThisMonth}</StatNumber>
                <StatHelpText>Requieren atención</StatHelpText>
              </Stat>
            </SimpleGrid>
          </CardBody>
        </Card>

        {/* Tabs */}
        <Tabs 
          colorScheme="blue" 
          variant="enclosed"
          index={activeTab}
          onChange={setActiveTab}
        >
          <TabList>
            <Tab>Dashboard General</Tab>
            <Tab>Gestión de Verificentros</Tab>
            <Tab>Reportes</Tab>
          </TabList>

          <TabPanels>
            <TabPanel p={0}>
              <Card bg={bgColor}>
                <CardBody>
                  <AdminVerificationDashboard />
                </CardBody>
              </Card>
            </TabPanel>
            
            <TabPanel p={0}>
              <Card bg={bgColor}>
                <CardBody>
                  <VerificationCenterManager allowCreate={true} />
                </CardBody>
              </Card>
            </TabPanel>
            
            <TabPanel p={0}>
              <Card bg={bgColor}>
                <CardBody>
                  <VStack spacing={6}>
                    <Heading size="md">Reportes de Verificación</Heading>
                    
                    <SimpleGrid columns={[1, 2]} spacing={6} width="full">
                      <Card>
                        <CardHeader>
                          <Heading size="sm">Verificaciones por Estado</Heading>
                        </CardHeader>
                        <CardBody>
                          <SimpleGrid columns={2} spacing={4}>
                            <Box>
                              <Box color="green.500" fontWeight="bold">Completadas</Box>
                              <Box fontSize="2xl">{mockStats.totalVerifications - mockStats.pendingVerifications}</Box>
                            </Box>
                            <Box>
                              <Box color="yellow.500" fontWeight="bold">Pendientes</Box>
                              <Box fontSize="2xl">{mockStats.pendingVerifications}</Box>
                            </Box>
                          </SimpleGrid>
                        </CardBody>
                      </Card>
                      
                      <Card>
                        <CardHeader>
                          <Heading size="sm">Verificaciones por Mes</Heading>
                        </CardHeader>
                        <CardBody>
                          <VStack align="start">
                            <Box>Este mes: {mockStats.completedThisMonth}</Box>
                            <Box>Mes anterior: {mockStats.completedThisMonth - 12}</Box>
                            <Box>Crecimiento: +8.2%</Box>
                          </VStack>
                        </CardBody>
                      </Card>
                      
                      <Card>
                        <CardHeader>
                          <Heading size="sm">Holograma Distribution</Heading>
                        </CardHeader>
                        <CardBody>
                          <VStack align="start">
                            <Box>Holograma 00: 156</Box>
                            <Box>Holograma 0: 892</Box>
                            <Box>Holograma 1: 145</Box>
                            <Box>Holograma 2: 54</Box>
                          </VStack>
                        </CardBody>
                      </Card>
                      
                      <Card>
                        <CardHeader>
                          <Heading size="sm">Verificentros Activos</Heading>
                        </CardHeader>
                        <CardBody>
                          <VStack align="start">
                            <Box>CDMX: 45 verificentros</Box>
                            <Box>EDOMEX: 23 verificentros</Box>
                            <Box>Total: 68 verificentros</Box>
                          </VStack>
                        </CardBody>
                      </Card>
                    </SimpleGrid>
                  </VStack>
                </CardBody>
              </Card>
            </TabPanel>
          </TabPanels>
        </Tabs>
      </VStack>
    </Container>
  );
}