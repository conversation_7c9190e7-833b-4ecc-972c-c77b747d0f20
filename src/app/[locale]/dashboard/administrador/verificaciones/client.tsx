'use client';

import { useState } from 'react';
import { useSession } from 'next-auth/react';
import {
  Box,
  Container,
  Tabs,
  <PERSON>bList,
  TabPanels,
  Tab,
  TabPanel,
  Heading,
  VStack,
  Card,
  CardHeader,
  CardBody,
  SimpleGrid,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  StatArrow,
  useColorModeValue,
  Spinner,
  Alert,
  AlertIcon,
} from '@chakra-ui/react';
import { AdminVerificationDashboard } from '@/components/verification/AdminVerificationDashboard';
import { VerificationCenterManager } from '@/components/verification/VerificationCenterManager';
import { useQuery } from '@tanstack/react-query';
import { getAdminDashboard } from '@/actions/verification';

export function VerificationManagementPage() {
  const [activeTab, setActiveTab] = useState(0);
  const bgColor = useColorModeValue('white', 'gray.800');

  // Importar useSession para verificar autenticación
  const { data: session, status } = useSession();

  // Log para debugging de sesión
  console.log('🔐 Admin Verificaciones - Estado de sesión:', {
    status,
    sessionExists: !!session,
    user: session?.user,
    userType: (session?.user as any)?.userType
  });

  // Fetch dashboard data usando API real
  const { data: dashboardData, isLoading: dashboardLoading, error: dashboardError } = useQuery({
    queryKey: ['admin-dashboard-stats'],
    queryFn: getAdminDashboard,
    enabled: status === 'authenticated', // Solo ejecutar cuando el usuario esté autenticado
    refetchInterval: 30000, // Refresh every 30 seconds
    retry: 1,
  });

  // Log para debugging
  console.log('📊 Estado del dashboard admin:', {
    dashboardData,
    dashboardLoading,
    dashboardError
  });

  // Usar datos reales o valores por defecto mientras carga
  const stats = dashboardData || {
    totalVerifications: 0,
    pendingVerifications: 0,
    completedThisMonth: 0,
    averageCompletionTime: 0,
    expiringThisMonth: 0,
  };

  return (
    <Container maxW="7xl" py={8}>
      <VStack spacing={6} align="stretch">
        {/* Header */}
        <Card bg={bgColor}>
          <CardHeader>
            <Heading size="lg">Administración de Verificaciones</Heading>
          </CardHeader>
          <CardBody>
            {dashboardLoading ? (
              <Box display="flex" justifyContent="center" p={8}>
                <Spinner size="lg" />
              </Box>
            ) : dashboardError ? (
              <Alert status="error">
                <AlertIcon />
                Error al cargar las estadísticas del dashboard
              </Alert>
            ) : (
              <SimpleGrid columns={[1, 2, 5]} spacing={4}>
                <Stat>
                  <StatLabel>Total Verificaciones</StatLabel>
                  <StatNumber>{stats.totalVerifications?.toLocaleString() || '0'}</StatNumber>
                  <StatHelpText>
                    <StatArrow type="increase" />
                    Histórico
                  </StatHelpText>
                </Stat>

                <Stat>
                  <StatLabel>Pendientes</StatLabel>
                  <StatNumber color="yellow.500">{stats.pendingVerifications || 0}</StatNumber>
                  <StatHelpText>Esperando cliente</StatHelpText>
                </Stat>

                <Stat>
                  <StatLabel>Completadas este mes</StatLabel>
                  <StatNumber color="green.500">{stats.completedThisMonth || 0}</StatNumber>
                  <StatHelpText>
                    <StatArrow type="increase" />
                    Este mes
                  </StatHelpText>
                </Stat>

                <Stat>
                  <StatLabel>Tiempo promedio</StatLabel>
                  <StatNumber>{stats.averageCompletionTime || 0} días</StatNumber>
                  <StatHelpText>Para completar</StatHelpText>
                </Stat>

                <Stat>
                  <StatLabel>Vencen este mes</StatLabel>
                  <StatNumber color="red.500">{stats.expiringThisMonth || 0}</StatNumber>
                  <StatHelpText>Requieren atención</StatHelpText>
                </Stat>
              </SimpleGrid>
            )}
          </CardBody>
        </Card>

        {/* Tabs */}
        <Tabs
          colorScheme="blue"
          variant="enclosed"
          index={activeTab}
          onChange={setActiveTab}
        >
          <TabList>
            <Tab>Dashboard General</Tab>
            <Tab>Gestión de Verificentros</Tab>
            <Tab>Reportes</Tab>
          </TabList>

          <TabPanels>
            <TabPanel p={0}>
              <Card bg={bgColor}>
                <CardBody>
                  <AdminVerificationDashboard />
                </CardBody>
              </Card>
            </TabPanel>

            <TabPanel p={0}>
              <Card bg={bgColor}>
                <CardBody>
                  <VerificationCenterManager allowCreate={true} />
                </CardBody>
              </Card>
            </TabPanel>

            <TabPanel p={0}>
              <Card bg={bgColor}>
                <CardBody>
                  <VStack spacing={6}>
                    <Heading size="md">Reportes de Verificación</Heading>

                    <SimpleGrid columns={[1, 2]} spacing={6} width="full">
                      <Card>
                        <CardHeader>
                          <Heading size="sm">Verificaciones por Estado</Heading>
                        </CardHeader>
                        <CardBody>
                          <SimpleGrid columns={2} spacing={4}>
                            <Box>
                              <Box color="green.500" fontWeight="bold">Completadas</Box>
                              <Box fontSize="2xl">{(stats.totalVerifications || 0) - (stats.pendingVerifications || 0)}</Box>
                            </Box>
                            <Box>
                              <Box color="yellow.500" fontWeight="bold">Pendientes</Box>
                              <Box fontSize="2xl">{stats.pendingVerifications || 0}</Box>
                            </Box>
                          </SimpleGrid>
                        </CardBody>
                      </Card>

                      <Card>
                        <CardHeader>
                          <Heading size="sm">Verificaciones por Mes</Heading>
                        </CardHeader>
                        <CardBody>
                          <VStack align="start">
                            <Box>Este mes: {stats.completedThisMonth || 0}</Box>
                            <Box>Mes anterior: {(stats.completedThisMonth || 0) - 12}</Box>
                            <Box>Crecimiento: +8.2%</Box>
                          </VStack>
                        </CardBody>
                      </Card>

                      <Card>
                        <CardHeader>
                          <Heading size="sm">Holograma Distribution</Heading>
                        </CardHeader>
                        <CardBody>
                          <VStack align="start">
                            <Box>Holograma 00: 156</Box>
                            <Box>Holograma 0: 892</Box>
                            <Box>Holograma 1: 145</Box>
                            <Box>Holograma 2: 54</Box>
                          </VStack>
                        </CardBody>
                      </Card>

                      <Card>
                        <CardHeader>
                          <Heading size="sm">Verificentros Activos</Heading>
                        </CardHeader>
                        <CardBody>
                          <VStack align="start">
                            <Box>CDMX: 45 verificentros</Box>
                            <Box>EDOMEX: 23 verificentros</Box>
                            <Box>Total: 68 verificentros</Box>
                          </VStack>
                        </CardBody>
                      </Card>
                    </SimpleGrid>
                  </VStack>
                </CardBody>
              </Card>
            </TabPanel>
          </TabPanels>
        </Tabs>
      </VStack>
    </Container>
  );
}