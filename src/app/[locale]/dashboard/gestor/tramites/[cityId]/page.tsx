'use client';

import { useEffect, useState } from 'react';
import { useParams } from 'next/navigation';
import { useTramites, Procedure } from '@/Providers/TramitesProvider';
import { RefreshCw, Search } from 'lucide-react';
import ProcedureDetailModal from './ProcedureDetailModal';
import { useToast } from '@chakra-ui/react';

export default function GestorTramitesPage() {
  // Add a key to force remount if needed
  const { cityId } = useParams();
  const { procedures: allProcedures, loading: contextLoading, error: contextError, refreshProcedures } = useTramites();
  const [procedures, setProcedures] = useState<Procedure[]>([]);
  const [filteredProcedures, setFilteredProcedures] = useState<Procedure[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedProcedure, setSelectedProcedure] = useState<Procedure | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const toast = useToast();

  // Function to handle search/filter
  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const term = e.target.value.toLowerCase();
    setSearchTerm(term);

    if (!term.trim()) {
      setFilteredProcedures(procedures);
      return;
    }

    const filtered = procedures.filter(procedure =>
      procedure.tramiteId.name.toLowerCase().includes(term) ||
      procedure.status.toLowerCase().includes(term) ||
      (procedure.notes && procedure.notes.toLowerCase().includes(term)) ||
      (typeof procedure.vehicleId === 'object' && procedure.vehicleId !== null && 'vin' in procedure.vehicleId && typeof (procedure.vehicleId as { vin?: string }).vin === 'string' && (procedure.vehicleId as { vin?: string }).vin!.toLowerCase().includes(term)) ||
      (typeof procedure.vehicleId === 'object' && procedure.vehicleId !== null && 'carPlates' in procedure.vehicleId && (procedure.vehicleId as { carPlates?: { plates?: string } }).carPlates?.plates && (procedure.vehicleId as { carPlates?: { plates?: string } }).carPlates!.plates!.toLowerCase().includes(term))
    );

    setFilteredProcedures(filtered);
  };

  // Function to refresh data
  const handleRefresh = async () => {
    setRefreshing(true);
    await refreshProcedures();
    setRefreshing(false);
  };

  // Function to handle procedure selection
  const handleProcedureSelect = (procedure: Procedure) => {
    try {
      if (!procedure || !procedure._id) {
        console.error('Invalid procedure data:', procedure);
        toast({
          title: 'Error',
          description: 'Datos del trámite inválidos',
          status: 'error',
          duration: 5000,
          isClosable: true,
        });
        return;
      }

      setSelectedProcedure(procedure);
      setIsModalOpen(true);
    } catch (error) {
      console.error('Error selecting procedure:', error);
      toast({
        title: 'Error',
        description: 'No se pudo abrir el detalle del trámite',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    }
  };

  // Function to close the modal
  const handleCloseModal = () => {
    try {
      setIsModalOpen(false);
      // Add a small delay before clearing the selected procedure
      setTimeout(() => {
        setSelectedProcedure(null);
      }, 300); // Increased delay to ensure DOM operations complete
    } catch (error) {
      console.error('Error closing modal:', error);
    }
  };

  // Function to start a procedure
  const handleStartProcedure = async (procedureId: string) => {
    try {

      const procedureToUpdate = procedures.find(p => p._id === procedureId);

      if (!procedureToUpdate) {
        throw new Error('Procedure not found');
      }
      const updatedProcedures = procedures.map(p => {
        if (p._id === procedureId) {
          return {
            ...p,
            status: 'En Proceso',
            startDate: new Date().toISOString()
          };
        }
        return p;
      });
      setProcedures(updatedProcedures);
      setFilteredProcedures(
        searchTerm
          ? updatedProcedures.filter(p =>
              p.tramiteId.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
              p.status.toLowerCase().includes(searchTerm.toLowerCase()) ||
              (p.notes && p.notes.toLowerCase().includes(searchTerm.toLowerCase()))
            )
          : updatedProcedures
      );

      // Also update the selected procedure for the modal
      const updatedProcedure = updatedProcedures.find(p => p._id === procedureId);
      if (updatedProcedure) {
        setSelectedProcedure(updatedProcedure);
      }

      // Show success toast
      toast({
        title: 'Trámite iniciado',
        description: `El trámite "${procedureToUpdate.tramiteId.name}" ha sido iniciado correctamente.`,
        status: 'success',
        duration: 5000,
        isClosable: true,
      });

      // In a real application, you would also update localStorage here
      localStorage.setItem('gestorProcedures', JSON.stringify(
        allProcedures.map(p => p._id === procedureId ? { ...p, status: 'En Proceso' } : p)
      ));

    } catch (error) {
      console.error('Error starting procedure:', error);
      toast({
        title: 'Error',
        description: 'No se pudo iniciar el trámite. Intente nuevamente.',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
      throw error; // Re-throw to be handled by the modal
    }
  };

  // First try to get procedures from context
  useEffect(() => {
    if (contextLoading) {
      return; // Wait for context to load
    }

    if (contextError) {
      setError(contextError);
      setLoading(false);
      return;
    }

    if (allProcedures && allProcedures.length > 0) {
      // Filter procedures by state from tramiteId instead of gestorId.location
      const stateProcedures = allProcedures.filter(
        (procedure) => procedure.tramiteId?.state?.toString() === cityId?.toString()
      );
      setProcedures(stateProcedures);
      setFilteredProcedures(stateProcedures);
      setLoading(false);
      return;
    }

    // If no procedures in context, try localStorage as fallback
    try {
      const storedProcedures = localStorage.getItem('gestorProcedures');
      if (!storedProcedures) {
        setError('No se encontraron procedimientos');
        setLoading(false);
        return;
      }

      try {
        const parsedProcedures = JSON.parse(storedProcedures);
        if (!Array.isArray(parsedProcedures)) {
          setError('El formato de los procedimientos no es un array');
          setLoading(false);
          return;
        }

        // Filter procedures by state from tramiteId instead of gestorId.location
        const stateProcedures = parsedProcedures.filter(
          (procedure: any) => procedure.tramiteId?.state?.toString() === cityId?.toString()
        );
        setProcedures(stateProcedures);
        setFilteredProcedures(stateProcedures);
      } catch (e) {
        setError('Procedimientos corruptos en localStorage');
        console.error('Error al parsear JSON:', e);
      }
    } catch (err) {
      setError('Error al cargar los trámites');
      console.error(err);
    } finally {
      setLoading(false);
    }
  }, [cityId, allProcedures, contextLoading, contextError]);

  if (loading) {
    return (
      <div className="p-4">
        <h1 className="mb-4 text-2xl font-bold">Trámites en {cityId}</h1>
        <div className="w-full p-4 border rounded-lg">
          <div className="animate-pulse">
            <div className="w-3/4 h-4 mb-4 bg-gray-200 rounded"></div>
            <div className="w-full h-4 mb-2 bg-gray-200 rounded"></div>
            <div className="w-full h-4 mb-2 bg-gray-200 rounded"></div>
            <div className="w-full h-4 mb-2 bg-gray-200 rounded"></div>
            <div className="w-5/6 h-4 bg-gray-200 rounded"></div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-4">
        <h1 className="mb-4 text-2xl font-bold">Trámites en {cityId}</h1>
        <div className="p-4 text-red-500 border border-red-200 rounded-lg bg-red-50">
          {error}
        </div>
      </div>
    );
  }

  return (
    <div className="p-4">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-2xl font-bold">Trámites en {cityId}</h1>
        <button
          onClick={handleRefresh}
          className="flex items-center px-3 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          disabled={refreshing}
        >
          <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
          {refreshing ? 'Actualizando...' : 'Actualizar'}
        </button>
      </div>

      {procedures.length === 0 ? (
        <p>No hay trámites disponibles</p>
      ) : (
        <>
          <div className="relative mb-4">
            <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
              <Search className="w-5 h-5 text-gray-400" />
            </div>
            <input
              type="text"
              className="block w-full py-2 pl-10 pr-3 leading-5 placeholder-gray-500 bg-white border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              placeholder="Buscar por nombre, estado o notas..."
              value={searchTerm}
              onChange={handleSearch}
            />
          </div>

          <div className="overflow-x-auto bg-white rounded-lg shadow">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">Trámite</th>
                  <th className="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">Estado</th>
                  <th className="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">Fecha de Creación</th>
                  <th className="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">VIN del Auto</th>
                  <th className="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">Placa</th>
                  <th className="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">Documentos</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredProcedures.length === 0 ? (
                  <tr>
                    <td colSpan={6} className="px-6 py-4 text-sm text-center text-gray-500">
                      No se encontraron resultados para "{searchTerm}"
                    </td>
                  </tr>
                ) : (
                  filteredProcedures.map((procedure) => (
                    <tr
                      key={procedure._id}
                      className="cursor-pointer hover:bg-gray-50"
                      onClick={() => handleProcedureSelect(procedure)}
                    >
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">{procedure.tramiteId.name}</div>
                        <div className="text-sm text-gray-500">{procedure.tramiteId.description}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                          ${procedure.status === 'Completado' ? 'bg-green-100 text-green-800' :
                            procedure.status === 'Pendiente' ? 'bg-yellow-100 text-yellow-800' :
                            procedure.status === 'En Proceso' ? 'bg-blue-100 text-blue-800' :
                            procedure.status === 'Cancelado' ? 'bg-red-100 text-red-800' :
                            'bg-gray-100 text-gray-800'}`}>
                          {procedure.status}
                        </span>
                      </td>
                      <td className="px-6 py-4 text-sm text-gray-500 whitespace-nowrap">
                        {new Date(procedure.createdAt).toLocaleDateString()}
                      </td>
                      <td className="px-6 py-4 text-sm text-gray-500 whitespace-nowrap">
                        {typeof procedure.vehicleId === 'object' && procedure.vehicleId !== null && 'vin' in procedure.vehicleId
                          ? (procedure.vehicleId as { vin?: string }).vin || 'No disponible'
                          : 'No disponible'}
                      </td>
                      <td className="max-w-xs px-6 py-4 text-sm text-gray-500 truncate">
                        {typeof procedure.vehicleId === 'object' && procedure.vehicleId !== null && 'carPlates' in procedure.vehicleId
                          ? (procedure.vehicleId as { carPlates?: { plates?: string } }).carPlates?.plates || 'Sin placa'
                          : 'Sin placa'}
                      </td>
                      <td className="px-6 py-4 text-sm text-gray-500 whitespace-nowrap">
                        {procedure.tramiteId.documents.length > 0 ? (
                          <div className="text-sm text-blue-600">
                            {procedure.tramiteId.documents.length} documento(s)
                          </div>
                        ) : (
                          'Sin documentos'
                        )}
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
          <div className="mt-4 text-sm text-gray-500">
            Mostrando {filteredProcedures.length} de {procedures.length} trámites
          </div>
        </>
      )}

      {/* Procedure Detail Modal */}
      {isModalOpen && selectedProcedure && (
        <ProcedureDetailModal
          procedure={selectedProcedure}
          isOpen={isModalOpen}
          onClose={handleCloseModal}
          onStartProcedure={handleStartProcedure}
          onUpdate={handleRefresh}
        />
      )}
    </div>
  );
}
