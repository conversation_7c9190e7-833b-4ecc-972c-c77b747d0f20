'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Di<PERSON>, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useToast } from '@/components/ui/use-toast';
import {
  AlertTriangle,
  Package,
  CheckCircle,
  Wrench,
  Loader2
} from 'lucide-react';
import { CorrectiveMaintenanceOrder, CorrectiveService, formatCurrency } from '../types';
import { startWork } from '../_actions/startWork';

interface StartWorkConfirmationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  order: CorrectiveMaintenanceOrder;
}

export default function StartWorkConfirmationModal({
  isOpen,
  onClose,
  onSuccess,
  order
}: StartWorkConfirmationModalProps) {
  const { toast } = useToast();
  const [isStarting, setIsStarting] = useState(false);

  // Analyze services and parts
  const servicesWithParts = order.services?.filter(service =>
    service.parts && service.parts.length > 0
  ) || [];

  const unavailableParts = servicesWithParts.flatMap(service =>
    service.parts.filter(part => part.availability === 'unavailable' || part.availability === 'pending')
  );

  const availableParts = servicesWithParts.flatMap(service =>
    service.parts.filter(part => part.availability === 'available')
  );

  const servicesWithoutParts = order.services?.filter(service =>
    !service.parts || service.parts.length === 0
  ) || [];

  const servicesWithAvailableParts = order.services?.filter(service =>
    service.parts && service.parts.length > 0 &&
    service.parts.every(part => part.availability === 'available')
  ) || [];

  const servicesWithUnavailableParts = order.services?.filter(service =>
    service.parts && service.parts.length > 0 &&
    service.parts.some(part => part.availability === 'unavailable' || part.availability === 'pending')
  ) || [];

  const canStartImmediately = servicesWithoutParts.length > 0 || servicesWithAvailableParts.length > 0;
  const hasUnavailableParts = unavailableParts.length > 0;

  const handleStartWork = async (force = false) => {
    setIsStarting(true);
    try {
      const response = await startWork(order._id);

      if (response.success) {
        toast({
          title: 'Éxito',
          description: 'Trabajo iniciado exitosamente. Todas las refacciones están disponibles.',
        });
        onSuccess();
      } else {
        toast({
          title: 'Error',
          description: response.message || 'Error al iniciar el trabajo',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error starting work:', error);
      toast({
        title: 'Error',
        description: 'Error inesperado al iniciar el trabajo',
        variant: 'destructive',
      });
    } finally {
      setIsStarting(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Wrench className="w-5 h-5" />
            Confirmar Inicio de Trabajo
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          {/* Error if there are unavailable parts */}
          {hasUnavailableParts && (
            <Alert className="border-red-200 bg-red-50">
              <AlertTriangle className="w-4 h-4 text-red-600" />
              <AlertDescription className="text-red-800">
                <strong>Error:</strong> No se puede iniciar el trabajo. Algunas refacciones no están disponibles.
                Todas las refacciones deben estar disponibles antes de poder iniciar el trabajo.
              </AlertDescription>
            </Alert>
          )}

          {/* Services that can start immediately */}
          {canStartImmediately && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-green-700">
                  <CheckCircle className="w-5 h-5" />
                  Servicios que pueden iniciarse ({servicesWithoutParts.length + servicesWithAvailableParts.length})
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                {servicesWithoutParts.map(service => (
                  <div key={service._id} className="flex items-center justify-between p-2 rounded bg-green-50">
                    <div>
                      <p className="font-medium">{service.serviceName}</p>
                      <p className="text-sm text-gray-600">Sin refacciones requeridas</p>
                    </div>
                    <Badge className="text-green-800 bg-green-100">
                      Listo
                    </Badge>
                  </div>
                ))}
                {servicesWithAvailableParts.map(service => (
                  <div key={service._id} className="flex items-center justify-between p-2 rounded bg-green-50">
                    <div>
                      <p className="font-medium">{service.serviceName}</p>
                      <p className="text-sm text-gray-600">
                        {service.parts.length} refacción(es) disponible(s)
                      </p>
                    </div>
                    <Badge className="text-green-800 bg-green-100">
                      Listo
                    </Badge>
                  </div>
                ))}
              </CardContent>
            </Card>
          )}

          {/* Services with unavailable parts */}
          {servicesWithUnavailableParts.length > 0 && (
            <Card className="border-red-200">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-red-700">
                  <AlertTriangle className="w-5 h-5" />
                  Servicios con Refacciones No Disponibles ({servicesWithUnavailableParts.length})
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                {servicesWithUnavailableParts.map(service => {
                  const unavailablePartsForService = service.parts?.filter(part =>
                    part.availability === 'unavailable' || part.availability === 'pending'
                  ) || [];
                  return (
                    <div key={service._id} className="p-2 border border-red-200 rounded bg-red-50">
                      <div className="flex items-center justify-between mb-2">
                        <div>
                          <p className="font-medium text-red-800">{service.serviceName}</p>
                          <p className="text-sm text-red-600">
                            {unavailablePartsForService.length} refacción(es) no disponible(s)
                          </p>
                        </div>
                        <Badge className="text-red-800 bg-red-100">
                          No Disponible
                        </Badge>
                      </div>
                      <div className="space-y-1">
                        {unavailablePartsForService.map((part, index) => (
                          <div key={index} className="flex items-center justify-between text-sm">
                            <span className="text-red-700">{part.name} (x{part.quantity})</span>
                            <span className="text-red-500">
                              {part.availability === 'pending' ? 'Pendiente de verificar' : 'No disponible'}
                            </span>
                          </div>
                        ))}
                      </div>
                    </div>
                  );
                })}
              </CardContent>
            </Card>
          )}

          {/* Summary */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Package className="w-5 h-5" />
                Resumen
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <p className="text-gray-500">Total de servicios:</p>
                  <p className="font-medium">{order.services?.length || 0}</p>
                </div>
                <div>
                  <p className="text-gray-500">Pueden iniciarse:</p>
                  <p className="font-medium text-green-600">
                    {servicesWithoutParts.length + servicesWithAvailableParts.length}
                  </p>
                </div>
                <div>
                  <p className="text-gray-500">Servicios con refacciones no disponibles:</p>
                  <p className="font-medium text-red-600">{servicesWithUnavailableParts.length}</p>
                </div>
                <div>
                  <p className="text-gray-500">Refacciones no disponibles:</p>
                  <p className="font-medium text-red-600">{unavailableParts.length}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Action explanation */}
          <div className="p-4 border border-blue-200 rounded-lg bg-blue-50">
            <h4 className="mb-2 font-medium text-blue-800">¿Qué sucederá al iniciar el trabajo?</h4>
            <ul className="space-y-1 text-sm text-blue-700">
              {canStartImmediately && !hasUnavailableParts && (
                <li>• Los servicios sin refacciones o con refacciones disponibles comenzarán inmediatamente</li>
              )}
              {hasUnavailableParts && (
                <li>• <strong>No se puede iniciar el trabajo</strong> hasta que todas las refacciones estén disponibles</li>
              )}
              <li>• Debes gestionar las refacciones no disponibles antes de poder iniciar el trabajo</li>
              <li>• Recibirás notificaciones cuando lleguen las refacciones</li>
            </ul>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Cancelar
          </Button>
          <Button
            onClick={() => handleStartWork()}
            disabled={isStarting || hasUnavailableParts}
            className="min-w-[120px]"
          >
            {isStarting && <Loader2 className="w-4 h-4 mr-2 animate-spin" />}
            {hasUnavailableParts
              ? 'Refacciones No Disponibles'
              : isStarting
                ? 'Iniciando...'
                : 'Iniciar Trabajo'
            }
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
