'use client';

import { useState, useEffect, Suspense } from 'react';
import {
  Box,
  Container,
  VStack,
  HStack,
  Text,
  Input,
  Select,
  Button,
  Card,
  CardHeader,
  CardBody,
  Heading,
  FormControl,
  FormLabel,
  FormErrorMessage,
  Alert,
  AlertIcon,
  Stepper,
  Step,
  StepIndicator,
  StepStatus,
  StepIcon,
  StepNumber,
  StepTitle,
  StepDescription,
  StepSeparator,
  useToast,
  Badge,
  Divider,
  SimpleGrid,
  useColorModeValue,
  Spinner,
  Checkbox,
  Textarea,
  Progress,
  Center,
} from '@chakra-ui/react';
import { FiCheck, FiUpload, FiSend, FiCalendar, FiShield } from 'react-icons/fi';
import { useQuery, useMutation } from '@tanstack/react-query';
import { FileUploader } from '@/components/verification/FileUploader';
import { 
  getCustomerVerificationData,
  updateCustomerVerification,
  getHologramOptions,
  getCompletionStatus
} from '@/actions/verification';

interface CustomerVerificationPageProps {
  uniqueLink: string;
}

interface HologramOption {
  type: string;
  name: string;
  description: string;
  color: string;
  exemptionPeriod?: string;
}

export function CustomerVerificationPage({ uniqueLink }: CustomerVerificationPageProps) {
  const [currentStep, setCurrentStep] = useState(0);
  const [verificationCertificateFile, setVerificationCertificateFile] = useState<File | null>(null);
  const [hologramType, setHologramType] = useState('');
  const [hologramPhotoFile, setHologramPhotoFile] = useState<File | null>(null);
  const [isExempt, setIsExempt] = useState(false);
  const [customerNotes, setCustomerNotes] = useState('');
  const [errors, setErrors] = useState<Record<string, string>>({});

  const bgColor = useColorModeValue('white', 'gray.800');
  const toast = useToast();

  // Fetch verification data
  const { data: verificationData, isLoading: isLoadingVerification, error: verificationError } = useQuery({
    queryKey: ['customer-verification', uniqueLink],
    queryFn: () => getCustomerVerificationData(uniqueLink),
    retry: 1,
  });

  // Fetch hologram options
  const { data: hologramOptions } = useQuery({
    queryKey: ['hologram-options'],
    queryFn: getHologramOptions,
  });

  // Fetch completion status
  const { data: completionStatus } = useQuery({
    queryKey: ['completion-status', uniqueLink],
    queryFn: () => getCompletionStatus(uniqueLink),
    enabled: !!verificationData,
  });

  // Update verification mutation
  const updateVerificationMutation = useMutation({
    mutationFn: (data: any) => updateCustomerVerification(uniqueLink, data),
    onSuccess: () => {
      toast({
        title: 'Verificación completada exitosamente',
        description: 'Gracias por completar tu verificación de emisiones',
        status: 'success',
        duration: 5000,
        isClosable: true,
      });
      
      // Reset to show success
      setCurrentStep(3);
    },
    onError: (error: any) => {
      toast({
        title: 'Error al completar verificación',
        description: error.message,
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    },
  });

  const steps = [
    {
      title: 'Información',
      description: 'Revisar datos de verificación'
    },
    {
      title: 'Certificado',
      description: 'Subir certificado de verificación'
    },
    {
      title: 'Holograma',
      description: 'Tipo y foto del holograma'
    },
    {
      title: 'Completado',
      description: 'Verificación finalizada'
    }
  ];

  // Auto-advance based on completion status
  useEffect(() => {
    if (completionStatus && !completionStatus.isComplete) {
      if (completionStatus.missingFields.certificate) {
        setCurrentStep(1);
      } else if (completionStatus.missingFields.hologramPhoto || completionStatus.missingFields.hologramType) {
        setCurrentStep(2);
      }
    } else if (completionStatus?.isComplete) {
      setCurrentStep(3);
    }
  }, [completionStatus]);

  const handleCertificateUpload = () => {
    if (!verificationCertificateFile) {
      setErrors({ certificate: 'El certificado de verificación es requerido' });
      return;
    }
    
    setErrors({});
    setCurrentStep(2);
  };

  const handleHologramComplete = () => {
    if (!hologramType) {
      setErrors({ hologramType: 'Por favor selecciona el tipo de holograma' });
      return;
    }
    if (!isExempt && !hologramPhotoFile) {
      setErrors({ hologramPhoto: 'La foto del holograma es requerida' });
      return;
    }
    
    setErrors({});
    handleSubmit();
  };

  const handleSubmit = async () => {
    // Mock URLs for files - in real implementation, upload files first
    const verificationCertificateUrl = verificationCertificateFile ? `https://cdn.onecar.com/uploads/${verificationCertificateFile.name}` : '';
    const hologramPhotoUrl = hologramPhotoFile ? `https://cdn.onecar.com/uploads/${hologramPhotoFile.name}` : '';

    const updateData = {
      verificationCertificate: verificationCertificateUrl,
      hologramType: hologramType as '00' | '0' | '1' | '2',
      hologramPhoto: hologramPhotoUrl,
      isExempt,
      customerNotes: customerNotes || undefined,
    };

    updateVerificationMutation.mutate(updateData);
  };


  const getHologramInfo = (type: string) => {
    return hologramOptions?.find((option: HologramOption) => option.type === type);
  };

  const calculateProgress = () => {
    if (!completionStatus) return 0;
    
    let completed = 0;
    let total = 3;
    
    if (!completionStatus.missingFields.certificate) completed++;
    if (!completionStatus.missingFields.hologramType) completed++;
    if (!completionStatus.missingFields.hologramPhoto) completed++;
    
    return (completed / total) * 100;
  };

  if (isLoadingVerification) {
    return (
      <Container maxW="4xl" py={8}>
        <Center minH="400px">
          <VStack spacing={4}>
            <Spinner size="xl" color="orange.500" />
            <Text>Cargando información de verificación...</Text>
          </VStack>
        </Center>
      </Container>
    );
  }

  if (verificationError || !verificationData) {
    return (
      <Container maxW="4xl" py={8}>
        <Alert status="error">
          <AlertIcon />
          <VStack align="start" spacing={2}>
            <Text fontWeight="bold">Error al cargar verificación</Text>
            <Text>
              El enlace de verificación no es válido o ha expirado. 
              Por favor contacta al centro de verificación.
            </Text>
          </VStack>
        </Alert>
      </Container>
    );
  }

  return (
    <Container maxW="4xl" py={8}>
      <VStack spacing={8}>
        {/* Header */}
        <Card bg={bgColor} w="full">
          <CardHeader textAlign="center">
            <VStack spacing={2}>
              <FiShield size="48" color="orange" />
              <Heading size="lg">Completar Verificación de Emisiones</Heading>
              <Text color="gray.600">Placa: {verificationData.vehiclePlate}</Text>
              <Text fontSize="sm" color="gray.500">
                Centro: {verificationData.verificationCenter?.name}
              </Text>
            </VStack>
          </CardHeader>
        </Card>

        {/* Progress */}
        {completionStatus && (
          <Card bg={bgColor} w="full">
            <CardBody>
              <VStack spacing={4}>
                <HStack justify="space-between" w="full">
                  <Text fontWeight="bold">Progreso de Verificación</Text>
                  <Text fontSize="sm" color="gray.600">
                    {Math.round(calculateProgress())}% Completado
                  </Text>
                </HStack>
                <Progress
                  value={calculateProgress()}
                  colorScheme="orange"
                  size="lg"
                  w="full"
                  hasStripe
                  isAnimated
                />
              </VStack>
            </CardBody>
          </Card>
        )}

        {/* Stepper */}
        <Stepper index={currentStep} colorScheme="orange" w="full">
          {steps.map((step, index) => (
            <Step key={index}>
              <StepIndicator>
                <StepStatus
                  complete={<StepIcon />}
                  incomplete={<StepNumber />}
                  active={<StepNumber />}
                />
              </StepIndicator>

              <Box flexShrink="0">
                <StepTitle>{step.title}</StepTitle>
                <StepDescription>{step.description}</StepDescription>
              </Box>

              <StepSeparator />
            </Step>
          ))}
        </Stepper>

        {/* Step Content */}
        <Card bg={bgColor} w="full" minH="400px">
          <CardBody>
            {/* Step 0: Information */}
            {currentStep === 0 && (
              <VStack spacing={6}>
                <Box textAlign="center">
                  <Heading size="md" mb={4}>Información de Verificación</Heading>
                  <Text color="gray.600" mb={6}>
                    Revisa los datos de tu verificación antes de continuar
                  </Text>
                </Box>

                <SimpleGrid columns={[1, 2]} spacing={6} w="full">
                  <Box>
                    <Text fontWeight="bold" mb={3}>Datos del Vehículo</Text>
                    <VStack align="start" spacing={2}>
                      <Text>Placa: {verificationData.vehiclePlate}</Text>
                      <Text>
                        Fecha de Verificación: {' '}
                        {new Date(verificationData.verificationDate).toLocaleDateString()}
                      </Text>
                      <Text>
                        Próxima Verificación: {' '}
                        {new Date(verificationData.nextVerificationDate).toLocaleDateString()}
                      </Text>
                    </VStack>
                  </Box>

                  <Box>
                    <Text fontWeight="bold" mb={3}>Centro de Verificación</Text>
                    <VStack align="start" spacing={2}>
                      <Text>{verificationData.verificationCenter?.name}</Text>
                      <Text>Código: {verificationData.verificationCenter?.code}</Text>
                      <Text>{verificationData.verificationCenter?.location?.address}</Text>
                    </VStack>
                  </Box>
                </SimpleGrid>

                <Divider />

                <Box w="full">
                  <Text fontWeight="bold" mb={3}>Documentos del Verificentro</Text>
                  <SimpleGrid columns={[1, 2]} spacing={4}>
                    {verificationData.vendorEvidence?.vehiclePhoto && (
                      <Alert status="success">
                        <AlertIcon />
                        <Text fontSize="sm">Foto del vehículo recibida</Text>
                      </Alert>
                    )}
                    {verificationData.vendorEvidence?.circulationCard && (
                      <Alert status="success">
                        <AlertIcon />
                        <Text fontSize="sm">Tarjeta de circulación recibida</Text>
                      </Alert>
                    )}
                  </SimpleGrid>
                </Box>

                <Button
                  colorScheme="orange"
                  size="lg"
                  onClick={() => setCurrentStep(1)}
                  leftIcon={<FiCheck />}
                >
                  Continuar con la Verificación
                </Button>
              </VStack>
            )}

            {/* Step 1: Certificate Upload */}
            {currentStep === 1 && (
              <VStack spacing={6}>
                <Box textAlign="center">
                  <Heading size="md" mb={2}>Subir Certificado de Verificación</Heading>
                  <Text color="gray.600">
                    Sube el certificado que recibiste del centro de verificación
                  </Text>
                </Box>

                <VStack spacing={3} w="full" maxW="500px">
                  <Text fontWeight="bold">📄 Certificado de Verificación</Text>
                  <Text fontSize="sm" color="gray.600" textAlign="center">
                    Documento oficial emitido por el centro de verificación
                  </Text>
                  <FileUploader
                    accept="image/*,.pdf"
                    maxFileSize={10 * 1024 * 1024} // 10MB
                    onFileSelect={setVerificationCertificateFile}
                    selectedFile={verificationCertificateFile}
                    error={errors.certificate}
                  />
                </VStack>

                {errors.certificate && (
                  <Alert status="error" maxW="500px">
                    <AlertIcon />
                    <Text fontSize="sm">{errors.certificate}</Text>
                  </Alert>
                )}

                <Button
                  colorScheme="orange"
                  size="lg"
                  onClick={handleCertificateUpload}
                  leftIcon={<FiUpload />}
                  isDisabled={!verificationCertificateFile}
                >
                  Continuar
                </Button>
              </VStack>
            )}

            {/* Step 2: Hologram */}
            {currentStep === 2 && (
              <VStack spacing={6}>
                <Box textAlign="center">
                  <Heading size="md" mb={2}>Información del Holograma</Heading>
                  <Text color="gray.600">
                    Selecciona el tipo de holograma y sube una foto clara
                  </Text>
                </Box>

                <FormControl isInvalid={!!errors.hologramType} maxW="400px">
                  <FormLabel>Tipo de Holograma Obtenido</FormLabel>
                  <Select
                    value={hologramType}
                    onChange={(e) => {
                      setHologramType(e.target.value);
                      setIsExempt(e.target.value === '00');
                    }}
                    placeholder="Selecciona el tipo de holograma"
                  >
                    {hologramOptions?.map((option: HologramOption) => (
                      <option key={option.type} value={option.type}>
                        {option.type} - {option.name} ({option.color})
                      </option>
                    ))}
                  </Select>
                  <FormErrorMessage>{errors.hologramType}</FormErrorMessage>
                </FormControl>

                {hologramType && (
                  <Alert status="info" maxW="500px">
                    <AlertIcon />
                    <VStack align="start" spacing={1}>
                      <Text fontWeight="bold">
                        {getHologramInfo(hologramType)?.name}
                      </Text>
                      <Text fontSize="sm">
                        {getHologramInfo(hologramType)?.description}
                      </Text>
                      {getHologramInfo(hologramType)?.exemptionPeriod && (
                        <Text fontSize="sm" color="blue.600">
                          Periodo de exención: {getHologramInfo(hologramType)?.exemptionPeriod}
                        </Text>
                      )}
                    </VStack>
                  </Alert>
                )}

                <Checkbox
                  isChecked={isExempt}
                  onChange={(e) => setIsExempt(e.target.checked)}
                  colorScheme="orange"
                >
                  Mi vehículo obtuvo un holograma de exención (00)
                </Checkbox>

                {!isExempt && (
                  <VStack spacing={3} w="full" maxW="500px">
                    <Text fontWeight="bold">📸 Foto del Holograma</Text>
                    <Text fontSize="sm" color="gray.600" textAlign="center">
                      Foto clara y visible del holograma en el parabrisas
                    </Text>
                    <FileUploader
                      accept="image/*"
                      maxFileSize={10 * 1024 * 1024} // 10MB
                      onFileSelect={setHologramPhotoFile}
                      selectedFile={hologramPhotoFile}
                      error={errors.hologramPhoto}
                    />
                  </VStack>
                )}

                <FormControl maxW="500px">
                  <FormLabel>Notas Adicionales (Opcional)</FormLabel>
                  <Textarea
                    value={customerNotes}
                    onChange={(e) => setCustomerNotes(e.target.value)}
                    placeholder="Agrega cualquier comentario sobre tu verificación..."
                    rows={3}
                  />
                </FormControl>

                {(errors.hologramType || errors.hologramPhoto) && (
                  <Alert status="error" maxW="500px">
                    <AlertIcon />
                    <VStack align="start" spacing={1}>
                      {errors.hologramType && <Text fontSize="sm">{errors.hologramType}</Text>}
                      {errors.hologramPhoto && <Text fontSize="sm">{errors.hologramPhoto}</Text>}
                    </VStack>
                  </Alert>
                )}

                <Button
                  colorScheme="orange"
                  size="lg"
                  onClick={handleHologramComplete}
                  leftIcon={<FiSend />}
                  isLoading={updateVerificationMutation.isPending}
                  loadingText="Completando..."
                >
                  Completar Verificación
                </Button>
              </VStack>
            )}

            {/* Step 3: Completed */}
            {currentStep === 3 && (
              <VStack spacing={6}>
                <Box textAlign="center">
                  <FiCheck size="64" color="green" />
                  <Heading size="lg" mt={4} color="green.600">
                    ¡Verificación Completada!
                  </Heading>
                  <Text color="gray.600" mt={2}>
                    Gracias por completar tu verificación de emisiones
                  </Text>
                </Box>

                <Alert status="success" maxW="500px">
                  <AlertIcon />
                  <VStack align="start" spacing={2}>
                    <Text fontWeight="bold">Tu verificación ha sido procesada exitosamente</Text>
                    <Text fontSize="sm">
                      Próxima verificación: {' '}
                      {new Date(verificationData.nextVerificationDate).toLocaleDateString()}
                    </Text>
                    <Text fontSize="sm">
                      Recibirás un recordatorio antes de la fecha de vencimiento.
                    </Text>
                  </VStack>
                </Alert>

                <Card maxW="500px">
                  <CardBody>
                    <VStack spacing={3}>
                      <Text fontWeight="bold">Resumen de tu Verificación</Text>
                      <Divider />
                      <HStack justify="space-between" w="full">
                        <Text>Placa:</Text>
                        <Text fontWeight="bold">{verificationData.vehiclePlate}</Text>
                      </HStack>
                      <HStack justify="space-between" w="full">
                        <Text>Centro:</Text>
                        <Text>{verificationData.verificationCenter?.name}</Text>
                      </HStack>
                      <HStack justify="space-between" w="full">
                        <Text>Holograma:</Text>
                        <Badge colorScheme="green">
                          {hologramType} - {getHologramInfo(hologramType)?.name}
                        </Badge>
                      </HStack>
                      <HStack justify="space-between" w="full">
                        <Text>Estado:</Text>
                        <Badge colorScheme="green">Completada</Badge>
                      </HStack>
                    </VStack>
                  </CardBody>
                </Card>
              </VStack>
            )}
          </CardBody>
        </Card>

        {/* Navigation */}
        {currentStep > 0 && currentStep < 3 && (
          <HStack>
            <Button
              variant="outline"
              onClick={() => setCurrentStep(currentStep - 1)}
            >
              Anterior
            </Button>
          </HStack>
        )}
      </VStack>
    </Container>
  );
}