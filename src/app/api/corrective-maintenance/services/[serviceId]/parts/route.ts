import { NextRequest, NextResponse } from 'next/server';
import { URL_API } from '@/constants';
import getCurrentUser from '@/actions/getCurrentUser';

export async function PUT(
  request: NextRequest,
  { params }: { params: { serviceId: string } }
) {
  try {
    const user = await getCurrentUser();

    if (!user) {
      return NextResponse.json(
        { success: false, message: 'Usuario no autenticado' },
        { status: 401 }
      );
    }

    const { serviceId } = params;
    const body = await request.json();

    // Forward the request to the backend API
    const response = await fetch(
      `${URL_API}/vendor-platform/corrective-maintenance/services/${serviceId}/parts`,
      {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${user.accessToken}`,
        },
        body: JSON.stringify(body),
      }
    );

    const responseData = await response.json();
    if (response.ok) {
      return NextResponse.json({
        success: true,
        data: responseData.data || responseData,
        message: responseData.message || 'Refacciones actualizadas exitosamente',
      });
    } else {
      return NextResponse.json(
        {
          success: false,
          message: responseData.message || 'Error al actualizar las refacciones',
        },
        { status: response.status }
      );
    }
  } catch (error: any) {
    console.error('❌ API Route - Error updating service parts:', error);

    return NextResponse.json(
      {
        success: false,
        message: error.message || 'Error inesperado al actualizar las refacciones',
      },
      { status: 500 }
    );
  }
}