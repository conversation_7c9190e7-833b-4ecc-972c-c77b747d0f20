import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/authOptions';

// This route is dynamic and should not be pre-rendered
export const dynamic = 'force-dynamic';

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const user = session.user as any;
    console.log('🔍 API Route Dashboard - Usuario:', {
      id: user._id,
      email: user.email,
      userType: user.userType,
      hasToken: !!user.accessToken
    });

    // Verificar permisos - permitir más tipos de usuario temporalmente para debugging
    if (!['superAdmin', 'company-gestor', 'workshop', 'gestor'].includes(user.userType)) {
      console.log('❌ Usuario no autorizado para dashboard:', { userType: user.userType });
      return NextResponse.json({ error: 'User not authorized for admin operations' }, { status: 403 });
    }

    // Llamar al backend real usando el endpoint de vendor (no requiere admin platform)
    const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL;
    const url = new URL('/vendor-platform/emissions-verification/vendor/dashboard', API_BASE_URL);

    console.log('📡 API Route Dashboard - Llamando al backend:', url.toString());

    const response = await fetch(url.toString(), {
      headers: {
        'Authorization': `Bearer ${user.accessToken}`,
        'Content-Type': 'application/json',
        // No incluir 'adpt': 'true' porque es endpoint de vendor
      },
    });

    console.log('📡 API Route Dashboard - Respuesta del backend:', {
      status: response.status,
      statusText: response.statusText,
      ok: response.ok
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ API Route Dashboard - Error del backend:', errorText);
      return NextResponse.json({ error: 'Failed to fetch dashboard data' }, { status: response.status });
    }

    const result = await response.json();
    console.log('✅ API Route Dashboard - Datos obtenidos:', result);
    return NextResponse.json(result.data);
  } catch (error) {
    console.error('❌ API Route Dashboard - Error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
