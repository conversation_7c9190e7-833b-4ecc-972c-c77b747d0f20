import { NextRequest, NextResponse } from 'next/server';
import { createVerificationRecord } from '@/actions/verification';

export async function POST(request: NextRequest) {
  try {
    console.log('📝 Creating verification - Starting...');
    
    const body = await request.json();
    console.log('📝 Request body:', body);
    
    const { vehiclePlate, verificationCenterId, verificationDate, vehiclePhoto, circulationCard } = body;

    if (!vehiclePlate || !verificationCenterId || !verificationDate) {
      console.log('❌ Missing required fields:', { vehiclePlate, verificationCenterId, verificationDate });
      return NextResponse.json(
        { error: 'vehiclePlate, verificationCenterId, and verificationDate are required' },
        { status: 400 }
      );
    }

    console.log('✅ All required fields present, calling createVerificationRecord...');
    
    const result = await createVerificationRecord({
      vehiclePlate,
      verificationCenterId,
      verificationDate,
      vehiclePhoto,
      circulationCard,
    });
    
    console.log('✅ Verification created successfully:', result);
    return NextResponse.json({ success: true, data: result });
  } catch (error: any) {
    console.error('❌ Error in create verification API:', error);
    console.error('❌ Error stack:', error.stack);
    return NextResponse.json(
      { error: error.message || 'Internal server error' },
      { status: 500 }
    );
  }
}