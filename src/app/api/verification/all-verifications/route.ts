import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/authOptions';

// This route is dynamic and should not be pre-rendered
export const dynamic = 'force-dynamic';

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const user = session.user as any;
    console.log('🔍 API Route All Verifications - Usuario:', {
      id: user._id,
      email: user.email,
      userType: user.userType,
      hasToken: !!user.accessToken
    });

    // Verificar permisos - permitir más tipos de usuario temporalmente para debugging
    if (!['superAdmin', 'company-gestor', 'workshop', 'gestor'].includes(user.userType)) {
      console.log('❌ Usuario no autorizado para all-verifications:', { userType: user.userType });
      return NextResponse.json({ error: 'User not authorized for admin operations' }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const page = searchParams.get('page');
    const limit = searchParams.get('limit');
    const status = searchParams.get('status');
    const search = searchParams.get('search');

    // Llamar al backend real
    const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL;
    const url = new URL('/vendor-platform/emissions-verification/admin/verifications', API_BASE_URL);

    // Agregar parámetros de query
    if (page) url.searchParams.set('page', page);
    if (limit) url.searchParams.set('limit', limit);
    if (status) url.searchParams.set('status', status);
    if (search) url.searchParams.set('search', search);

    console.log('📡 API Route All Verifications - Llamando al backend:', url.toString());

    const response = await fetch(url.toString(), {
      headers: {
        'Authorization': `Bearer ${user.accessToken}`,
        'Content-Type': 'application/json',
        'adpt': 'true', // Header para indicar que es admin platform
      },
    });

    console.log('📡 API Route All Verifications - Respuesta del backend:', {
      status: response.status,
      statusText: response.statusText,
      ok: response.ok
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ API Route All Verifications - Error del backend:', errorText);
      return NextResponse.json({ error: 'Failed to fetch verifications' }, { status: response.status });
    }

    const result = await response.json();
    console.log('✅ API Route All Verifications - Datos obtenidos:', result);
    return NextResponse.json(result.data);
  } catch (error) {
    console.error('❌ API Route All Verifications - Error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
