import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/authOptions';
import { VerificationCenter } from '@/types';

// This route is dynamic and should not be pre-rendered
export const dynamic = 'force-dynamic';

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const user = session.user as any;
    console.log('🔍 API Route - Usuario:', {
      id: user._id,
      email: user.email,
      userType: user.userType,
      hasToken: !!user.accessToken
    });

    // Verificar permisos - Incluir usuarios "eco"
    if (!['superAdmin', 'workshop', 'gestor', 'company-gestor', 'eco'].includes(user.userType)) {
      return NextResponse.json({ error: 'User not authorized for verification operations' }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const state = searchParams.get('state');

    // Llamar al backend real
    const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL;
    const url = new URL('/vendor-platform/emissions-verification/vendor/verification-centers', API_BASE_URL);
    if (state) url.searchParams.set('state', state);

    console.log('📡 API Route - Llamando al backend:', url.toString());

    const response = await fetch(url.toString(), {
      headers: {
        'Authorization': `Bearer ${user.accessToken}`,
        'Content-Type': 'application/json',
      },
    });

    console.log('📡 API Route - Respuesta del backend:', {
      status: response.status,
      statusText: response.statusText,
      ok: response.ok
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ API Route - Error del backend:', errorText);
      return NextResponse.json({ error: 'Failed to fetch verification centers' }, { status: response.status });
    }

    const result = await response.json();
    console.log('✅ API Route - Centros obtenidos:', result);
    return NextResponse.json(result.data);
  } catch (error) {
    console.error('❌ API Route - Error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session || (session.user as any).userType !== 'superAdmin') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { name, code, location, state, organizationId, authorizedFor } = body;

    if (!name || !code || !location || !state || !organizationId) {
      return NextResponse.json({
        error: 'name, code, location, state, and organizationId are required'
      }, { status: 400 });
    }

    // TODO: Implement API call to backend to create verification center
    // This is a placeholder for the actual API implementation
    const mockCenter: VerificationCenter = {
      _id: 'new-center-id',
      name,
      code,
      location,
      state,
      organizationId,
      authorizedFor: authorizedFor || ['vehicles'],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    return NextResponse.json(mockCenter, { status: 201 });
  } catch (error) {
    console.error('Error creating verification center:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}