import { NextRequest, NextResponse } from 'next/server';
import { CustomerVerificationAccess } from '@/types';

export async function GET(request: NextRequest, { params }: { params: { token: string } }) {
  try {
    const { token } = params;

    if (!token) {
      return NextResponse.json({ error: 'Token is required' }, { status: 400 });
    }

    // TODO: Implement API call to backend to validate token and get verification data
    // This is a placeholder for the actual API implementation
    const mockAccess: CustomerVerificationAccess = {
      _id: 'access-id',
      verificationId: 'verification-id',
      uniqueToken: token,
      expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days from now
      accessedAt: new Date().toISOString(),
      status: 'active',
    };

    const mockVerificationData = {
      _id: 'verification-id',
      vehicleId: 'vehicle-id',
      plateNumber: 'ABC123',
      verificationCenterId: 'center-1',
      verificationDate: new Date().toISOString(),
      status: 'pending',
      documents: {
        vehiclePhoto: null,
        circulationCard: null,
        verificationCertificate: null,
        hologramPhoto: null,
      },
      access: mockAccess,
    };

    return NextResponse.json(mockVerificationData);
  } catch (error) {
    console.error('Error validating customer token:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function PUT(request: NextRequest, { params }: { params: { token: string } }) {
  try {
    const { token } = params;
    const body = await request.json();

    if (!token) {
      return NextResponse.json({ error: 'Token is required' }, { status: 400 });
    }

    const { documents, hologramType, nextVerificationDate } = body;

    // TODO: Implement API call to backend to update verification with customer data
    // This is a placeholder for the actual API implementation
    const mockUpdatedVerification = {
      _id: 'verification-id',
      vehicleId: 'vehicle-id',
      plateNumber: 'ABC123',
      verificationCenterId: 'center-1',
      verificationDate: new Date().toISOString(),
      nextVerificationDate: nextVerificationDate || null,
      hologramType: hologramType || '1',
      status: 'completed',
      documents,
      customerConfirmation: {
        confirmedAt: new Date().toISOString(),
        hologramUploaded: !!documents.hologramPhoto,
        nextVerificationDateProvided: !!nextVerificationDate,
      },
      updatedAt: new Date().toISOString(),
    };

    return NextResponse.json(mockUpdatedVerification);
  } catch (error) {
    console.error('Error updating customer verification:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}