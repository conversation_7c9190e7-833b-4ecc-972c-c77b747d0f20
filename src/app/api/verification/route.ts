import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/authOptions';

// This route is dynamic and should not be pre-rendered
export const dynamic = 'force-dynamic';

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const vehicleId = searchParams.get('vehicleId');
    const plateNumber = searchParams.get('plateNumber');

    if (!vehicleId && !plateNumber) {
      return NextResponse.json({ error: 'vehicleId or plateNumber required' }, { status: 400 });
    }

    // TODO: Implement API call to backend to get verification data
    // This is a placeholder for the actual API implementation
    const mockVerificationData = {
      vehicleId: vehicleId || 'mock-vehicle-id',
      plateNumber: plateNumber || 'ABC123',
      verifications: [],
      currentStatus: 'pending',
      nextVerificationDue: null,
    };

    return NextResponse.json(mockVerificationData);
  } catch (error) {
    console.error('Error fetching verification data:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { vehicleId, plateNumber, verificationCenterId, documents } = body;

    if (!vehicleId || !plateNumber || !verificationCenterId) {
      return NextResponse.json({ 
        error: 'vehicleId, plateNumber, and verificationCenterId are required' 
      }, { status: 400 });
    }

    // TODO: Implement API call to backend to create verification record
    // This is a placeholder for the actual API implementation
    const mockVerificationRecord = {
      _id: 'mock-verification-id',
      vehicleId,
      plateNumber,
      verificationCenterId,
      vendorUserId: session.user._id,
      verificationDate: new Date().toISOString(),
      status: 'pending',
      documents,
      uniqueLink: `${process.env.NEXTAUTH_URL}/verification/customer/${generateToken()}`,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    return NextResponse.json(mockVerificationRecord, { status: 201 });
  } catch (error) {
    console.error('Error creating verification record:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

function generateToken(): string {
  return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
}